<?php $__env->startSection('title', 'Q<PERSON>ả<PERSON> lý sản phẩm - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý sản phẩm'); ?>

<?php $__env->startSection('page-actions'); ?>
<div class="btn-group" role="group">
    <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Thêm sản phẩm
    </a>
    <a href="<?php echo e(route('import.index')); ?>" class="btn btn-success">
        <i class="fas fa-file-import me-1"></i>
        Import CSV
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Danh sách sản phẩm</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="<?php echo e(route('products.index')); ?>" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control form-control-sm" name="search" 
                               placeholder="Tìm kiếm..." value="<?php echo e(request('search')); ?>">
                    </div>
                    <div class="col-auto">
                        <select name="category_id" class="form-select form-select-sm">
                            <option value="">Tất cả danh mục</option>
                            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                                    <?php echo e($category->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select name="supplier_id" class="form-select form-select-sm">
                            <option value="">Tất cả nhà cung cấp</option>
                            <?php $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($supplier->id); ?>" <?php echo e(request('supplier_id') == $supplier->id ? 'selected' : ''); ?>>
                                    <?php echo e($supplier->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="col-auto">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                            <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                            <option value="out_of_stock" <?php echo e(request('status') == 'out_of_stock' ? 'selected' : ''); ?>>Hết hàng</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="<?php echo e(route('products.index')); ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="text-decoration-none text-dark">
                                Tên sản phẩm
                                <?php if(request('sort_by') == 'name'): ?>
                                    <i class="fas fa-sort-<?php echo e(request('sort_order') == 'asc' ? 'up' : 'down'); ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>SKU</th>
                        <th>Danh mục</th>
                        <th>Nhà cung cấp</th>
                        <th>
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'cost_price', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="text-decoration-none text-dark">
                                Giá gốc
                                <?php if(request('sort_by') == 'cost_price'): ?>
                                    <i class="fas fa-sort-<?php echo e(request('sort_order') == 'asc' ? 'up' : 'down'); ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'selling_price', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="text-decoration-none text-dark">
                                Giá bán
                                <?php if(request('sort_by') == 'selling_price'): ?>
                                    <i class="fas fa-sort-<?php echo e(request('sort_order') == 'asc' ? 'up' : 'down'); ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'stock_quantity', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="text-decoration-none text-dark">
                                Tồn kho
                                <?php if(request('sort_by') == 'stock_quantity'): ?>
                                    <i class="fas fa-sort-<?php echo e(request('sort_order') == 'asc' ? 'up' : 'down'); ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <?php if($product->first_image): ?>
                                <img src="<?php echo e(asset('storage/' . $product->first_image)); ?>" 
                                     alt="<?php echo e($product->name); ?>" 
                                     class="img-thumbnail" 
                                     style="width: 50px; height: 50px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($product->name); ?></strong>
                                <?php if($product->is_featured): ?>
                                    <span class="badge bg-warning text-dark ms-1">Nổi bật</span>
                                <?php endif; ?>
                            </div>
                            <?php if($product->short_description): ?>
                                <small class="text-muted"><?php echo e(Str::limit($product->short_description, 50)); ?></small>
                            <?php endif; ?>
                        </td>
                        <td><code><?php echo e($product->sku); ?></code></td>
                        <td><?php echo e($product->category->name); ?></td>
                        <td><?php echo e($product->supplier->name); ?></td>
                        <td><?php echo e(number_format($product->cost_price)); ?>đ</td>
                        <td>
                            <strong><?php echo e(number_format($product->selling_price)); ?>đ</strong>
                            <?php if($product->compare_price): ?>
                                <br><small class="text-muted text-decoration-line-through"><?php echo e(number_format($product->compare_price)); ?>đ</small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo e($product->is_low_stock ? 'danger' : ($product->stock_quantity > 50 ? 'success' : 'warning')); ?>">
                                <?php echo e($product->stock_quantity); ?>

                            </span>
                        </td>
                        <td>
                            <?php if($product->status == 'active'): ?>
                                <span class="badge bg-success">Hoạt động</span>
                            <?php elseif($product->status == 'inactive'): ?>
                                <span class="badge bg-secondary">Không hoạt động</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Hết hàng</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?php echo e(route('products.show', $product)); ?>" class="btn btn-outline-info" title="Xem">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('products.edit', $product)); ?>" class="btn btn-outline-primary" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('products.destroy', $product)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger" title="Xóa"
                                            onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="11" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-box fa-3x mb-3"></i>
                                <p>Chưa có sản phẩm nào</p>
                                <a href="<?php echo e(route('products.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Thêm sản phẩm đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if($products->hasPages()): ?>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị <?php echo e($products->firstItem()); ?> - <?php echo e($products->lastItem()); ?> 
                trong tổng số <?php echo e($products->total()); ?> sản phẩm
            </div>
            <div>
                <?php echo e($products->appends(request()->query())->links()); ?>

            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/products/index.blade.php ENDPATH**/ ?>