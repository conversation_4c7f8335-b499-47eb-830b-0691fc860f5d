<?php

namespace App\Http\Controllers;

use App\Models\CheckoutOrder;
use App\Models\CheckoutUnit;
use App\Models\CheckoutPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckoutOrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Check if user is authenticated and has team
        if (!$user || !$user->team_id) {
            return redirect()->route('dashboard')->with('error', 'Please ensure you are assigned to a team.');
        }

        $query = CheckoutOrder::with(['checkoutUnit', 'checkoutPayments']);

        // Apply team filter based on user role
        if (!$user->isAdmin()) {
            $query->whereHas('checkoutUnit', function($q) use ($user) {
                $q->where('team_id', $user->team_id);
            });
        } elseif ($request->filled('team_id')) {
            $query->whereHas('checkoutUnit', function($q) use ($request) {
                $q->where('team_id', $request->team_id);
            });
        }

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%")
                  ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('checkout_unit_id')) {
            $query->where('checkout_unit_id', $request->checkout_unit_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $orders = $query->paginate(20);

        // Get checkout units for filter
        $checkoutUnitsQuery = CheckoutUnit::query();
        if (!$user->isAdmin()) {
            $checkoutUnitsQuery->where('team_id', $user->team_id);
        }
        $checkoutUnits = $checkoutUnitsQuery->get();

        // Get teams for admin filter
        $teams = $user->isAdmin() ? \App\Models\Team::all() : collect();

        return view('checkout.orders.index', compact('orders', 'checkoutUnits', 'teams'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();

        // Check if user is authenticated and has team
        if (!$user || !$user->team_id) {
            return redirect()->route('dashboard')->with('error', 'Please ensure you are assigned to a team.');
        }

        $checkoutUnits = CheckoutUnit::where('team_id', $user->team_id)->where('status', 'active')->get();

        return view('checkout.orders.create', compact('checkoutUnits'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'checkout_unit_id' => 'required|exists:checkout_units,id',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'nullable|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'customer_address' => 'nullable|string',
            'product_name' => 'required|string|max:255',
            'product_sku' => 'nullable|string|max:100',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'required|numeric|min:0',
            'total_amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        // Check if user can access this checkout unit
        $checkoutUnit = CheckoutUnit::findOrFail($validated['checkout_unit_id']);
        if (!$user->isAdmin() && $checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to checkout unit');
        }

        $validated['created_by'] = $user->id;

        CheckoutOrder::create($validated);

        return redirect()->route('checkout.orders.index')
            ->with('success', 'Đơn hàng checkout đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(CheckoutOrder $order)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $order->checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to order data');
        }

        $order->load(['checkoutUnit', 'checkoutPayments', 'createdBy']);

        return view('checkout.orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CheckoutOrder $order)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $order->checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to order data');
        }

        $checkoutUnits = CheckoutUnit::where('team_id', $user->team_id)->where('status', 'active')->get();

        return view('checkout.orders.edit', compact('order', 'checkoutUnits'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CheckoutOrder $order)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $order->checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to order data');
        }

        $validated = $request->validate([
            'checkout_unit_id' => 'required|exists:checkout_units,id',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'nullable|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'customer_address' => 'nullable|string',
            'product_name' => 'required|string|max:255',
            'product_sku' => 'nullable|string|max:100',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'required|numeric|min:0',
            'total_amount' => 'required|numeric|min:0',
            'status' => 'required|in:pending,processing,shipped,delivered,completed,cancelled,returned,refunded',
            'notes' => 'nullable|string',
        ]);

        // Check if user can access the new checkout unit
        $checkoutUnit = CheckoutUnit::findOrFail($validated['checkout_unit_id']);
        if (!$user->isAdmin() && $checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to checkout unit');
        }

        $order->update($validated);

        return redirect()->route('checkout.orders.index')
            ->with('success', 'Đơn hàng checkout đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CheckoutOrder $order)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $order->checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to order data');
        }

        // Check if order has payments
        if ($order->checkoutPayments()->count() > 0) {
            return redirect()->route('checkout.orders.index')
                ->with('error', 'Không thể xóa đơn hàng có thanh toán. Vui lòng xóa tất cả thanh toán trước.');
        }

        $order->delete();

        return redirect()->route('checkout.orders.index')
            ->with('success', 'Đơn hàng checkout đã được xóa thành công!');
    }

    /**
     * Update order status
     */
    public function updateStatus(Request $request, CheckoutOrder $order)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $order->checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to order data');
        }

        $validated = $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,completed,cancelled,returned,refunded',
            'notes' => 'nullable|string',
        ]);

        $order->update([
            'status' => $validated['status'],
            'notes' => $validated['notes'] ? $order->notes . "\n" . now()->format('Y-m-d H:i') . ": " . $validated['notes'] : $order->notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Trạng thái đơn hàng đã được cập nhật thành công!'
        ]);
    }

    /**
     * Bulk update orders
     */
    public function bulkUpdate(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:checkout_orders,id',
            'action' => 'required|in:update_status,delete',
            'status' => 'required_if:action,update_status|in:pending,processing,shipped,delivered,completed,cancelled,returned,refunded',
        ]);

        $orders = CheckoutOrder::whereIn('id', $validated['order_ids'])->get();

        // Check permissions for all orders
        foreach ($orders as $order) {
            if (!$user->isAdmin() && $order->checkoutUnit->team_id != $user->team_id) {
                abort(403, 'Unauthorized access to some order data');
            }
        }

        if ($validated['action'] === 'update_status') {
            CheckoutOrder::whereIn('id', $validated['order_ids'])
                ->update(['status' => $validated['status']]);

            $message = 'Đã cập nhật trạng thái cho ' . count($validated['order_ids']) . ' đơn hàng.';
        } else {
            CheckoutOrder::whereIn('id', $validated['order_ids'])->delete();
            $message = 'Đã xóa ' . count($validated['order_ids']) . ' đơn hàng.';
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Show order tracking
     */
    public function tracking(CheckoutOrder $checkoutOrder)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $checkoutOrder->checkoutUnit->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to order data');
        }

        $checkoutOrder->load(['checkoutUnit', 'checkoutPayments']);

        return view('checkout.orders.tracking', compact('checkoutOrder'));
    }
}
