# Hướng dẫn cài đặt nhanh Dropship Manager

## Bước 1: <PERSON><PERSON><PERSON> bị môi trường

### Cài đặt XAMPP
1. T<PERSON>i XAMPP từ https://www.apachefriends.org/
2. Cài đặt và khởi động Apache + MySQL
3. Mở phpMyAdmin: http://localhost/phpmyadmin

### Tạo database
1. Trong phpMyAdmin, tạo database mới: `dropshipping_db`
2. Chọn Collation: `utf8mb4_unicode_ci`

## Bước 2: C<PERSON><PERSON> đặt Laravel

```bash
# Cài đặt dependencies
composer install

# Tạo file .env
cp .env.example .env

# Tạo application key
php artisan key:generate
```

## Bước 3: Cấu hình database

Mở file `.env` và cập nhật:

```env
APP_NAME="Dropship Manager"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=dropshipping_db
DB_USERNAME=root
DB_PASSWORD=
```

## Bước 4: Chạy migration và seeder

```bash
# Tạo bảng và dữ liệu mẫu
php artisan migrate
php artisan db:seed

# Tạo symbolic link cho storage
php artisan storage:link
```

## Bước 5: Khởi động server

```bash
php artisan serve
```

Truy cập: http://localhost:8000

## Dữ liệu mẫu

Sau khi chạy seeder, hệ thống sẽ có:
- 8 danh mục sản phẩm
- 5 nhà cung cấp
- 8 sản phẩm mẫu
- 1 user admin (email: <EMAIL>)

## Tính năng chính

### Dashboard
- Thống kê tổng quan
- Biểu đồ doanh thu
- Sản phẩm sắp hết hàng

### Quản lý sản phẩm
- Thêm/sửa/xóa sản phẩm
- Upload hình ảnh
- Quản lý tồn kho
- Tính profit margin

### Import CSV
- Truy cập: `/import`
- Tải template CSV mẫu
- Import sản phẩm hàng loạt

## Cấu trúc file CSV

Các cột bắt buộc:
- `name`: Tên sản phẩm
- `cost_price`: Giá gốc
- `selling_price`: Giá bán  
- `category_name`: Tên danh mục
- `supplier_name`: Tên nhà cung cấp

Ví dụ:
```csv
name,cost_price,selling_price,category_name,supplier_name
"Áo thun nam",50000,100000,"Thời trang nam","Công ty ABC"
```

## Troubleshooting

### Lỗi database connection
- Kiểm tra MySQL đã khởi động trong XAMPP
- Kiểm tra thông tin database trong `.env`

### Lỗi 500 Internal Server Error
```bash
# Cấp quyền cho thư mục
chmod -R 755 storage
chmod -R 755 bootstrap/cache

# Hoặc trên Windows
icacls storage /grant Everyone:F /T
icacls bootstrap\cache /grant Everyone:F /T
```

### Lỗi Class not found
```bash
composer dump-autoload
```

### Lỗi storage link
```bash
php artisan storage:link
```

## Triển khai production

### Shared hosting
1. Upload mã nguồn vào thư mục riêng
2. Copy thư mục `public` vào `public_html`
3. Chỉnh sửa `public_html/index.php`:

```php
require __DIR__.'/../laravel_project/vendor/autoload.php';
$app = require_once __DIR__.'/../laravel_project/bootstrap/app.php';
```

4. Cập nhật `.env`:
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=http://yourdomain.com
```

### Đồng bộ dữ liệu
- Sử dụng MySQL server tập trung
- Tất cả máy kết nối đến cùng 1 database
- Cập nhật `DB_HOST` trong `.env`

## Liên hệ hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra file log: `storage/logs/laravel.log`
2. Tạo issue trên GitHub
3. Liên hệ support team

---

**Chúc bạn sử dụng Dropship Manager thành công!**
