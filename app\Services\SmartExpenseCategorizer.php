<?php

namespace App\Services;

use App\Models\MonthlyExpense;
use App\Models\ExpenseCategory;
use App\Models\ExpenseLearningData;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class SmartExpenseCategorizer
{
    private $categories = [
        'advertising' => [
            'keywords' => ['facebook ads', 'google ads', 'tiktok ads', 'instagram', 'marketing', 'promotion', 'boost', 'campaign'],
            'patterns' => ['/ads?/i', '/marketing/i', '/campaign/i', '/boost/i', '/quang.*cao/i', '/pr/i'],
            'amount_ranges' => ['min' => 100000, 'max' => 50000000],
            'frequency' => 'daily_to_weekly',
            'weight' => 1.0
        ],
        'shipping' => [
            'keywords' => ['ghn', 'viettel post', 'shipping', 'delivery', 'freight', 'logistics', 'giao hang', 'van chuyen'],
            'patterns' => ['/ship/i', '/delivery/i', '/freight/i', '/giao.*hang/i', '/van.*chuyen/i', '/ghn/i'],
            'amount_ranges' => ['min' => 20000, 'max' => 5000000],
            'frequency' => 'daily',
            'weight' => 1.2
        ],
        'refunds' => [
            'keywords' => ['refund', 'return', 'hoàn tiền', 'đổi trả', 'compensation', 'bồi thường'],
            'patterns' => ['/refund/i', '/return/i', '/hoan.*tien/i', '/doi.*tra/i', '/compensation/i'],
            'amount_ranges' => ['min' => 50000, 'max' => 20000000],
            'frequency' => 'occasional',
            'weight' => 1.1
        ],
        'tools_software' => [
            'keywords' => ['subscription', 'software', 'tool', 'app', 'service', 'license', 'saas', 'hosting'],
            'patterns' => ['/subscription/i', '/software/i', '/license/i', '/saas/i', '/hosting/i', '/domain/i'],
            'amount_ranges' => ['min' => 100000, 'max' => 10000000],
            'frequency' => 'monthly',
            'weight' => 1.0
        ],
        'office_supplies' => [
            'keywords' => ['office', 'supplies', 'stationery', 'equipment', 'furniture', 'văn phòng', 'thiết bị'],
            'patterns' => ['/office/i', '/supplies/i', '/equipment/i', '/van.*phong/i', '/thiet.*bi/i'],
            'amount_ranges' => ['min' => 50000, 'max' => 5000000],
            'frequency' => 'monthly_to_quarterly',
            'weight' => 0.9
        ],
        'utilities' => [
            'keywords' => ['electricity', 'water', 'internet', 'phone', 'điện', 'nước', 'internet', 'điện thoại'],
            'patterns' => ['/electric/i', '/water/i', '/internet/i', '/phone/i', '/dien/i', '/nuoc/i'],
            'amount_ranges' => ['min' => 200000, 'max' => 5000000],
            'frequency' => 'monthly',
            'weight' => 1.3
        ],
        'travel' => [
            'keywords' => ['travel', 'flight', 'hotel', 'taxi', 'grab', 'du lịch', 'máy bay', 'khách sạn'],
            'patterns' => ['/travel/i', '/flight/i', '/hotel/i', '/taxi/i', '/grab/i', '/du.*lich/i'],
            'amount_ranges' => ['min' => 100000, 'max' => 20000000],
            'frequency' => 'occasional',
            'weight' => 1.0
        ],
        'meals' => [
            'keywords' => ['meal', 'food', 'restaurant', 'lunch', 'dinner', 'ăn uống', 'nhà hàng', 'cơm'],
            'patterns' => ['/meal/i', '/food/i', '/restaurant/i', '/lunch/i', '/an.*uong/i', '/com/i'],
            'amount_ranges' => ['min' => 50000, 'max' => 2000000],
            'frequency' => 'daily',
            'weight' => 0.8
        ],
        'other' => [
            'keywords' => ['other', 'miscellaneous', 'khác', 'linh tinh'],
            'patterns' => ['/other/i', '/misc/i', '/khac/i'],
            'amount_ranges' => ['min' => 0, 'max' => *********],
            'frequency' => 'any',
            'weight' => 0.5
        ]
    ];

    public function categorizeExpense($description, $amount, $vendor = null, $teamId = null)
    {
        try {
            $scores = [];
            $description = strtolower(trim($description));
            
            foreach ($this->categories as $category => $rules) {
                $score = 0;
                
                // Keyword matching (30 points max)
                foreach ($rules['keywords'] as $keyword) {
                    if (stripos($description, $keyword) !== false) {
                        $score += 30 * $rules['weight'];
                        break; // Only count once per category
                    }
                }
                
                // Pattern matching (25 points max)
                foreach ($rules['patterns'] as $pattern) {
                    if (preg_match($pattern, $description)) {
                        $score += 25 * $rules['weight'];
                        break; // Only count once per category
                    }
                }
                
                // Amount range matching (20 points max)
                if ($amount >= $rules['amount_ranges']['min'] && 
                    $amount <= $rules['amount_ranges']['max']) {
                    $score += 20;
                }
                
                // Historical pattern matching (15 points max)
                if ($teamId) {
                    $historicalScore = $this->getHistoricalPatternScore($description, $amount, $category, $teamId);
                    $score += $historicalScore;
                }
                
                // Vendor matching (10 points max)
                if ($vendor) {
                    $vendorScore = $this->getVendorCategoryScore($vendor, $category);
                    $score += $vendorScore;
                }
                
                $scores[$category] = round($score, 2);
            }
            
            // Get best match
            arsort($scores);
            $bestCategory = array_key_first($scores);
            $confidence = min($scores[$bestCategory], 100);
            
            // Get alternatives (top 2 after best match)
            $alternatives = array_slice($scores, 1, 2, true);
            
            return [
                'suggested_category' => $bestCategory,
                'confidence' => $confidence,
                'alternatives' => $alternatives,
                'requires_review' => $confidence < 70,
                'all_scores' => $scores
            ];
            
        } catch (\Exception $e) {
            Log::error('Error in expense categorization: ' . $e->getMessage());
            
            return [
                'suggested_category' => 'other',
                'confidence' => 0,
                'alternatives' => [],
                'requires_review' => true,
                'error' => 'Categorization failed'
            ];
        }
    }
    
    private function getHistoricalPatternScore($description, $amount, $category, $teamId)
    {
        $cacheKey = "historical_pattern_{$teamId}_{$category}";
        
        return Cache::remember($cacheKey, 3600, function () use ($description, $amount, $category, $teamId) {
            // Get similar expenses from last 6 months
            $similarExpenses = MonthlyExpense::where('team_id', $teamId)
                ->where('category', $category)
                ->where('created_at', '>=', now()->subMonths(6))
                ->get();
            
            if ($similarExpenses->isEmpty()) {
                return 0;
            }
            
            $score = 0;
            $descriptionWords = explode(' ', strtolower($description));
            
            foreach ($similarExpenses as $expense) {
                $expenseWords = explode(' ', strtolower($expense->description));
                $commonWords = array_intersect($descriptionWords, $expenseWords);
                
                if (count($commonWords) > 0) {
                    $similarity = count($commonWords) / max(count($descriptionWords), count($expenseWords));
                    $score += $similarity * 15; // Max 15 points for historical patterns
                }
                
                // Amount similarity
                $amountDiff = abs($expense->amount - $amount) / max($expense->amount, $amount);
                if ($amountDiff < 0.3) { // Within 30% of historical amounts
                    $score += 5;
                }
            }
            
            return min($score, 15); // Cap at 15 points
        });
    }
    
    private function getVendorCategoryScore($vendor, $category)
    {
        $cacheKey = "vendor_category_{$vendor}_{$category}";
        
        return Cache::remember($cacheKey, 7200, function () use ($vendor, $category) {
            // Check if this vendor has been used for this category before
            $vendorHistory = MonthlyExpense::where('vendor', 'like', "%{$vendor}%")
                ->where('category', $category)
                ->where('created_at', '>=', now()->subMonths(12))
                ->count();
            
            if ($vendorHistory > 0) {
                return min($vendorHistory * 2, 10); // Max 10 points for vendor matching
            }
            
            return 0;
        });
    }
    
    public function learnFromUserCorrection($originalSuggestion, $userCorrection, $expenseData)
    {
        try {
            // Save learning data for future improvements
            ExpenseLearningData::create([
                'description' => $expenseData['description'],
                'amount' => $expenseData['amount'],
                'vendor' => $expenseData['vendor'] ?? null,
                'team_id' => $expenseData['team_id'],
                'suggested_category' => $originalSuggestion['suggested_category'],
                'actual_category' => $userCorrection,
                'confidence' => $originalSuggestion['confidence'],
                'correction_reason' => 'user_correction'
            ]);
            
            // Update category weights based on correction
            $this->updateCategoryWeights($originalSuggestion, $userCorrection, $expenseData);
            
            // Clear relevant caches
            $this->clearRelevantCaches($expenseData['team_id'], $userCorrection);
            
            Log::info("Learned from user correction: {$originalSuggestion['suggested_category']} -> {$userCorrection}");
            
        } catch (\Exception $e) {
            Log::error('Error in learning from user correction: ' . $e->getMessage());
        }
    }
    
    private function updateCategoryWeights($originalSuggestion, $userCorrection, $expenseData)
    {
        // This is a simplified learning mechanism
        // In a real ML system, this would be more sophisticated
        
        $description = strtolower($expenseData['description']);
        $wrongCategory = $originalSuggestion['suggested_category'];
        $correctCategory = $userCorrection;
        
        // Extract key terms from description
        $terms = explode(' ', $description);
        $terms = array_filter($terms, function($term) {
            return strlen($term) > 2; // Filter out short words
        });
        
        // Store term associations for future use
        foreach ($terms as $term) {
            $this->updateTermCategoryAssociation($term, $correctCategory, 1);
            $this->updateTermCategoryAssociation($term, $wrongCategory, -0.5);
        }
    }
    
    private function updateTermCategoryAssociation($term, $category, $weight)
    {
        // This would typically be stored in a database table
        // For now, we'll use cache with a long TTL
        $cacheKey = "term_category_{$term}_{$category}";
        $currentWeight = Cache::get($cacheKey, 0);
        $newWeight = $currentWeight + $weight;
        
        Cache::put($cacheKey, $newWeight, 86400 * 30); // 30 days
    }
    
    private function clearRelevantCaches($teamId, $category)
    {
        Cache::forget("historical_pattern_{$teamId}_{$category}");
        Cache::forget("vendor_category_*_{$category}");
    }
    
    public function getCategoryStatistics($teamId, $period = 30)
    {
        $expenses = MonthlyExpense::where('team_id', $teamId)
            ->where('created_at', '>=', now()->subDays($period))
            ->get();
        
        $stats = [];
        
        foreach ($this->categories as $category => $rules) {
            $categoryExpenses = $expenses->where('category', $category);
            
            $stats[$category] = [
                'count' => $categoryExpenses->count(),
                'total_amount' => $categoryExpenses->sum('amount'),
                'average_amount' => $categoryExpenses->avg('amount') ?: 0,
                'percentage' => $expenses->count() > 0 ? 
                    round(($categoryExpenses->count() / $expenses->count()) * 100, 2) : 0
            ];
        }
        
        return $stats;
    }
    
    public function getCategorizationAccuracy($teamId, $period = 30)
    {
        $learningData = ExpenseLearningData::where('team_id', $teamId)
            ->where('created_at', '>=', now()->subDays($period))
            ->get();
        
        if ($learningData->isEmpty()) {
            return ['accuracy' => 100, 'total_corrections' => 0];
        }
        
        $correctPredictions = $learningData->where('suggested_category', '=', 'actual_category')->count();
        $totalPredictions = $learningData->count();
        
        return [
            'accuracy' => round(($correctPredictions / $totalPredictions) * 100, 2),
            'total_corrections' => $totalPredictions,
            'correct_predictions' => $correctPredictions
        ];
    }
}
