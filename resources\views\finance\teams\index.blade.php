@extends('layouts.app')

@section('title', 'Quản lý Teams - Dropship Manager')
@section('page-title', 'Quản lý Teams')

@section('page-actions')
<a href="{{ route('finance.teams.create') }}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    Tạo Team mới
</a>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Danh sách Teams</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('finance.teams.index') }}" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control form-control-sm" name="search" 
                               placeholder="Tìm kiếm..." value="{{ request('search') }}">
                    </div>
                    <div class="col-auto">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Hoạt động</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="{{ route('finance.teams.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Leader</th>
                        <th>Members</th>
                        <th>eBay Accounts</th>
                        <th>Tài chính</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($teams as $team)
                    <tr>
                        <td>
                            <div>
                                <strong>{{ $team->name }}</strong>
                                <br><code class="small">{{ $team->code }}</code>
                                @if($team->description)
                                    <br><small class="text-muted">{{ Str::limit($team->description, 50) }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            @if($team->leader)
                                <div>
                                    <strong>{{ $team->leader->name }}</strong>
                                    <br><small class="text-muted">{{ $team->leader->email }}</small>
                                </div>
                            @else
                                <span class="text-muted">Chưa có leader</span>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <span class="badge bg-primary mb-1">{{ $team->member_stats['total'] }} tổng</span>
                                <small class="text-muted">
                                    {{ $team->member_stats['sellers'] }} sellers, 
                                    {{ $team->member_stats['users'] }} users
                                </small>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <span class="badge bg-success mb-1">{{ $team->member_stats['active_accounts'] }} active</span>
                                <small class="text-muted">{{ $team->member_stats['total_accounts'] }} tổng</small>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <small class="text-success">Payout: {{ number_format($team->total_payouts) }}đ</small>
                                <small class="text-danger">Chi phí: {{ number_format($team->total_expenses) }}đ</small>
                                <small class="text-{{ $team->profit >= 0 ? 'primary' : 'warning' }}">
                                    <strong>Lợi nhuận: {{ number_format($team->profit) }}đ</strong>
                                </small>
                            </div>
                        </td>
                        <td>
                            @if($team->status == 'active')
                                <span class="badge bg-success">Hoạt động</span>
                            @else
                                <span class="badge bg-secondary">Không hoạt động</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ route('finance.teams.show', $team) }}" class="btn btn-outline-info" title="Xem">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('finance.teams.edit', $team) }}" class="btn btn-outline-primary" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ route('finance.teams.members', $team) }}" class="btn btn-outline-success" title="Quản lý Members">
                                    <i class="fas fa-users"></i>
                                </a>
                                <form action="{{ route('finance.teams.destroy', $team) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger" title="Xóa"
                                            onclick="return confirm('Bạn có chắc chắn muốn xóa team này?\n\nLưu ý: Chỉ có thể xóa team không có members và dữ liệu tài chính.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>Chưa có team nào</p>
                                <a href="{{ route('finance.teams.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Tạo team đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($teams->hasPages())
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị {{ $teams->firstItem() }} - {{ $teams->lastItem() }} 
                trong tổng số {{ $teams->total() }} teams
            </div>
            <div>
                {{ $teams->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ $teams->total() }}</h5>
                <p class="card-text">Tổng Teams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ $teams->where('status', 'active')->count() }}</h5>
                <p class="card-text">Đang hoạt động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ $teams->sum(function($team) { return $team->member_stats['total']; }) }}</h5>
                <p class="card-text">Tổng Members</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ $teams->sum(function($team) { return $team->member_stats['total_accounts']; }) }}</h5>
                <p class="card-text">Tổng eBay Accounts</p>
            </div>
        </div>
    </div>
</div>
@endsection
