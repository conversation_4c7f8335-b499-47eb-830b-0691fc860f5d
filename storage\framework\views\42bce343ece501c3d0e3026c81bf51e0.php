<?php $__env->startSection('title', 'Quản lý Payouts - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý eBay Payouts'); ?>

<?php $__env->startSection('content'); ?>
<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('finance.payouts.index')); ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Mã payout, tài khoản..." value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Chờ xử lý</option>
                    <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Hoàn thành</option>
                    <option value="failed" <?php echo e(request('status') == 'failed' ? 'selected' : ''); ?>>Thất bại</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Team</label>
                <select name="team_id" class="form-select">
                    <option value="">Tất cả teams</option>
                    <?php $__currentLoopData = $teams ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($team->id); ?>" <?php echo e(request('team_id') == $team->id ? 'selected' : ''); ?>>
                            <?php echo e($team->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Sắp xếp</label>
                <select name="sort_by" class="form-select">
                    <option value="created_at" <?php echo e(request('sort_by') == 'created_at' ? 'selected' : ''); ?>>Ngày tạo</option>
                    <option value="amount_vnd" <?php echo e(request('sort_by') == 'amount_vnd' ? 'selected' : ''); ?>>Số tiền</option>
                    <option value="payout_date" <?php echo e(request('sort_by') == 'payout_date' ? 'selected' : ''); ?>>Ngày payout</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Payouts Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách Payouts</h6>
        <div class="btn-group">
            <a href="<?php echo e(route('finance.payouts.quick-create')); ?>" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i> Nhập nhanh
            </a>
            <a href="<?php echo e(route('finance.payouts.create')); ?>" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Thêm mới
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Mã Payout</th>
                        <th>Tài khoản eBay</th>
                        <th>Team</th>
                        <th>Số tiền</th>
                        <th>Ngày payout</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $payouts ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payout): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <strong><?php echo e($payout->payout_number); ?></strong>
                        </td>
                        <td>
                            <?php echo e($payout->ebayAccount->account_name ?? 'N/A'); ?>

                            <br><small class="text-muted"><?php echo e($payout->ebayAccount->email ?? ''); ?></small>
                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo e($payout->team->name ?? 'N/A'); ?></span>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e(number_format($payout->amount_vnd)); ?>đ</strong><br>
                                <small class="text-muted"><?php echo e($payout->amount); ?> <?php echo e($payout->currency); ?></small>
                            </div>
                        </td>
                        <td><?php echo e($payout->payout_date->format('d/m/Y')); ?></td>
                        <td>
                            <span class="badge bg-<?php echo e($payout->status == 'completed' ? 'success' : 
                                ($payout->status == 'failed' ? 'danger' : 'warning')); ?>">
                                <?php echo e(ucfirst($payout->status)); ?>

                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('finance.payouts.show', $payout)); ?>" class="btn btn-info btn-sm" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('finance.payouts.edit', $payout)); ?>" class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('finance.payouts.destroy', $payout)); ?>" method="POST" class="d-inline" 
                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa payout này?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger btn-sm" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="text-center">Không có payout nào</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if(isset($payouts) && $payouts->hasPages()): ?>
        <div class="d-flex justify-content-center">
            <?php echo e($payouts->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/finance/payouts/index.blade.php ENDPATH**/ ?>