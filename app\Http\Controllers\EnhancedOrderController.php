<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EnhancedOrderController extends Controller
{
    public function index(Request $request)
    {
        $query = Order::with(['user', 'team', 'orderItems.product'])
                     ->orderBy('created_at', 'desc');

        // Filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('platform')) {
            $query->where('platform', $request->platform);
        }

        if ($request->filled('platform_account')) {
            $query->where('platform_account', $request->platform_account);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('team_id')) {
            $query->where('team_id', $request->team_id);
        }

        if ($request->filled('is_profitable')) {
            $query->where('is_profitable', $request->boolean('is_profitable'));
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhere('buyer_id', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('external_order_id', 'like', "%{$search}%");
            });
        }

        // Team restriction for non-admin users
        if (!auth()->user()->isAdmin()) {
            $query->where('team_id', auth()->user()->team_id);
        }

        $orders = $query->paginate(20);

        // Statistics
        $stats = $this->getOrderStatistics($request);

        // Filter options
        $filterOptions = [
            'platforms' => Order::distinct()->pluck('platform')->filter(),
            'accounts' => Order::distinct()->pluck('platform_account')->filter(),
            'teams' => Team::all(),
            'users' => User::all(),
            'statuses' => [
                'pending' => 'Chờ xử lý',
                'confirmed' => 'Đã xác nhận',
                'processing' => 'Đang xử lý',
                'shipped' => 'Đã gửi hàng',
                'delivered' => 'Đã giao hàng',
                'cancelled' => 'Đã hủy',
                'returned' => 'Trả hàng'
            ]
        ];

        return view('orders.enhanced.index', compact('orders', 'stats', 'filterOptions'));
    }

    public function create()
    {
        $products = Product::active()->with('supplier')->get();
        $teams = Team::all();
        $users = User::all();
        $suppliers = Supplier::all();

        return view('orders.enhanced.create', compact('products', 'teams', 'users', 'suppliers'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email',
            'customer_phone' => 'required|string',
            'shipping_address' => 'required|string',
            'buyer_id' => 'nullable|string',
            'external_order_id' => 'nullable|string',
            'platform' => 'required|string',
            'platform_account' => 'required|string',
            'product_link' => 'nullable|url',
            'assigned_to' => 'nullable|exists:users,id',
            'team_id' => 'required|exists:teams,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.selling_price' => 'required|numeric|min:0',
            'platform_fee' => 'nullable|numeric|min:0',
            'shipping_fee' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        DB::transaction(function () use ($validated) {
            // Create order
            $order = Order::create([
                'user_id' => auth()->id(),
                'team_id' => $validated['team_id'],
                'customer_name' => $validated['customer_name'],
                'customer_email' => $validated['customer_email'],
                'customer_phone' => $validated['customer_phone'],
                'shipping_address' => $validated['shipping_address'],
                'buyer_id' => $validated['buyer_id'],
                'external_order_id' => $validated['external_order_id'],
                'platform' => $validated['platform'],
                'platform_account' => $validated['platform_account'],
                'product_link' => $validated['product_link'],
                'assigned_to' => $validated['assigned_to'],
                'platform_fee' => $validated['platform_fee'] ?? 0,
                'shipping_fee' => $validated['shipping_fee'] ?? 0,
                'notes' => $validated['notes'],
                'status' => 'pending'
            ]);

            // Create order items
            $subtotal = 0;
            $costTotal = 0;

            foreach ($validated['items'] as $item) {
                $product = Product::find($item['product_id']);
                $itemTotal = $item['selling_price'] * $item['quantity'];
                $itemCost = $product->cost_price * $item['quantity'];

                $order->orderItems()->create([
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'unit_price' => $item['selling_price'],
                    'quantity' => $item['quantity'],
                    'total_price' => $itemTotal
                ]);

                $subtotal += $itemTotal;
                $costTotal += $itemCost;
            }

            // Update order totals
            $order->update([
                'subtotal' => $subtotal,
                'total_amount' => $subtotal + $order->shipping_fee,
                'cost_amount' => $costTotal,
                'profit_amount' => $subtotal - $costTotal - $order->platform_fee,
                'profit_margin' => $costTotal > 0 ? (($subtotal - $costTotal) / $costTotal) * 100 : 0,
                'is_profitable' => ($subtotal - $costTotal - $order->platform_fee) > 0
            ]);
        });

        return redirect()->route('orders.enhanced.index')
                        ->with('success', 'Đơn hàng đã được tạo thành công!');
    }

    public function show(Order $order)
    {
        $order->load(['user', 'team', 'orderItems.product.supplier']);
        
        return view('orders.enhanced.show', compact('order'));
    }

    public function edit(Order $order)
    {
        $order->load(['orderItems.product']);
        $products = Product::active()->with('supplier')->get();
        $teams = Team::all();
        $users = User::all();
        $suppliers = Supplier::all();

        return view('orders.enhanced.edit', compact('order', 'products', 'teams', 'users', 'suppliers'));
    }

    public function update(Request $request, Order $order)
    {
        // Similar validation and update logic as store method
        // Implementation would be similar but for updating existing order
        
        return redirect()->route('orders.enhanced.show', $order)
                        ->with('success', 'Đơn hàng đã được cập nhật thành công!');
    }

    public function destroy(Order $order)
    {
        $order->delete();
        
        return redirect()->route('orders.enhanced.index')
                        ->with('success', 'Đơn hàng đã được xóa thành công!');
    }

    public function dashboard()
    {
        $stats = $this->getDashboardStatistics();
        
        return view('orders.enhanced.dashboard', compact('stats'));
    }

    private function getOrderStatistics($request)
    {
        $query = Order::query();
        
        // Apply same filters as index
        if (!auth()->user()->isAdmin()) {
            $query->where('team_id', auth()->user()->team_id);
        }

        return [
            'total_orders' => $query->count(),
            'pending_orders' => $query->where('status', 'pending')->count(),
            'processing_orders' => $query->where('status', 'processing')->count(),
            'completed_orders' => $query->whereIn('status', ['delivered', 'completed'])->count(),
            'total_revenue' => $query->sum('total_amount'),
            'total_profit' => $query->sum('profit_amount'),
            'profitable_orders' => $query->where('is_profitable', true)->count(),
            'avg_order_value' => $query->avg('total_amount'),
            'avg_profit_margin' => $query->avg('profit_margin')
        ];
    }

    private function getDashboardStatistics()
    {
        $query = Order::query();
        
        if (!auth()->user()->isAdmin()) {
            $query->where('team_id', auth()->user()->team_id);
        }

        $today = Carbon::today();
        $thisWeek = Carbon::now()->startOfWeek();
        $thisMonth = Carbon::now()->startOfMonth();

        return [
            'today' => [
                'orders' => $query->whereDate('created_at', $today)->count(),
                'revenue' => $query->whereDate('created_at', $today)->sum('total_amount'),
                'profit' => $query->whereDate('created_at', $today)->sum('profit_amount')
            ],
            'week' => [
                'orders' => $query->where('created_at', '>=', $thisWeek)->count(),
                'revenue' => $query->where('created_at', '>=', $thisWeek)->sum('total_amount'),
                'profit' => $query->where('created_at', '>=', $thisWeek)->sum('profit_amount')
            ],
            'month' => [
                'orders' => $query->where('created_at', '>=', $thisMonth)->count(),
                'revenue' => $query->where('created_at', '>=', $thisMonth)->sum('total_amount'),
                'profit' => $query->where('created_at', '>=', $thisMonth)->sum('profit_amount')
            ],
            'platforms' => $query->select('platform', DB::raw('count(*) as count'))
                                ->groupBy('platform')
                                ->pluck('count', 'platform'),
            'top_accounts' => $query->select('platform_account', DB::raw('sum(profit_amount) as profit'))
                                   ->groupBy('platform_account')
                                   ->orderBy('profit', 'desc')
                                   ->limit(5)
                                   ->get()
        ];
    }

    public function bulkUpdate(Request $request)
    {
        $validated = $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id',
            'action' => 'required|in:update_status,assign_to,update_platform_account',
            'status' => 'required_if:action,update_status',
            'assigned_to' => 'required_if:action,assign_to',
            'platform_account' => 'required_if:action,update_platform_account'
        ]);

        $orders = Order::whereIn('id', $validated['order_ids']);

        switch ($validated['action']) {
            case 'update_status':
                $orders->update(['status' => $validated['status']]);
                break;
            case 'assign_to':
                $orders->update(['assigned_to' => $validated['assigned_to']]);
                break;
            case 'update_platform_account':
                $orders->update(['platform_account' => $validated['platform_account']]);
                break;
        }

        return response()->json(['success' => true, 'message' => 'Cập nhật thành công!']);
    }
}
