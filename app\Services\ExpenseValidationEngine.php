<?php

namespace App\Services;

use App\Models\MonthlyExpense;
use App\Models\Team;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ExpenseValidationEngine
{
    private $businessRules = [
        'advertising' => [
            'max_single_transaction' => 20000000, // 20M VND
            'max_daily_total' => 50000000, // 50M VND
            'require_receipt_over' => 5000000, // 5M VND
            'flag_round_numbers_over' => 2000000 // 2M VND
        ],
        'shipping' => [
            'max_single_transaction' => 5000000, // 5M VND
            'max_daily_total' => 20000000, // 20M VND
            'require_receipt_over' => 1000000, // 1M VND
            'flag_round_numbers_over' => 500000 // 500K VND
        ],
        'refunds' => [
            'max_single_transaction' => 50000000, // 50M VND
            'max_daily_total' => 100000000, // 100M VND
            'require_receipt_over' => 2000000, // 2M VND
            'require_justification' => true
        ],
        'tools_software' => [
            'max_single_transaction' => 10000000, // 10M VND
            'max_monthly_total' => 30000000, // 30M VND
            'require_business_case_over' => 5000000, // 5M VND
            'check_duplicate_subscriptions' => true
        ],
        'office_supplies' => [
            'max_single_transaction' => 5000000, // 5M VND
            'max_monthly_total' => 15000000, // 15M VND
            'require_receipt_over' => 1000000 // 1M VND
        ],
        'utilities' => [
            'max_single_transaction' => 10000000, // 10M VND
            'expected_monthly_range' => [500000, 5000000], // 500K - 5M VND
            'flag_if_outside_range' => true
        ],
        'travel' => [
            'max_single_transaction' => 30000000, // 30M VND
            'require_approval_over' => 10000000, // 10M VND
            'require_itinerary' => true
        ],
        'meals' => [
            'max_single_transaction' => 2000000, // 2M VND
            'max_daily_total' => 5000000, // 5M VND
            'flag_expensive_meals' => 1000000 // 1M VND
        ]
    ];

    public function validateExpense($expense)
    {
        try {
            $validationResults = [
                'overall_status' => 'passed',
                'flags' => [],
                'warnings' => [],
                'errors' => [],
                'recommendations' => []
            ];

            // Amount validation
            $amountValidation = $this->validateAmount($expense);
            $validationResults['flags'] = array_merge($validationResults['flags'], $amountValidation);

            // Frequency validation
            $frequencyValidation = $this->validateFrequency($expense);
            $validationResults['warnings'] = array_merge($validationResults['warnings'], $frequencyValidation);

            // Pattern validation
            $patternValidation = $this->validatePattern($expense);
            $validationResults['warnings'] = array_merge($validationResults['warnings'], $patternValidation);

            // Business logic validation
            $businessValidation = $this->validateBusinessLogic($expense);
            $validationResults['errors'] = array_merge($validationResults['errors'], $businessValidation);

            // Fraud detection
            $fraudDetection = $this->detectFraudIndicators($expense);
            $validationResults['fraud_detection'] = $fraudDetection;

            // Determine overall status
            if (!empty($validationResults['errors']) || $fraudDetection['risk_level'] === 'high') {
                $validationResults['overall_status'] = 'failed';
            } elseif (!empty($validationResults['warnings']) || $fraudDetection['risk_level'] === 'medium') {
                $validationResults['overall_status'] = 'warning';
            }

            return $validationResults;

        } catch (\Exception $e) {
            Log::error('Error in expense validation: ' . $e->getMessage());
            
            return [
                'overall_status' => 'error',
                'flags' => [],
                'warnings' => [],
                'errors' => [['type' => 'system_error', 'message' => 'Validation system error']],
                'recommendations' => [],
                'fraud_detection' => ['risk_level' => 'unknown', 'fraud_score' => 0]
            ];
        }
    }

    private function validateAmount($expense)
    {
        $flags = [];
        $teamId = $expense->team_id ?? $expense['team_id'];
        $category = $expense->category ?? $expense['category'];
        $amount = $expense->amount ?? $expense['amount'];

        // Get historical data for comparison
        $historicalData = $this->getHistoricalExpenses($teamId, $category, 90);
        
        if ($historicalData->isNotEmpty()) {
            $avgAmount = $historicalData->avg('amount');
            $maxAmount = $historicalData->max('amount');
            $stdDev = $this->calculateStandardDeviation($historicalData->pluck('amount')->toArray());

            // Statistical anomaly detection
            if ($amount > ($avgAmount + (3 * $stdDev))) {
                $flags[] = [
                    'type' => 'statistical_anomaly',
                    'severity' => 'high',
                    'message' => "Số tiền vượt quá 3 độ lệch chuẩn so với trung bình (" . number_format($avgAmount, 0) . " VND)",
                    'action' => 'require_justification',
                    'data' => [
                        'amount' => $amount,
                        'average' => $avgAmount,
                        'standard_deviation' => $stdDev
                    ]
                ];
            } elseif ($amount > ($avgAmount + (2 * $stdDev))) {
                $flags[] = [
                    'type' => 'statistical_warning',
                    'severity' => 'medium',
                    'message' => "Số tiền cao hơn bình thường so với lịch sử",
                    'action' => 'review_recommended'
                ];
            }
        }

        // Business rule validation
        if (isset($this->businessRules[$category])) {
            $rules = $this->businessRules[$category];
            
            if (isset($rules['max_single_transaction']) && $amount > $rules['max_single_transaction']) {
                $flags[] = [
                    'type' => 'business_rule_violation',
                    'severity' => 'high',
                    'message' => "Vượt quá giới hạn giao dịch đơn lẻ cho category {$category} (" . number_format($rules['max_single_transaction'], 0) . " VND)",
                    'action' => 'require_manager_approval'
                ];
            }

            if (isset($rules['require_receipt_over']) && $amount > $rules['require_receipt_over']) {
                $flags[] = [
                    'type' => 'receipt_required',
                    'severity' => 'medium',
                    'message' => "Cần có hóa đơn/chứng từ cho giao dịch trên " . number_format($rules['require_receipt_over'], 0) . " VND",
                    'action' => 'request_receipt'
                ];
            }
        }

        // Round number detection
        if ($this->isRoundNumber($amount) && $amount > 1000000) {
            $flags[] = [
                'type' => 'round_number_alert',
                'severity' => 'low',
                'message' => "Số tiền tròn có thể cần xác minh thêm",
                'action' => 'verify_accuracy'
            ];
        }

        return $flags;
    }

    private function validateFrequency($expense)
    {
        $warnings = [];
        $teamId = $expense->team_id ?? $expense['team_id'];
        $category = $expense->category ?? $expense['category'];
        $userId = $expense->created_by ?? $expense['created_by'];

        // Check frequency patterns
        $recentExpenses = $this->getRecentExpenses($teamId, $category, $userId, 7);

        // High frequency detection
        if ($recentExpenses->count() > 5) {
            $warnings[] = [
                'type' => 'high_frequency',
                'severity' => 'medium',
                'message' => "Tần suất tạo expense cao bất thường ({$recentExpenses->count()} trong 7 ngày)",
                'action' => 'review_pattern',
                'data' => ['count' => $recentExpenses->count(), 'period' => 7]
            ];
        }

        // Rapid succession detection
        $rapidExpenses = $recentExpenses->where('created_at', '>=', now()->subHours(1));
        if ($rapidExpenses->count() > 2) {
            $warnings[] = [
                'type' => 'rapid_succession',
                'severity' => 'high',
                'message' => "Nhiều expenses được tạo trong 1 giờ ({$rapidExpenses->count()} expenses)",
                'action' => 'check_duplicates'
            ];
        }

        return $warnings;
    }

    private function validatePattern($expense)
    {
        $warnings = [];
        $description = $expense->description ?? $expense['description'];
        $amount = $expense->amount ?? $expense['amount'];

        // Check for suspicious patterns in description
        $suspiciousPatterns = [
            '/test/i' => 'Description contains "test"',
            '/temp/i' => 'Description contains "temp"',
            '/xxx/i' => 'Description contains placeholder text',
            '/asdf/i' => 'Description contains random characters',
            '/^.{1,3}$/i' => 'Description too short'
        ];

        foreach ($suspiciousPatterns as $pattern => $message) {
            if (preg_match($pattern, $description)) {
                $warnings[] = [
                    'type' => 'suspicious_description',
                    'severity' => 'medium',
                    'message' => $message,
                    'action' => 'verify_description'
                ];
                break;
            }
        }

        // Check for missing vendor information for certain categories
        $vendor = $expense->vendor ?? $expense['vendor'] ?? null;
        $category = $expense->category ?? $expense['category'];
        
        if (empty($vendor) && in_array($category, ['tools_software', 'advertising', 'office_supplies'])) {
            $warnings[] = [
                'type' => 'missing_vendor',
                'severity' => 'low',
                'message' => "Thiếu thông tin vendor cho category {$category}",
                'action' => 'add_vendor_info'
            ];
        }

        return $warnings;
    }

    private function validateBusinessLogic($expense)
    {
        $errors = [];
        $category = $expense->category ?? $expense['category'];
        $amount = $expense->amount ?? $expense['amount'];
        $teamId = $expense->team_id ?? $expense['team_id'];

        if (!isset($this->businessRules[$category])) {
            return $errors;
        }

        $rules = $this->businessRules[$category];

        // Check daily limits
        if (isset($rules['max_daily_total'])) {
            $todayTotal = $this->getDailyTotal($teamId, $category, today());
            if (($todayTotal + $amount) > $rules['max_daily_total']) {
                $errors[] = [
                    'type' => 'daily_limit_exceeded',
                    'severity' => 'high',
                    'message' => "Vượt quá giới hạn chi tiêu hàng ngày cho {$category}",
                    'action' => 'require_special_approval',
                    'data' => [
                        'current_total' => $todayTotal,
                        'new_amount' => $amount,
                        'limit' => $rules['max_daily_total']
                    ]
                ];
            }
        }

        // Check monthly limits
        if (isset($rules['max_monthly_total'])) {
            $monthlyTotal = $this->getMonthlyTotal($teamId, $category, now()->format('Y-m'));
            if (($monthlyTotal + $amount) > $rules['max_monthly_total']) {
                $errors[] = [
                    'type' => 'monthly_limit_exceeded',
                    'severity' => 'high',
                    'message' => "Vượt quá giới hạn chi tiêu hàng tháng cho {$category}",
                    'action' => 'require_budget_approval'
                ];
            }
        }

        return $errors;
    }

    private function detectFraudIndicators($expense)
    {
        $fraudScore = 0;
        $indicators = [];
        $amount = $expense->amount ?? $expense['amount'];
        $description = $expense->description ?? $expense['description'];
        $createdAt = $expense->created_at ?? now();
        $userId = $expense->created_by ?? $expense['created_by'];

        // Time-based indicators
        if ($this->isCreatedOutsideBusinessHours($createdAt)) {
            $fraudScore += 15;
            $indicators[] = 'created_outside_business_hours';
        }

        // Amount-based indicators
        if ($this->isRoundNumber($amount) && $amount > 5000000) {
            $fraudScore += 20;
            $indicators[] = 'large_round_amount';
        }

        // Description-based indicators
        if ($this->hasSuspiciousDescription($description)) {
            $fraudScore += 25;
            $indicators[] = 'suspicious_description';
        }

        // User behavior indicators
        if ($this->isNewUserLargeExpense($userId, $amount)) {
            $fraudScore += 30;
            $indicators[] = 'new_user_large_expense';
        }

        // Duplicate detection
        if ($this->hasPotentialDuplicate($expense)) {
            $fraudScore += 35;
            $indicators[] = 'potential_duplicate';
        }

        $riskLevel = $this->calculateRiskLevel($fraudScore);
        $recommendedAction = $this->getRecommendedAction($fraudScore);

        return [
            'fraud_score' => $fraudScore,
            'risk_level' => $riskLevel,
            'indicators' => $indicators,
            'recommended_action' => $recommendedAction,
            'requires_manual_review' => $fraudScore >= 50
        ];
    }

    // Helper methods
    private function getHistoricalExpenses($teamId, $category, $days)
    {
        return MonthlyExpense::where('team_id', $teamId)
            ->where('category', $category)
            ->where('created_at', '>=', now()->subDays($days))
            ->get();
    }

    private function getRecentExpenses($teamId, $category, $userId, $days)
    {
        return MonthlyExpense::where('team_id', $teamId)
            ->where('category', $category)
            ->where('created_by', $userId)
            ->where('created_at', '>=', now()->subDays($days))
            ->get();
    }

    private function getDailyTotal($teamId, $category, $date)
    {
        return MonthlyExpense::where('team_id', $teamId)
            ->where('category', $category)
            ->whereDate('created_at', $date)
            ->sum('amount');
    }

    private function getMonthlyTotal($teamId, $category, $month)
    {
        return MonthlyExpense::where('team_id', $teamId)
            ->where('category', $category)
            ->where('expense_month', $month)
            ->sum('amount');
    }

    private function calculateStandardDeviation($values)
    {
        if (count($values) < 2) return 0;
        
        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(function($x) use ($mean) {
            return pow($x - $mean, 2);
        }, $values)) / count($values);
        
        return sqrt($variance);
    }

    private function isRoundNumber($amount)
    {
        return $amount % 100000 === 0; // Multiples of 100K
    }

    private function isCreatedOutsideBusinessHours($createdAt)
    {
        $hour = Carbon::parse($createdAt)->hour;
        return $hour < 8 || $hour > 18; // Outside 8 AM - 6 PM
    }

    private function hasSuspiciousDescription($description)
    {
        $suspiciousPatterns = [
            '/test/i', '/temp/i', '/xxx/i', '/asdf/i', '/qwerty/i',
            '/^.{1,3}$/i', // Too short
            '/(.)\1{4,}/', // Repeated characters
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $description)) {
                return true;
            }
        }

        return false;
    }

    private function isNewUserLargeExpense($userId, $amount)
    {
        $userExpenseCount = MonthlyExpense::where('created_by', $userId)
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return $userExpenseCount < 5 && $amount > 5000000; // New user with large expense
    }

    private function hasPotentialDuplicate($expense)
    {
        $amount = $expense->amount ?? $expense['amount'];
        $description = $expense->description ?? $expense['description'];
        $teamId = $expense->team_id ?? $expense['team_id'];

        $similar = MonthlyExpense::where('team_id', $teamId)
            ->where('amount', $amount)
            ->where('description', 'like', '%' . substr($description, 0, 20) . '%')
            ->where('created_at', '>=', now()->subHours(24))
            ->count();

        return $similar > 0;
    }

    private function calculateRiskLevel($fraudScore)
    {
        if ($fraudScore >= 70) return 'high';
        if ($fraudScore >= 40) return 'medium';
        return 'low';
    }

    private function getRecommendedAction($fraudScore)
    {
        if ($fraudScore >= 70) return 'immediate_freeze_and_investigation';
        if ($fraudScore >= 50) return 'require_manager_approval_and_verification';
        if ($fraudScore >= 30) return 'enhanced_review_required';
        return 'standard_processing';
    }
}
