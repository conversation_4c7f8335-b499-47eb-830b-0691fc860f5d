<?php

namespace App\Http\Controllers;

use App\Models\CheckoutUnit;
use Illuminate\Http\Request;

class CheckoutUnitController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = CheckoutUnit::query();

        // Tìm kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('contact_person', 'like', "%{$search}%");
            });
        }

        // Lọc theo trạng thái
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Sắp xếp
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $checkoutUnits = $query->withCount(['checkoutOrders', 'checkoutPayments'])->paginate(20);

        return view('checkout.units.index', compact('checkoutUnits'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('checkout.units.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:checkout_units,code',
            'description' => 'nullable|string',
            'checkout_rate' => 'required|numeric|min:0|max:100',
            'google_sheet_url' => 'nullable|url',
            'contact_person' => 'nullable|string|max:255',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive',
        ]);

        CheckoutUnit::create($validated);

        return redirect()->route('checkout.units.index')
            ->with('success', 'Đơn vị checkout đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(CheckoutUnit $checkoutUnit)
    {
        $checkoutUnit->load(['checkoutOrders' => function($query) {
            $query->with('checkoutPayments')->latest()->take(20);
        }]);

        // Thống kê chi tiết
        $stats = [
            'total_orders' => $checkoutUnit->total_orders,
            'completed_orders' => $checkoutUnit->completed_orders,
            'problematic_orders' => $checkoutUnit->problematic_orders,
            'total_checkout_amount' => $checkoutUnit->total_checkout_amount,
            'total_paid_amount' => $checkoutUnit->total_paid_amount,
            'pending_payment_amount' => $checkoutUnit->pending_payment_amount,
            'completion_rate' => $checkoutUnit->completion_rate,
            'problem_rate' => $checkoutUnit->problem_rate,
        ];

        // Thống kê theo status
        $statusStats = $checkoutUnit->checkoutOrders()
            ->selectRaw('status, count(*) as count, sum(total_amount) as amount')
            ->groupBy('status')
            ->get();

        // Thống kê theo tháng (6 tháng gần nhất)
        $monthlyStats = [];
        for ($i = 5; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $orders = $checkoutUnit->checkoutOrders()
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();

            $amount = $checkoutUnit->checkoutOrders()
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->sum('total_amount');

            $monthlyStats[] = [
                'month' => $date->format('m/Y'),
                'orders' => $orders,
                'amount' => $amount,
            ];
        }

        return view('checkout.units.show', compact('checkoutUnit', 'stats', 'statusStats', 'monthlyStats'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CheckoutUnit $checkoutUnit)
    {
        return view('checkout.units.edit', compact('checkoutUnit'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CheckoutUnit $checkoutUnit)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:checkout_units,code,' . $checkoutUnit->id,
            'description' => 'nullable|string',
            'checkout_rate' => 'required|numeric|min:0|max:100',
            'google_sheet_url' => 'nullable|url',
            'contact_person' => 'nullable|string|max:255',
            'contact_email' => 'nullable|email|max:255',
            'contact_phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive',
        ]);

        $checkoutUnit->update($validated);

        return redirect()->route('checkout.units.index')
            ->with('success', 'Đơn vị checkout đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CheckoutUnit $checkoutUnit)
    {
        // Kiểm tra xem có đơn hàng nào thuộc đơn vị này không
        if ($checkoutUnit->checkoutOrders()->count() > 0) {
            return redirect()->route('checkout.units.index')
                ->with('error', 'Không thể xóa đơn vị checkout này vì vẫn còn đơn hàng!');
        }

        $checkoutUnit->delete();

        return redirect()->route('checkout.units.index')
            ->with('success', 'Đơn vị checkout đã được xóa thành công!');
    }

    /**
     * Sync data from Google Sheets
     */
    public function syncGoogleSheet(CheckoutUnit $checkoutUnit)
    {
        if (!$checkoutUnit->google_sheet_url) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa cấu hình link Google Sheets'
            ]);
        }

        // TODO: Implement Google Sheets sync logic
        // Có thể sử dụng Google Sheets API hoặc CSV export

        return response()->json([
            'success' => true,
            'message' => 'Đồng bộ dữ liệu thành công!'
        ]);
    }

    /**
     * Export unit statistics
     */
    public function export(CheckoutUnit $checkoutUnit, Request $request)
    {
        $format = $request->get('format', 'csv');

        // TODO: Implement export logic
        // Export thống kê đơn vị checkout ra CSV/Excel

        return response()->json([
            'success' => true,
            'message' => 'Xuất dữ liệu thành công!'
        ]);
    }
}
