<?php

namespace App\Providers;

use App\Services\SmartExpenseCategorizer;
use App\Services\ExpenseValidationEngine;
use App\Services\SmartApprovalWorkflow;
use App\Services\ExpenseAnalyticsDashboard;
use Illuminate\Support\ServiceProvider;

class SmartExpenseServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register Smart Expense Services as singletons
        $this->app->singleton(SmartExpenseCategorizer::class, function ($app) {
            return new SmartExpenseCategorizer();
        });

        $this->app->singleton(ExpenseValidationEngine::class, function ($app) {
            return new ExpenseValidationEngine();
        });

        $this->app->singleton(SmartApprovalWorkflow::class, function ($app) {
            return new SmartApprovalWorkflow(
                $app->make(ExpenseValidationEngine::class)
            );
        });

        $this->app->singleton(ExpenseAnalyticsDashboard::class, function ($app) {
            return new ExpenseAnalyticsDashboard();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Boot any additional services if needed
    }
}
