<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expense_learning_data', function (Blueprint $table) {
            $table->id();
            $table->text('description');
            $table->decimal('amount', 15, 2);
            $table->string('vendor')->nullable();
            $table->foreignId('team_id')->constrained()->onDelete('cascade');
            $table->string('suggested_category');
            $table->string('actual_category');
            $table->decimal('confidence', 5, 2);
            $table->string('correction_reason')->default('user_correction');
            $table->json('metadata')->nullable(); // For storing additional learning data
            $table->timestamps();

            $table->index(['team_id', 'created_at']);
            $table->index(['suggested_category', 'actual_category']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expense_learning_data');
    }
};
