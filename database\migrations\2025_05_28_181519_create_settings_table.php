<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // Khóa setting (VD: site_name, default_currency)
            $table->text('value')->nullable(); // Giá trị setting (có thể là JSON)
            $table->string('group')->default('general'); // Nhóm setting (general, finance, notification, etc.)
            $table->string('type')->default('string'); // Loại dữ liệu (string, integer, boolean, json, array)
            $table->text('description')->nullable(); // Mô tả setting
            $table->text('default_value')->nullable(); // <PERSON><PERSON><PERSON> trị mặc định
            $table->boolean('is_public')->default(false); // <PERSON><PERSON> thể truy cập public không
            $table->boolean('is_encrypted')->default(false); // C<PERSON> mã hóa không (cho API keys)
            $table->json('validation_rules')->nullable(); // Rules validation (JSON)
            $table->json('options')->nullable(); // Options cho select/radio (JSON)
            $table->integer('sort_order')->default(0); // Thứ tự hiển thị
            $table->foreignId('updated_by')->nullable()->constrained('users')->onDelete('set null'); // Người cập nhật cuối
            $table->timestamps();

            $table->index(['group', 'sort_order']);
            $table->index('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
