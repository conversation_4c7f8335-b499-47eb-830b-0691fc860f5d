<?php $__env->startSection('title', '<PERSON><PERSON>ả<PERSON> lý Đ<PERSON> hàng - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý Đơn hàng'); ?>

<?php $__env->startSection('content'); ?>
<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('orders.index')); ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Mã đơn, tên khách hàng..." value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">Tr<PERSON>ng thái</label>
                <select name="status" class="form-select">
                    <option value=""><PERSON>ấ<PERSON> cả</option>
                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Chờ xử lý</option>
                    <option value="confirmed" <?php echo e(request('status') == 'confirmed' ? 'selected' : ''); ?>>Đã xác nhận</option>
                    <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>Đang xử lý</option>
                    <option value="shipped" <?php echo e(request('status') == 'shipped' ? 'selected' : ''); ?>>Đã gửi</option>
                    <option value="delivered" <?php echo e(request('status') == 'delivered' ? 'selected' : ''); ?>>Đã giao</option>
                    <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Đã hủy</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Thanh toán</label>
                <select name="payment_status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="pending" <?php echo e(request('payment_status') == 'pending' ? 'selected' : ''); ?>>Chờ thanh toán</option>
                    <option value="paid" <?php echo e(request('payment_status') == 'paid' ? 'selected' : ''); ?>>Đã thanh toán</option>
                    <option value="failed" <?php echo e(request('payment_status') == 'failed' ? 'selected' : ''); ?>>Thất bại</option>
                    <option value="refunded" <?php echo e(request('payment_status') == 'refunded' ? 'selected' : ''); ?>>Đã hoàn tiền</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Sắp xếp</label>
                <select name="sort_by" class="form-select">
                    <option value="created_at" <?php echo e(request('sort_by') == 'created_at' ? 'selected' : ''); ?>>Ngày tạo</option>
                    <option value="total_amount" <?php echo e(request('sort_by') == 'total_amount' ? 'selected' : ''); ?>>Tổng tiền</option>
                    <option value="customer_name" <?php echo e(request('sort_by') == 'customer_name' ? 'selected' : ''); ?>>Tên khách hàng</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Orders Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách Đơn hàng</h6>
        <a href="<?php echo e(route('orders.create')); ?>" class="btn btn-primary btn-sm">
            <i class="fas fa-plus"></i> Tạo đơn hàng
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Mã đơn</th>
                        <th>Khách hàng</th>
                        <th>Số items</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Thanh toán</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <strong><?php echo e($order->order_number); ?></strong>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($order->customer_name); ?></strong><br>
                                <small class="text-muted"><?php echo e($order->customer_email); ?></small>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo e($order->total_items); ?></span>
                        </td>
                        <td>
                            <strong><?php echo e(number_format($order->total_amount)); ?>đ</strong>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo e($order->status == 'pending' ? 'warning' : 
                                ($order->status == 'delivered' ? 'success' : 
                                ($order->status == 'cancelled' ? 'danger' : 'primary'))); ?>">
                                <?php echo e(ucfirst($order->status)); ?>

                            </span>
                        </td>
                        <td>
                            <span class="badge bg-<?php echo e($order->payment_status == 'paid' ? 'success' : 
                                ($order->payment_status == 'failed' ? 'danger' : 'warning')); ?>">
                                <?php echo e(ucfirst($order->payment_status)); ?>

                            </span>
                        </td>
                        <td><?php echo e($order->created_at->format('d/m/Y H:i')); ?></td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('orders.show', $order)); ?>" class="btn btn-info btn-sm" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('orders.edit', $order)); ?>" class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if($order->can_cancel): ?>
                                <form action="<?php echo e(route('orders.destroy', $order)); ?>" method="POST" class="d-inline" 
                                      onsubmit="return confirm('Bạn có chắc chắn muốn hủy đơn hàng này?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger btn-sm" title="Hủy đơn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="8" class="text-center">Không có đơn hàng nào</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($orders->hasPages()): ?>
        <div class="d-flex justify-content-center">
            <?php echo e($orders->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/orders/index.blade.php ENDPATH**/ ?>