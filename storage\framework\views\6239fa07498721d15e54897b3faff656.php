<?php $__env->startSection('title', 'Quản lý Chi phí'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Analytics Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Tổng quan Chi phí - <?php echo e($analytics['spending_overview']['vs_previous_period']['trend'] == 'increasing' ? '📈' : ($analytics['spending_overview']['vs_previous_period']['trend'] == 'decreasing' ? '📉' : '➡️')); ?>

                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-primary mb-1"><?php echo e(number_format($analytics['spending_overview']['total_spent'], 0)); ?> VND</h3>
                                <p class="text-muted mb-0">Tổng chi phí tháng này</p>
                                <small class="text-<?php echo e($analytics['spending_overview']['vs_previous_period']['percentage_change'] >= 0 ? 'danger' : 'success'); ?>">
                                    <?php echo e($analytics['spending_overview']['vs_previous_period']['percentage_change'] >= 0 ? '+' : ''); ?><?php echo e($analytics['spending_overview']['vs_previous_period']['percentage_change']); ?>% so với tháng trước
                                </small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-info mb-1"><?php echo e($analytics['spending_overview']['total_transactions']); ?></h3>
                                <p class="text-muted mb-0">Tổng giao dịch</p>
                                <small class="text-muted"><?php echo e(number_format($analytics['spending_overview']['average_transaction'], 0)); ?> VND/giao dịch</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-warning mb-1"><?php echo e(number_format($analytics['spending_overview']['pending_approvals'], 0)); ?> VND</h3>
                                <p class="text-muted mb-0">Chờ duyệt</p>
                                <small class="text-muted"><?php echo e($analytics['approval_metrics']['total_pending']); ?> giao dịch</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-success mb-1"><?php echo e($analytics['approval_metrics']['approval_rate']); ?>%</h3>
                                <p class="text-muted mb-0">Tỷ lệ duyệt</p>
                                <small class="text-muted"><?php echo e($analytics['approval_metrics']['auto_approved']); ?> tự động duyệt</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt text-primary me-2"></i>
                        Danh sách Chi phí
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="<?php echo e(route('finance.expenses.analytics')); ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-chart-bar me-1"></i>
                            Analytics
                        </a>
                        <a href="<?php echo e(route('finance.expenses.export')); ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </a>
                        <a href="<?php echo e(route('finance.expenses.create')); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            Tạo mới
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="<?php echo e(route('finance.expenses.index')); ?>" class="row g-3">
                                <div class="col-md-2">
                                    <label for="status" class="form-label">Trạng thái</label>
                                    <select class="form-select form-select-sm" id="status" name="status">
                                        <option value="">Tất cả</option>
                                        <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Chờ duyệt</option>
                                        <option value="approved" <?php echo e(request('status') == 'approved' ? 'selected' : ''); ?>>Đã duyệt</option>
                                        <option value="paid" <?php echo e(request('status') == 'paid' ? 'selected' : ''); ?>>Đã thanh toán</option>
                                        <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Đã hủy</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="approval_status" class="form-label">Duyệt</label>
                                    <select class="form-select form-select-sm" id="approval_status" name="approval_status">
                                        <option value="">Tất cả</option>
                                        <option value="pending" <?php echo e(request('approval_status') == 'pending' ? 'selected' : ''); ?>>Chờ duyệt</option>
                                        <option value="approved" <?php echo e(request('approval_status') == 'approved' ? 'selected' : ''); ?>>Đã duyệt</option>
                                        <option value="auto_approved" <?php echo e(request('approval_status') == 'auto_approved' ? 'selected' : ''); ?>>Tự động duyệt</option>
                                        <option value="rejected" <?php echo e(request('approval_status') == 'rejected' ? 'selected' : ''); ?>>Từ chối</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="category" class="form-label">Phân loại</label>
                                    <select class="form-select form-select-sm" id="category" name="category">
                                        <option value="">Tất cả</option>
                                        <option value="advertising" <?php echo e(request('category') == 'advertising' ? 'selected' : ''); ?>>Quảng cáo</option>
                                        <option value="shipping" <?php echo e(request('category') == 'shipping' ? 'selected' : ''); ?>>Vận chuyển</option>
                                        <option value="refunds" <?php echo e(request('category') == 'refunds' ? 'selected' : ''); ?>>Hoàn tiền</option>
                                        <option value="tools_software" <?php echo e(request('category') == 'tools_software' ? 'selected' : ''); ?>>Công cụ/Phần mềm</option>
                                        <option value="office_supplies" <?php echo e(request('category') == 'office_supplies' ? 'selected' : ''); ?>>Văn phòng phẩm</option>
                                        <option value="utilities" <?php echo e(request('category') == 'utilities' ? 'selected' : ''); ?>>Tiện ích</option>
                                        <option value="travel" <?php echo e(request('category') == 'travel' ? 'selected' : ''); ?>>Du lịch</option>
                                        <option value="meals" <?php echo e(request('category') == 'meals' ? 'selected' : ''); ?>>Ăn uống</option>
                                        <option value="other" <?php echo e(request('category') == 'other' ? 'selected' : ''); ?>>Khác</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="risk_level" class="form-label">Mức độ rủi ro</label>
                                    <select class="form-select form-select-sm" id="risk_level" name="risk_level">
                                        <option value="">Tất cả</option>
                                        <option value="low" <?php echo e(request('risk_level') == 'low' ? 'selected' : ''); ?>>Thấp</option>
                                        <option value="medium" <?php echo e(request('risk_level') == 'medium' ? 'selected' : ''); ?>>Trung bình</option>
                                        <option value="high" <?php echo e(request('risk_level') == 'high' ? 'selected' : ''); ?>>Cao</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="month" class="form-label">Tháng</label>
                                    <input type="month" class="form-control form-control-sm" id="month" name="month" value="<?php echo e(request('month', date('Y-m'))); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label for="search" class="form-label">Tìm kiếm</label>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control" id="search" name="search" value="<?php echo e(request('search')); ?>" placeholder="Tìm kiếm...">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <?php if(auth()->user()->hasRole(['admin', 'finance_manager', 'team_leader'])): ?>
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="bulkApproveBtn">
                                    <i class="fas fa-check-double me-1"></i>
                                    Duyệt hàng loạt (Rủi ro thấp)
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.location.reload()">
                                    <i class="fas fa-sync me-1"></i>
                                    Làm mới
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Expenses Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Mã số</th>
                                    <th>Tiêu đề</th>
                                    <th>Phân loại</th>
                                    <th>Số tiền</th>
                                    <th>Trạng thái</th>
                                    <th>Duyệt</th>
                                    <th>Rủi ro</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $expenses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <a href="<?php echo e(route('finance.expenses.show', $expense)); ?>" class="text-decoration-none">
                                            <?php echo e($expense->expense_number); ?>

                                        </a>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo e($expense->title); ?></strong>
                                            <?php if($expense->vendor): ?>
                                                <br><small class="text-muted"><?php echo e($expense->vendor); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?php echo e(ucfirst($expense->category)); ?></span>
                                        <?php if($expense->category_manually_corrected): ?>
                                            <br><small class="text-warning"><i class="fas fa-edit"></i> Đã sửa</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong><?php echo e(number_format($expense->amount, 0)); ?> <?php echo e($expense->currency); ?></strong>
                                    </td>
                                    <td>
                                        <?php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'paid' => 'primary',
                                                'cancelled' => 'danger'
                                            ];
                                        ?>
                                        <span class="badge bg-<?php echo e($statusColors[$expense->status] ?? 'secondary'); ?>">
                                            <?php echo e($expense->status_label); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                            $approvalColors = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'auto_approved' => 'info',
                                                'rejected' => 'danger'
                                            ];
                                        ?>
                                        <span class="badge bg-<?php echo e($approvalColors[$expense->approval_status] ?? 'secondary'); ?>">
                                            <?php echo e(ucfirst($expense->approval_status)); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                            $riskColors = [
                                                'low' => 'success',
                                                'medium' => 'warning',
                                                'high' => 'danger'
                                            ];
                                        ?>
                                        <span class="badge bg-<?php echo e($riskColors[$expense->risk_level] ?? 'secondary'); ?>">
                                            <?php echo e(ucfirst($expense->risk_level)); ?>

                                        </span>
                                        <?php if($expense->fraud_score > 0): ?>
                                            <br><small class="text-muted">Score: <?php echo e($expense->fraud_score); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div><?php echo e($expense->created_at->format('d/m/Y')); ?></div>
                                        <small class="text-muted"><?php echo e($expense->created_at->format('H:i')); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="<?php echo e(route('finance.expenses.show', $expense)); ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if($expense->can_edit): ?>
                                                <a href="<?php echo e(route('finance.expenses.edit', $expense)); ?>" class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                            <?php if($expense->can_approve && auth()->user()->hasRole(['admin', 'finance_manager', 'team_leader'])): ?>
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="showApprovalModal(<?php echo e($expense->id); ?>)">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-inbox fa-3x mb-3"></i>
                                            <p>Không có chi phí nào được tìm thấy.</p>
                                            <a href="<?php echo e(route('finance.expenses.create')); ?>" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i>
                                                Tạo chi phí đầu tiên
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if($expenses->hasPages()): ?>
                        <div class="d-flex justify-content-center">
                            <?php echo e($expenses->appends(request()->query())->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xử lý duyệt chi phí</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="approvalForm" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="decision" class="form-label">Quyết định</label>
                        <select class="form-select" id="decision" name="decision" required>
                            <option value="">Chọn quyết định</option>
                            <option value="approved">Duyệt</option>
                            <option value="rejected">Từ chối</option>
                            <option value="request_changes">Yêu cầu chỉnh sửa</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="comments" class="form-label">Ghi chú</label>
                        <textarea class="form-control" id="comments" name="comments" rows="3" placeholder="Nhập ghi chú (tùy chọn)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Xác nhận</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Bulk approve functionality
    $('#bulkApproveBtn').on('click', function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Đang xử lý...');
        
        $.ajax({
            url: '<?php echo e(route("finance.expenses.bulk-approve")); ?>',
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showAlert(`Đã xử lý ${response.processed} chi phí rủi ro thấp`, 'success');
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    showAlert('Có lỗi xảy ra khi xử lý', 'danger');
                }
            },
            error: function() {
                showAlert('Có lỗi xảy ra khi xử lý', 'danger');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check-double me-1"></i>Duyệt hàng loạt (Rủi ro thấp)');
            }
        });
    });
});

function showApprovalModal(expenseId) {
    $('#approvalForm').attr('action', `/finance/expenses/${expenseId}/process-approval`);
    $('#approvalModal').modal('show');
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/expenses/index.blade.php ENDPATH**/ ?>