<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('product_name'); // Lưu tên sản phẩm tại thời điểm đặt hàng
            $table->string('product_sku'); // Lưu SKU tại thời điểm đặt hàng
            $table->decimal('unit_price', 10, 2); // Gi<PERSON> tại thời điểm đặt hàng
            $table->integer('quantity');
            $table->decimal('total_price', 10, 2); // unit_price * quantity
            $table->json('product_attributes')->nullable(); // <PERSON>hu<PERSON>c tính sản phẩm (màu, size, etc.)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
