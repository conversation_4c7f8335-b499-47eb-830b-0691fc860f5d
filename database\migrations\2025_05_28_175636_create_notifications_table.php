<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // Loại notification
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Người nhận
            $table->foreignId('team_id')->nullable()->constrained()->onDelete('cascade'); // Team liên quan
            $table->string('title'); // Tiêu đề notification
            $table->text('message'); // Nội dung
            $table->json('data')->nullable(); // Dữ liệu bổ sung
            $table->string('action_url')->nullable(); // Link để xem chi tiết
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium'); // Độ ưu tiên
            $table->timestamp('read_at')->nullable(); // Thời gian đọc
            $table->timestamps();

            $table->index(['user_id', 'read_at']);
            $table->index(['team_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
