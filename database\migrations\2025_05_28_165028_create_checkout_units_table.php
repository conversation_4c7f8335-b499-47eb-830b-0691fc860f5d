<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checkout_units', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên đơn vị checkout (VD: Team A, Team B, etc.)
            $table->string('code')->unique(); // Mã đơn vị (VD: TEAM_A, TEAM_B)
            $table->text('description')->nullable(); // Mô tả
            $table->decimal('checkout_rate', 5, 2)->default(0); // Tỷ lệ checkout (%)
            $table->string('google_sheet_url')->nullable(); // Link Google Sheet
            $table->string('contact_person')->nullable(); // Người liên hệ
            $table->string('contact_email')->nullable(); // <PERSON><PERSON> liên hệ
            $table->string('contact_phone')->nullable(); // Số điện thoại
            $table->enum('status', ['active', 'inactive'])->default('active'); // Trạng thái
            $table->json('settings')->nullable(); // Cài đặt bổ sung (JSON)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checkout_units');
    }
};
