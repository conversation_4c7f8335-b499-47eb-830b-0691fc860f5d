<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TeamController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Chỉ admin mới có thể xem tất cả teams
        if (!$user->isAdmin()) {
            abort(403, 'Bạn không có quyền truy cập trang này.');
        }

        $query = Team::with(['leader', 'members']);

        // Tìm kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Lọc theo status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $teams = $query->paginate(15);

        // Thêm thống kê cho mỗi team
        $teams->getCollection()->transform(function ($team) {
            $team->member_stats = $team->getMemberStats();
            return $team;
        });

        return view('finance.teams.index', compact('teams'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();

        if (!$user->isAdmin()) {
            abort(403, 'Bạn không có quyền tạo team mới.');
        }

        // Lấy danh sách users có thể làm leader (chưa là leader của team nào)
        $availableLeaders = User::whereNotIn('id', function($query) {
            $query->select('leader_id')->from('teams')->whereNotNull('leader_id');
        })->get();

        return view('finance.teams.create', compact('availableLeaders'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        if (!$user->isAdmin()) {
            abort(403, 'Bạn không có quyền tạo team mới.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:teams,name',
            'code' => 'nullable|string|max:50|unique:teams,code',
            'description' => 'nullable|string',
            'leader_id' => 'nullable|exists:users,id',
            'status' => 'required|in:active,inactive'
        ]);

        // Kiểm tra leader có thuộc team nào chưa
        if ($validated['leader_id']) {
            $leader = User::find($validated['leader_id']);
            // Tạm thời cho phép leader thuộc team khác, sẽ chuyển sau
        }

        $team = Team::create($validated);

        // Nếu có leader, cập nhật role và team cho leader
        if ($validated['leader_id']) {
            $leader = User::find($validated['leader_id']);
            $leader->update([
                'team_id' => $team->id,
                'role' => 'team_leader'
            ]);
        }

        return redirect()->route('finance.teams.index')
            ->with('success', 'Team đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Team $team)
    {
        $user = Auth::user();

        if (!$user->canViewTeam($team->id)) {
            abort(403, 'Bạn không có quyền xem thông tin team này.');
        }

        $team->load(['leader', 'members', 'ebayAccounts', 'ebayPayouts', 'monthlyExpenses']);

        // Thống kê team
        $stats = [
            'members' => $team->getMemberStats(),
            'finance' => [
                'total_payouts' => $team->total_payouts,
                'total_expenses' => $team->total_expenses,
                'profit' => $team->profit,
                'monthly_payouts' => $team->getMonthlyPayouts(now()->year, now()->month),
                'monthly_expenses' => $team->getMonthlyExpenses(now()->year, now()->month),
            ]
        ];

        // Lấy members theo role
        $members = [
            'leaders' => $team->getTeamLeaders(),
            'sellers' => $team->getSellers(),
            'users' => $team->getUsers()
        ];

        // Recent activities
        $recentPayouts = $team->ebayPayouts()->with(['ebayAccount', 'enteredBy'])
                              ->orderBy('created_at', 'desc')->limit(5)->get();
        $recentExpenses = $team->monthlyExpenses()->with(['createdBy'])
                               ->orderBy('created_at', 'desc')->limit(5)->get();

        return view('finance.teams.show', compact('team', 'stats', 'members', 'recentPayouts', 'recentExpenses'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Team $team)
    {
        $user = Auth::user();

        if (!$user->canManageTeam($team->id)) {
            abort(403, 'Bạn không có quyền chỉnh sửa team này.');
        }

        // Lấy danh sách users có thể làm leader
        $availableLeaders = User::where(function($query) use ($team) {
            $query->where('team_id', $team->id)
                  ->orWhereNotIn('id', function($subQuery) {
                      $subQuery->select('leader_id')->from('teams')->whereNotNull('leader_id');
                  });
        })->get();

        return view('finance.teams.edit', compact('team', 'availableLeaders'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Team $team)
    {
        $user = Auth::user();

        if (!$user->canManageTeam($team->id)) {
            abort(403, 'Bạn không có quyền cập nhật team này.');
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:teams,name,' . $team->id,
            'code' => 'nullable|string|max:50|unique:teams,code,' . $team->id,
            'description' => 'nullable|string',
            'leader_id' => 'nullable|exists:users,id',
            'status' => 'required|in:active,inactive'
        ]);

        // Kiểm tra leader mới có thuộc team này không
        if ($validated['leader_id'] && $validated['leader_id'] != $team->leader_id) {
            $newLeader = User::find($validated['leader_id']);
            if ($newLeader->team_id != $team->id) {
                return back()->withErrors(['leader_id' => 'Leader phải là thành viên của team này.']);
            }
        }

        $oldLeaderId = $team->leader_id;
        $team->update($validated);

        // Cập nhật role cho leader cũ và mới
        if ($oldLeaderId != $validated['leader_id']) {
            // Cập nhật leader cũ
            if ($oldLeaderId) {
                $oldLeader = User::find($oldLeaderId);
                if ($oldLeader) {
                    $oldLeader->update(['role' => 'user']);
                }
            }

            // Cập nhật leader mới
            if ($validated['leader_id']) {
                $newLeader = User::find($validated['leader_id']);
                $newLeader->update(['role' => 'team_leader']);
            }
        }

        return redirect()->route('finance.teams.index')
            ->with('success', 'Team đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Team $team)
    {
        $user = Auth::user();

        if (!$user->isAdmin()) {
            abort(403, 'Chỉ admin mới có thể xóa team.');
        }

        // Kiểm tra team có members không
        if ($team->members()->count() > 0) {
            return back()->withErrors(['error' => 'Không thể xóa team có members. Vui lòng chuyển tất cả members sang team khác trước.']);
        }

        // Kiểm tra team có dữ liệu tài chính không
        if ($team->ebayPayouts()->count() > 0 || $team->monthlyExpenses()->count() > 0) {
            return back()->withErrors(['error' => 'Không thể xóa team có dữ liệu tài chính.']);
        }

        $team->delete();

        return redirect()->route('finance.teams.index')
            ->with('success', 'Team đã được xóa thành công!');
    }

    /**
     * Manage team members
     */
    public function members(Team $team)
    {
        $user = Auth::user();

        if (!$user->canManageTeam($team->id)) {
            abort(403, 'Bạn không có quyền quản lý members của team này.');
        }

        $team->load(['members']);

        // Lấy users không thuộc team nào hoặc thuộc team khác để có thể add
        $availableUsers = User::where('team_id', '!=', $team->id)->get();

        return view('finance.teams.members', compact('team', 'availableUsers'));
    }

    /**
     * Add member to team
     */
    public function addMember(Request $request, Team $team)
    {
        $user = Auth::user();

        if (!$user->canManageTeam($team->id)) {
            abort(403, 'Bạn không có quyền thêm member vào team này.');
        }

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|in:user,seller,team_leader'
        ]);

        try {
            $team->addMember($validated['user_id'], $validated['role']);

            return back()->with('success', 'Member đã được thêm vào team thành công!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Remove member from team
     */
    public function removeMember(Request $request, Team $team, User $member)
    {
        $user = Auth::user();

        if (!$user->canManageTeam($team->id)) {
            abort(403, 'Bạn không có quyền xóa member khỏi team này.');
        }

        try {
            $team->removeMember($member->id);

            return back()->with('success', 'Member đã được xóa khỏi team thành công!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Change team leader
     */
    public function changeLeader(Request $request, Team $team)
    {
        $user = Auth::user();

        if (!$user->canManageTeam($team->id)) {
            abort(403, 'Bạn không có quyền thay đổi leader của team này.');
        }

        $validated = $request->validate([
            'new_leader_id' => 'required|exists:users,id'
        ]);

        try {
            $team->changeLeader($validated['new_leader_id']);

            return back()->with('success', 'Leader đã được thay đổi thành công!');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => $e->getMessage()]);
        }
    }
}
