<?php

namespace Database\Seeders;

use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teams = [
            [
                'name' => 'Team Alpha',
                'code' => 'ALPHA',
                'description' => 'Team chuyên về thời trang và phụ kiện',
                'status' => 'active'
            ],
            [
                'name' => 'Team Beta',
                'code' => 'BETA',
                'description' => 'Team chuyên về điện tử và công nghệ',
                'status' => 'active'
            ],
            [
                'name' => 'Team Gamma',
                'code' => 'GAMMA',
                'description' => 'Team chuyên về gia dụng và nhà bếp',
                'status' => 'active'
            ],
            [
                'name' => 'Team Delta',
                'code' => 'DELTA',
                'description' => 'Team chuyên về sức khỏe và làm đẹp',
                'status' => 'active'
            ]
        ];

        foreach ($teams as $teamData) {
            Team::create($teamData);
        }

        // Tạo default team cho admin
        $adminTeam = Team::create([
            'name' => 'Admin Team',
            'code' => 'ADMIN',
            'description' => 'Team dành cho administrators',
            'status' => 'active'
        ]);

        // Tạo users với roles khác nhau
        $adminUser = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin',
            'team_id' => $adminTeam->id
        ]);

        $teams = Team::all();

        foreach ($teams as $index => $team) {
            // Tạo team leader
            $leader = User::create([
                'name' => 'Leader ' . $team->name,
                'email' => 'leader' . ($index + 1) . '@dropship.com',
                'password' => bcrypt('password'),
                'role' => 'team_leader',
                'team_id' => $team->id
            ]);

            // Cập nhật leader_id cho team
            $team->update(['leader_id' => $leader->id]);

            // Tạo 2-3 sellers cho mỗi team
            for ($i = 1; $i <= 3; $i++) {
                User::create([
                    'name' => 'Seller ' . $i . ' - ' . $team->name,
                    'email' => 'seller' . $i . '.team' . ($index + 1) . '@dropship.com',
                    'password' => bcrypt('password'),
                    'role' => 'seller',
                    'team_id' => $team->id
                ]);
            }
        }
    }
}
