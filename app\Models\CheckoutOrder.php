<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CheckoutOrder extends Model
{
    protected $fillable = [
        'checkout_order_number',
        'checkout_unit_id',
        'order_id',
        'external_order_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'shipping_address',
        'order_value',
        'checkout_fee',
        'shipping_fee',
        'total_amount',
        'status',
        'payment_status',
        'notes',
        'tracking_info',
        'confirmed_at',
        'shipped_at',
        'delivered_at',
        'completed_at'
    ];

    protected $casts = [
        'order_value' => 'decimal:2',
        'checkout_fee' => 'decimal:2',
        'shipping_fee' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'tracking_info' => 'array',
        'confirmed_at' => 'datetime',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    // Tự động tạo checkout order number khi tạo order mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($checkoutOrder) {
            if (empty($checkoutOrder->checkout_order_number)) {
                $checkoutOrder->checkout_order_number = 'CO-' . date('Ymd') . '-' . str_pad(static::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }

    // Relationship với CheckoutUnit
    public function checkoutUnit(): BelongsTo
    {
        return $this->belongsTo(CheckoutUnit::class);
    }

    // Relationship với Order (đơn hàng gốc)
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // Relationship với CheckoutPayments
    public function checkoutPayments(): HasMany
    {
        return $this->hasMany(CheckoutPayment::class);
    }

    // Scope để lấy các order theo status
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Scope để lấy các order theo payment status
    public function scopeByPaymentStatus($query, $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }

    // Scope để lấy các order có sự cố
    public function scopeProblematic($query)
    {
        return $query->whereIn('status', ['cancelled', 'returned', 'refunded']);
    }

    // Scope để lấy các order hoàn thành
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Kiểm tra order có thể hủy không
    public function getCanCancelAttribute()
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    // Kiểm tra order có thể ship không
    public function getCanShipAttribute()
    {
        return $this->status === 'processing';
    }

    // Lấy trạng thái hiển thị
    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Chờ xử lý',
            'confirmed' => 'Đã xác nhận',
            'processing' => 'Đang xử lý',
            'shipped' => 'Đã gửi hàng',
            'delivered' => 'Đã giao hàng',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            'returned' => 'Trả hàng',
            'refunded' => 'Hoàn tiền'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    // Lấy trạng thái thanh toán hiển thị
    public function getPaymentStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Chờ thanh toán',
            'partial' => 'Thanh toán một phần',
            'paid' => 'Đã thanh toán',
            'refunded' => 'Đã hoàn tiền'
        ];

        return $labels[$this->payment_status] ?? $this->payment_status;
    }

    // Tính tổng tiền đã thanh toán cho order này
    public function getTotalPaidAttribute()
    {
        return $this->checkoutPayments()->where('status', 'completed')->sum('amount');
    }

    // Tính tiền còn nợ
    public function getRemainingAmountAttribute()
    {
        return max(0, $this->total_amount - $this->total_paid);
    }

    // Kiểm tra có phải order có sự cố không
    public function getIsProblematicAttribute()
    {
        return in_array($this->status, ['cancelled', 'returned', 'refunded']);
    }
}
