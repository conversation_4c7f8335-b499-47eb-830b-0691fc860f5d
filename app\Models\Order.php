<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    protected $fillable = [
        'order_number',
        'user_id',
        'team_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'shipping_address',
        'billing_address',
        'buyer_id',
        'external_order_id',
        'platform',
        'platform_account',
        'product_link',
        'assigned_to',
        'subtotal',
        'shipping_fee',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'cost_amount',
        'profit_amount',
        'platform_fee',
        'status',
        'payment_status',
        'payment_method',
        'fulfillment_status',
        'notes',
        'tracking_info',
        'images',
        'is_profitable',
        'profit_margin',
        'confirmed_at',
        'shipped_at',
        'delivered_at'
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'shipping_fee' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'cost_amount' => 'decimal:2',
        'profit_amount' => 'decimal:2',
        'platform_fee' => 'decimal:2',
        'profit_margin' => 'decimal:2',
        'tracking_info' => 'array',
        'images' => 'array',
        'is_profitable' => 'boolean',
        'confirmed_at' => 'datetime',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime'
    ];

    // Tự động tạo order number khi tạo order mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (empty($order->order_number)) {
                $order->order_number = 'ORD-' . date('Ymd') . '-' . str_pad(static::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }

    // Relationship với User
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Relationship với Team
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    // Relationship với OrderItems
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    // Relationship với assigned user
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    // Scope để lấy các order theo status
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Scope để lấy các order theo platform
    public function scopeByPlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    // Scope để lấy các order có lợi nhuận
    public function scopeProfitable($query)
    {
        return $query->where('is_profitable', true);
    }

    // Scope để lấy các order theo team
    public function scopeByTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    // Scope để lấy các order được giao cho user
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    // Tính toán lợi nhuận tự động
    public function calculateProfit()
    {
        $this->cost_amount = $this->orderItems->sum(function ($item) {
            return $item->product ? $item->product->cost_price * $item->quantity : 0;
        });

        $this->profit_amount = $this->total_amount - $this->cost_amount - $this->platform_fee;

        if ($this->cost_amount > 0) {
            $this->profit_margin = ($this->profit_amount / $this->cost_amount) * 100;
        }

        $this->is_profitable = $this->profit_amount > 0;
        $this->save();
    }

    // Get status badge color
    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'pending' => 'warning',
            'confirmed' => 'info',
            'processing' => 'primary',
            'shipped' => 'success',
            'delivered' => 'success',
            'cancelled' => 'danger',
            'returned' => 'secondary',
            default => 'secondary'
        };
    }

    // Get platform icon
    public function getPlatformIconAttribute()
    {
        return match($this->platform) {
            'ebay' => 'fab fa-ebay',
            'amazon' => 'fab fa-amazon',
            'shopify' => 'fab fa-shopify',
            'facebook' => 'fab fa-facebook',
            'instagram' => 'fab fa-instagram',
            default => 'fas fa-shopping-cart'
        };
    }



    // Scope để lấy các order theo payment status
    public function scopeByPaymentStatus($query, $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }

    // Tính tổng số items trong order
    public function getTotalItemsAttribute()
    {
        return $this->orderItems()->sum('quantity');
    }

    // Kiểm tra order có thể hủy không
    public function getCanCancelAttribute()
    {
        return in_array($this->status, ['pending', 'confirmed']);
    }

    // Kiểm tra order có thể ship không
    public function getCanShipAttribute()
    {
        return $this->status === 'processing' && $this->payment_status === 'paid';
    }

    // Lấy trạng thái hiển thị
    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Chờ xử lý',
            'confirmed' => 'Đã xác nhận',
            'processing' => 'Đang xử lý',
            'shipped' => 'Đã gửi hàng',
            'delivered' => 'Đã giao hàng',
            'cancelled' => 'Đã hủy'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    // Lấy trạng thái thanh toán hiển thị
    public function getPaymentStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Chờ thanh toán',
            'paid' => 'Đã thanh toán',
            'failed' => 'Thanh toán thất bại',
            'refunded' => 'Đã hoàn tiền'
        ];

        return $labels[$this->payment_status] ?? $this->payment_status;
    }
}
