<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Tạo user admin mặc định
        User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
        ]);

        // Chạy các seeders
        $this->call([
            CategorySeeder::class,
            SupplierSeeder::class,
            ProductSeeder::class,
            CheckoutUnitSeeder::class,
            CheckoutOrderSeeder::class,
        ]);
    }
}
