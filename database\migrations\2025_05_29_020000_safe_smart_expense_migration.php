<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ki<PERSON>m tra và thêm từng column một cách an toàn
        Schema::table('monthly_expenses', function (Blueprint $table) {
            // 1. Thêm category column nếu chưa có
            if (!Schema::hasColumn('monthly_expenses', 'category')) {
                $table->string('category')->default('other')->after('status');
            }
        });

        Schema::table('monthly_expenses', function (Blueprint $table) {
            // 2. Thêm vendor column
            if (!Schema::hasColumn('monthly_expenses', 'vendor')) {
                $table->string('vendor')->nullable()->after('description');
            }
        });

        Schema::table('monthly_expenses', function (Blueprint $table) {
            // 3. Smart categorization fields
            if (!Schema::hasColumn('monthly_expenses', 'suggested_category')) {
                $table->string('suggested_category')->nullable()->after('category');
            }
            if (!Schema::hasColumn('monthly_expenses', 'categorization_confidence')) {
                $table->decimal('categorization_confidence', 5, 2)->nullable()->after('suggested_category');
            }
            if (!Schema::hasColumn('monthly_expenses', 'category_manually_corrected')) {
                $table->boolean('category_manually_corrected')->default(false)->after('categorization_confidence');
            }
        });

        Schema::table('monthly_expenses', function (Blueprint $table) {
            // 4. Validation and fraud detection fields
            if (!Schema::hasColumn('monthly_expenses', 'validation_results')) {
                $table->json('validation_results')->nullable()->after('category_manually_corrected');
            }
            if (!Schema::hasColumn('monthly_expenses', 'fraud_score')) {
                $table->decimal('fraud_score', 5, 2)->default(0)->after('validation_results');
            }
            if (!Schema::hasColumn('monthly_expenses', 'risk_level')) {
                $table->string('risk_level')->default('low')->after('fraud_score');
            }
            if (!Schema::hasColumn('monthly_expenses', 'fraud_indicators')) {
                $table->json('fraud_indicators')->nullable()->after('risk_level');
            }
        });

        Schema::table('monthly_expenses', function (Blueprint $table) {
            // 5. Approval workflow fields
            if (!Schema::hasColumn('monthly_expenses', 'approval_status')) {
                $table->string('approval_status')->default('pending')->after('status');
            }
            if (!Schema::hasColumn('monthly_expenses', 'approval_path')) {
                $table->json('approval_path')->nullable()->after('approval_status');
            }
            if (!Schema::hasColumn('monthly_expenses', 'approval_history')) {
                $table->json('approval_history')->nullable()->after('approval_path');
            }
        });

        Schema::table('monthly_expenses', function (Blueprint $table) {
            // 6. Additional fields
            if (!Schema::hasColumn('monthly_expenses', 'approval_comments')) {
                $table->text('approval_comments')->nullable()->after('approved_by');
            }
            if (!Schema::hasColumn('monthly_expenses', 'processed_at')) {
                $table->timestamp('processed_at')->nullable()->after('approval_comments');
            }
        });

        // 7. Add indexes for performance (với error handling)
        try {
            if (!$this->indexExists('monthly_expenses', 'idx_approval_status_created_at')) {
                Schema::table('monthly_expenses', function (Blueprint $table) {
                    $table->index(['approval_status', 'created_at'], 'idx_approval_status_created_at');
                });
            }
        } catch (Exception $e) {
            // Index already exists, continue
        }

        try {
            if (!$this->indexExists('monthly_expenses', 'idx_risk_level_fraud_score')) {
                Schema::table('monthly_expenses', function (Blueprint $table) {
                    $table->index(['risk_level', 'fraud_score'], 'idx_risk_level_fraud_score');
                });
            }
        } catch (Exception $e) {
            // Index already exists, continue
        }

        try {
            if (!$this->indexExists('monthly_expenses', 'idx_category_team_id_created_at')) {
                Schema::table('monthly_expenses', function (Blueprint $table) {
                    $table->index(['category', 'team_id', 'created_at'], 'idx_category_team_id_created_at');
                });
            }
        } catch (Exception $e) {
            // Index already exists, continue
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monthly_expenses', function (Blueprint $table) {
            // Drop indexes first
            try {
                $table->dropIndex('idx_approval_status_created_at');
            } catch (Exception $e) {}
            
            try {
                $table->dropIndex('idx_risk_level_fraud_score');
            } catch (Exception $e) {}
            
            try {
                $table->dropIndex('idx_category_team_id_created_at');
            } catch (Exception $e) {}

            // Drop columns
            $columns = [
                'vendor',
                'suggested_category',
                'categorization_confidence',
                'category_manually_corrected',
                'validation_results',
                'fraud_score',
                'risk_level',
                'fraud_indicators',
                'approval_status',
                'approval_path',
                'approval_history',
                'approval_comments',
                'processed_at',
                'category'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('monthly_expenses', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }

    /**
     * Check if index exists
     */
    private function indexExists($table, $indexName)
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
            return count($indexes) > 0;
        } catch (Exception $e) {
            return false;
        }
    }
};
