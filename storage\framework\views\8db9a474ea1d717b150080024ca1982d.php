<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-envelope me-1"></i>
        <PERSON><PERSON><PERSON> hình SMTP
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_host" class="form-label">SMTP Host</label>
                <input type="text" 
                       class="form-control <?php $__errorArgs = ['smtp_host'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="smtp_host" 
                       name="smtp_host" 
                       value="<?php echo e(old('smtp_host', $values['smtp_host'] ?? '')); ?>"
                       placeholder="smtp.gmail.com">
                <div class="form-text">Địa chỉ SMTP server (VD: smtp.gmail.com, smtp.outlook.com)</div>
                <?php $__errorArgs = ['smtp_host'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_port" class="form-label">SMTP Port</label>
                <select class="form-select <?php $__errorArgs = ['smtp_port'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        id="smtp_port" 
                        name="smtp_port">
                    <?php
                        $ports = [
                            '587' => '587 (TLS - Recommended)',
                            '465' => '465 (SSL)',
                            '25' => '25 (Non-encrypted)',
                            '2525' => '2525 (Alternative)'
                        ];
                        $currentPort = old('smtp_port', $values['smtp_port'] ?? '587');
                    ?>
                    <?php $__currentLoopData = $ports; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($value); ?>" <?php echo e($currentPort == $value ? 'selected' : ''); ?>>
                            <?php echo e($label); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <div class="form-text">Port kết nối SMTP (thường là 587 cho TLS)</div>
                <?php $__errorArgs = ['smtp_port'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_username" class="form-label">
                    SMTP Username
                    <span class="badge bg-warning text-dark ms-1">Encrypted</span>
                </label>
                <div class="input-group">
                    <input type="email" 
                           class="form-control <?php $__errorArgs = ['smtp_username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="smtp_username" 
                           name="smtp_username" 
                           value="<?php echo e(old('smtp_username', $values['smtp_username'] ? '••••••••••••••••' : '')); ?>"
                           placeholder="<EMAIL>">
                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('smtp_username')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-text">Email đăng nhập SMTP</div>
                <?php $__errorArgs = ['smtp_username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_password" class="form-label">
                    SMTP Password
                    <span class="badge bg-warning text-dark ms-1">Encrypted</span>
                </label>
                <div class="input-group">
                    <input type="password" 
                           class="form-control <?php $__errorArgs = ['smtp_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="smtp_password" 
                           name="smtp_password" 
                           value="<?php echo e(old('smtp_password', $values['smtp_password'] ? '••••••••••••••••' : '')); ?>"
                           placeholder="App Password hoặc mật khẩu email">
                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('smtp_password')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-text">Mật khẩu hoặc App Password cho SMTP</div>
                <?php $__errorArgs = ['smtp_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="setting-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">Test kết nối SMTP</h6>
                        <small class="text-muted">Kiểm tra cấu hình SMTP có hoạt động không</small>
                    </div>
                    <button type="button" class="btn btn-outline-primary" onclick="testApi('smtp', this)">
                        <i class="fas fa-paper-plane me-1"></i>
                        Test SMTP
                    </button>
                </div>
                <div class="test-result mt-2" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-clock me-1"></i>
        Tần suất thông báo
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="notification_frequency" class="form-label">
                    Tần suất gửi thông báo <span class="text-danger">*</span>
                </label>
                <select class="form-select <?php $__errorArgs = ['notification_frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        id="notification_frequency" 
                        name="notification_frequency" 
                        required>
                    <?php
                        $frequencies = [
                            'immediate' => 'Ngay lập tức',
                            'hourly' => 'Mỗi giờ',
                            'daily' => 'Hàng ngày (9:00 AM)',
                            'weekly' => 'Hàng tuần (Thứ 2, 9:00 AM)'
                        ];
                        $currentFrequency = old('notification_frequency', $values['notification_frequency'] ?? 'immediate');
                    ?>
                    <?php $__currentLoopData = $frequencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($value); ?>" <?php echo e($currentFrequency === $value ? 'selected' : ''); ?>>
                            <?php echo e($label); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <div class="form-text">Tần suất gửi email notifications cho users</div>
                <?php $__errorArgs = ['notification_frequency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label class="form-label">Mô tả tần suất</label>
                <div class="card bg-light">
                    <div class="card-body">
                        <div id="frequencyDescription">
                            <?php
                                $descriptions = [
                                    'immediate' => 'Gửi email ngay khi có thông báo mới. Phù hợp cho môi trường cần phản hồi nhanh.',
                                    'hourly' => 'Gửi email tổng hợp mỗi giờ. Giảm spam email nhưng vẫn kịp thời.',
                                    'daily' => 'Gửi email tổng hợp hàng ngày lúc 9:00 AM. Phù hợp cho thông báo không khẩn cấp.',
                                    'weekly' => 'Gửi email tổng hợp hàng tuần vào thứ 2. Phù hợp cho báo cáo định kỳ.'
                                ];
                                $currentDesc = $descriptions[$currentFrequency] ?? $descriptions['immediate'];
                            ?>
                            <small><?php echo e($currentDesc); ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-cog me-1"></i>
        Cấu hình nâng cao
    </h6>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Hướng dẫn cấu hình SMTP</h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="smtpGuideAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gmailGuide">
                                    Gmail SMTP
                                </button>
                            </h2>
                            <div id="gmailGuide" class="accordion-collapse collapse" data-bs-parent="#smtpGuideAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Cấu hình:</h6>
                                            <ul class="small">
                                                <li><strong>Host:</strong> smtp.gmail.com</li>
                                                <li><strong>Port:</strong> 587 (TLS)</li>
                                                <li><strong>Username:</strong> <EMAIL></li>
                                                <li><strong>Password:</strong> App Password</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Cách tạo App Password:</h6>
                                            <ol class="small">
                                                <li>Vào Google Account settings</li>
                                                <li>Bật 2-Step Verification</li>
                                                <li>Tạo App Password cho "Mail"</li>
                                                <li>Sử dụng App Password thay vì mật khẩu thường</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#outlookGuide">
                                    Outlook/Hotmail SMTP
                                </button>
                            </h2>
                            <div id="outlookGuide" class="accordion-collapse collapse" data-bs-parent="#smtpGuideAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Cấu hình:</h6>
                                            <ul class="small">
                                                <li><strong>Host:</strong> smtp-mail.outlook.com</li>
                                                <li><strong>Port:</strong> 587 (TLS)</li>
                                                <li><strong>Username:</strong> <EMAIL></li>
                                                <li><strong>Password:</strong> Mật khẩu email</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Lưu ý:</h6>
                                            <ul class="small">
                                                <li>Có thể cần bật "Less secure app access"</li>
                                                <li>Hoặc sử dụng App Password nếu có 2FA</li>
                                                <li>Kiểm tra spam folder nếu không nhận được email</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#customGuide">
                                    Custom SMTP Server
                                </button>
                            </h2>
                            <div id="customGuide" class="accordion-collapse collapse" data-bs-parent="#smtpGuideAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Thông tin cần thiết:</h6>
                                            <ul class="small">
                                                <li>SMTP server hostname</li>
                                                <li>Port number (thường 587, 465, hoặc 25)</li>
                                                <li>Authentication method</li>
                                                <li>Username và password</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Kiểm tra:</h6>
                                            <ul class="small">
                                                <li>Firewall có block port không</li>
                                                <li>SSL/TLS certificate hợp lệ</li>
                                                <li>Authentication credentials đúng</li>
                                                <li>Rate limiting của provider</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-chart-pie me-1"></i>
        Thống kê thông báo
    </h6>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h6 class="card-title text-primary">SMTP Status</h6>
                    <p class="card-text">
                        <?php if(!empty($values['smtp_host']) && !empty($values['smtp_username'])): ?>
                            <span class="badge bg-success">Đã cấu hình</span>
                        <?php else: ?>
                            <span class="badge bg-secondary">Chưa cấu hình</span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h6 class="card-title text-info">Tần suất gửi</h6>
                    <p class="card-text">
                        <strong><?php echo e($frequencies[$values['notification_frequency'] ?? 'immediate'] ?? 'Ngay lập tức'); ?></strong>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h6 class="card-title text-success">Email hôm nay</h6>
                    <p class="card-text">
                        <strong>0</strong><br>
                        <small class="text-muted">Emails đã gửi</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update frequency description when selection changes
document.getElementById('notification_frequency').addEventListener('change', function() {
    const descriptions = {
        'immediate': 'Gửi email ngay khi có thông báo mới. Phù hợp cho môi trường cần phản hồi nhanh.',
        'hourly': 'Gửi email tổng hợp mỗi giờ. Giảm spam email nhưng vẫn kịp thời.',
        'daily': 'Gửi email tổng hợp hàng ngày lúc 9:00 AM. Phù hợp cho thông báo không khẩn cấp.',
        'weekly': 'Gửi email tổng hợp hàng tuần vào thứ 2. Phù hợp cho báo cáo định kỳ.'
    };
    
    const selectedValue = this.value;
    const description = descriptions[selectedValue] || descriptions['immediate'];
    
    document.getElementById('frequencyDescription').innerHTML = `<small>${description}</small>`;
});

// Clear encrypted fields when focused
document.querySelectorAll('#smtp_username, #smtp_password').forEach(function(input) {
    input.addEventListener('focus', function() {
        if (this.value === '••••••••••••••••') {
            this.value = '';
            this.type = 'text';
        }
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
<?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/admin/settings/partials/notification.blade.php ENDPATH**/ ?>