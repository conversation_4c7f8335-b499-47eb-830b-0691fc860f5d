<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display notifications for current user
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        $query = Notification::where('user_id', $user->id)
                            ->orderBy('created_at', 'desc');

        // Filter by read status
        if ($request->filled('status')) {
            if ($request->status === 'unread') {
                $query->unread();
            } elseif ($request->status === 'read') {
                $query->whereNotNull('read_at');
            }
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->byType($request->type);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->byPriority($request->priority);
        }

        $notifications = $query->paginate(20);

        // Get stats
        $stats = [
            'total' => Notification::where('user_id', $user->id)->count(),
            'unread' => $this->notificationService->getUnreadCount($user->id),
            'high_priority' => Notification::where('user_id', $user->id)
                                         ->whereIn('priority', ['high', 'urgent'])
                                         ->unread()
                                         ->count()
        ];

        return view('notifications.index', compact('notifications', 'stats'));
    }

    /**
     * Get notifications for header dropdown (AJAX)
     */
    public function getRecent()
    {
        $user = Auth::user();
        $notifications = $this->notificationService->getRecentNotifications($user->id, 5);
        $unreadCount = $this->notificationService->getUnreadCount($user->id);

        return response()->json([
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'time_ago' => $notification->time_ago,
                    'priority' => $notification->priority,
                    'priority_color' => $notification->priority_color,
                    'is_read' => $notification->isRead(),
                    'action_url' => $notification->action_url
                ];
            }),
            'unread_count' => $unreadCount
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, $id)
    {
        $user = Auth::user();
        $success = $this->notificationService->markAsRead($id, $user->id);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => $success,
                'unread_count' => $this->notificationService->getUnreadCount($user->id)
            ]);
        }

        if ($success) {
            return back()->with('success', 'Notification đã được đánh dấu là đã đọc.');
        }

        return back()->with('error', 'Không thể đánh dấu notification.');
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(Request $request)
    {
        $user = Auth::user();
        $success = $this->notificationService->markAllAsRead($user->id);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => $success,
                'unread_count' => 0
            ]);
        }

        if ($success) {
            return back()->with('success', 'Tất cả notifications đã được đánh dấu là đã đọc.');
        }

        return back()->with('error', 'Không thể đánh dấu tất cả notifications.');
    }

    /**
     * Delete notification
     */
    public function destroy($id)
    {
        $user = Auth::user();

        $notification = Notification::where('id', $id)
                                  ->where('user_id', $user->id)
                                  ->first();

        if (!$notification) {
            return back()->with('error', 'Notification không tồn tại.');
        }

        $notification->delete();

        return back()->with('success', 'Notification đã được xóa.');
    }

    /**
     * Get notification count for user (AJAX)
     */
    public function getCount()
    {
        $user = Auth::user();
        $unreadCount = $this->notificationService->getUnreadCount($user->id);

        return response()->json([
            'unread_count' => $unreadCount
        ]);
    }
}
