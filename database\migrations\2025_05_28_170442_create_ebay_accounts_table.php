<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ebay_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('account_name'); // Tên account eBay
            $table->string('email')->unique(); // Email account
            $table->string('ebay_user_id')->nullable(); // eBay User ID
            $table->foreignId('team_id')->constrained()->onDelete('cascade'); // Team sở hữu
            $table->foreignId('seller_id')->nullable()->constrained('users')->onDelete('set null'); // Seller quản lý
            $table->enum('status', ['active', 'suspended', 'inactive'])->default('active'); // Trạng thái
            $table->enum('payout_schedule', ['weekly', 'bi_weekly', 'monthly'])->default('weekly'); // Lịch payout
            $table->date('last_payout_date')->nullable(); // Ngày payout cuối cùng
            $table->decimal('total_payouts', 15, 2)->default(0); // Tổng payout đã nhận
            $table->text('notes')->nullable(); // Ghi chú
            $table->json('settings')->nullable(); // Cài đặt account (JSON)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ebay_accounts');
    }
};
