<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Services\SettingsService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SettingsController extends Controller
{
    protected $settingsService;
    protected $notificationService;

    public function __construct(SettingsService $settingsService, NotificationService $notificationService)
    {
        $this->settingsService = $settingsService;
        $this->notificationService = $notificationService;
    }

    /**
     * Display settings page
     */
    public function index(Request $request)
    {
        $activeTab = $request->get('tab', 'general');

        // Get settings grouped by category
        $settingsGroups = [
            'general' => Setting::byGroup('general')->get(),
            'finance' => Setting::byGroup('finance')->get(),
            'notification' => Setting::byGroup('notification')->get(),
            'api' => Setting::byGroup('api')->get(),
            'checkout' => Setting::byGroup('checkout')->get(),
            'team' => Setting::byGroup('team')->get(),
        ];

        // Get current values
        $currentValues = [];
        foreach ($settingsGroups as $group => $settings) {
            foreach ($settings as $setting) {
                $currentValues[$setting->key] = $setting->processed_value;
            }
        }

        return view('admin.settings.index', compact('settingsGroups', 'currentValues', 'activeTab'));
    }

    /**
     * Update settings
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $settings = $request->except(['_token', '_method', 'tab']);

        // Update settings
        $result = $this->settingsService->updateMultiple($settings, $user->id);

        if ($result['success']) {
            // Send notification to other admins about settings change
            $this->notificationService->createForAdmins(
                'settings_updated',
                'Cấu hình hệ thống đã được cập nhật',
                "Cấu hình hệ thống đã được cập nhật bởi {$user->name}. " . count($result['updated']) . " settings đã được thay đổi.",
                [
                    'updated_settings' => $result['updated'],
                    'updated_by' => $user->name
                ],
                'medium',
                route('admin.settings.index')
            );

            $message = 'Cấu hình đã được cập nhật thành công!';
            if (!empty($result['errors'])) {
                $message .= ' Tuy nhiên có một số lỗi: ' . implode(', ', array_keys($result['errors']));
            }

            return back()->with('success', $message)->with('tab', $request->get('tab', 'general'));
        }

        return back()->withErrors($result['errors'])->with('tab', $request->get('tab', 'general'));
    }

    /**
     * Test API connection
     */
    public function testApi(Request $request)
    {
        $apiType = $request->get('api_type');

        if (!$apiType) {
            return response()->json([
                'success' => false,
                'message' => 'API type is required'
            ]);
        }

        $result = $this->settingsService->testApiConnection($apiType);

        return response()->json($result);
    }

    /**
     * Export settings
     */
    public function export()
    {
        try {
            $settings = $this->settingsService->exportSettings();
            $filename = 'dropship-manager-settings-' . now()->format('Y-m-d-H-i-s') . '.json';

            return response()->json($settings)
                ->header('Content-Type', 'application/json')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');

        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi export settings: ' . $e->getMessage());
        }
    }

    /**
     * Import settings
     */
    public function import(Request $request)
    {
        $request->validate([
            'settings_file' => 'required|file|mimes:json|max:2048'
        ]);

        try {
            $file = $request->file('settings_file');
            $content = file_get_contents($file->getPathname());
            $data = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('File JSON không hợp lệ');
            }

            $result = $this->settingsService->importSettings($data, Auth::id());

            if ($result['success']) {
                // Send notification about settings import
                $this->notificationService->createForAdmins(
                    'settings_imported',
                    'Cấu hình hệ thống đã được import',
                    "Cấu hình hệ thống đã được import bởi " . Auth::user()->name . ". " . $result['imported'] . " settings đã được cập nhật.",
                    [
                        'imported_count' => $result['imported'],
                        'imported_by' => Auth::user()->name
                    ],
                    'high',
                    route('admin.settings.index')
                );

                $message = "Import thành công! Đã cập nhật {$result['imported']} settings.";
                if (!empty($result['errors'])) {
                    $message .= ' Có một số lỗi: ' . implode(', ', $result['errors']);
                }

                return back()->with('success', $message);
            }

            return back()->with('error', 'Import thất bại: ' . implode(', ', $result['errors']));

        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi import settings: ' . $e->getMessage());
        }
    }

    /**
     * Reset settings to default
     */
    public function resetToDefaults(Request $request)
    {
        $group = $request->get('group');

        try {
            $resetCount = $this->settingsService->resetToDefaults($group, Auth::id());

            // Send notification about settings reset
            $this->notificationService->createForAdmins(
                'settings_reset',
                'Cấu hình hệ thống đã được reset',
                "Cấu hình hệ thống " . ($group ? "nhóm {$group}" : "toàn bộ") . " đã được reset về mặc định bởi " . Auth::user()->name . ". {$resetCount} settings đã được reset.",
                [
                    'reset_group' => $group,
                    'reset_count' => $resetCount,
                    'reset_by' => Auth::user()->name
                ],
                'high',
                route('admin.settings.index')
            );

            $message = $group
                ? "Đã reset {$resetCount} settings trong nhóm {$group} về giá trị mặc định!"
                : "Đã reset {$resetCount} settings về giá trị mặc định!";

            return back()->with('success', $message)->with('tab', $group ?: 'general');

        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi reset settings: ' . $e->getMessage());
        }
    }

    /**
     * Initialize default settings
     */
    public function initializeDefaults()
    {
        try {
            $this->settingsService->initializeDefaults();

            return back()->with('success', 'Đã khởi tạo settings mặc định thành công!');

        } catch (\Exception $e) {
            return back()->with('error', 'Lỗi khởi tạo settings: ' . $e->getMessage());
        }
    }

    /**
     * Clear settings cache
     */
    public function clearCache()
    {
        try {
            Setting::clearCache();

            return response()->json([
                'success' => true,
                'message' => 'Cache đã được xóa thành công!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi xóa cache: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get setting value (AJAX)
     */
    public function getValue(Request $request)
    {
        $key = $request->get('key');

        if (!$key) {
            return response()->json([
                'success' => false,
                'message' => 'Setting key is required'
            ]);
        }

        $value = $this->settingsService->get($key);

        return response()->json([
            'success' => true,
            'key' => $key,
            'value' => $value
        ]);
    }

    /**
     * Set setting value (AJAX)
     */
    public function setValue(Request $request)
    {
        $key = $request->get('key');
        $value = $request->get('value');

        if (!$key) {
            return response()->json([
                'success' => false,
                'message' => 'Setting key is required'
            ]);
        }

        try {
            $this->settingsService->set($key, $value, Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'Setting đã được cập nhật thành công!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi cập nhật setting: ' . $e->getMessage()
            ]);
        }
    }
}
