<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-users me-1"></i>
        G<PERSON>ớ<PERSON> hạn thành viên
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="max_members_per_team" class="form-label">
                    Số lượng members tối đa per team <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                    <input type="number" 
                           class="form-control @error('max_members_per_team') is-invalid @enderror" 
                           id="max_members_per_team" 
                           name="max_members_per_team" 
                           value="{{ old('max_members_per_team', $values['max_members_per_team'] ?? '50') }}"
                           min="1"
                           max="1000"
                           required>
                    <span class="input-group-text">members</span>
                </div>
                <div class="form-text">Giới hạn số lượng thành viên tối đa trong một team</div>
                @error('max_members_per_team')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label class="form-label">Thống kê hiện tại</label>
                <div class="card bg-light">
                    <div class="card-body">
                        @php
                            $totalTeams = \App\Models\Team::count();
                            $totalMembers = \App\Models\User::whereNotNull('team_id')->count();
                            $avgMembersPerTeam = $totalTeams > 0 ? round($totalMembers / $totalTeams, 1) : 0;
                            $maxMembersLimit = $values['max_members_per_team'] ?? 50;
                        @endphp
                        <small>
                            <strong>Tổng số teams:</strong> {{ $totalTeams }}<br>
                            <strong>Tổng số members:</strong> {{ $totalMembers }}<br>
                            <strong>Trung bình:</strong> {{ $avgMembersPerTeam }} members/team<br>
                            <strong>Giới hạn hiện tại:</strong> {{ $maxMembersLimit }} members/team
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-user-tag me-1"></i>
        Quyền mặc định
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="default_user_role" class="form-label">
                    Role mặc định cho user mới <span class="text-danger">*</span>
                </label>
                <select class="form-select @error('default_user_role') is-invalid @enderror" 
                        id="default_user_role" 
                        name="default_user_role" 
                        required>
                    @php
                        $roles = [
                            'user' => 'User - Quyền cơ bản',
                            'seller' => 'Seller - Quyền bán hàng',
                        ];
                        $currentRole = old('default_user_role', $values['default_user_role'] ?? 'user');
                    @endphp
                    @foreach($roles as $value => $label)
                        <option value="{{ $value }}" {{ $currentRole === $value ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
                <div class="form-text">Role được gán tự động cho user mới khi đăng ký</div>
                @error('default_user_role')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label class="form-label">Mô tả quyền</label>
                <div class="card bg-light">
                    <div class="card-body">
                        <div id="roleDescription">
                            @php
                                $roleDescriptions = [
                                    'user' => 'Quyền xem thông tin cơ bản, không thể tạo/sửa dữ liệu tài chính.',
                                    'seller' => 'Quyền tạo và quản lý đơn hàng, xem báo cáo của team.',
                                ];
                                $currentDesc = $roleDescriptions[$currentRole] ?? $roleDescriptions['user'];
                            @endphp
                            <small>{{ $currentDesc }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-chart-bar me-1"></i>
        Thống kê teams
    </h6>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h6 class="card-title text-primary">Tổng teams</h6>
                    <p class="card-text">
                        <strong class="h5">{{ $totalTeams }}</strong><br>
                        <small class="text-muted">Đang hoạt động</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h6 class="card-title text-success">Tổng members</h6>
                    <p class="card-text">
                        <strong class="h5">{{ $totalMembers }}</strong><br>
                        <small class="text-muted">Trong teams</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h6 class="card-title text-info">Team leaders</h6>
                    <p class="card-text">
                        @php
                            $teamLeaders = \App\Models\User::where('role', 'team_leader')->count();
                        @endphp
                        <strong class="h5">{{ $teamLeaders }}</strong><br>
                        <small class="text-muted">Đang quản lý</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h6 class="card-title text-warning">Trung bình</h6>
                    <p class="card-text">
                        <strong class="h5">{{ $avgMembersPerTeam }}</strong><br>
                        <small class="text-muted">Members/team</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-list me-1"></i>
        Danh sách teams
    </h6>
    
    <div class="row">
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Team</th>
                            <th>Leader</th>
                            <th>Members</th>
                            <th>Giới hạn</th>
                            <th>Trạng thái</th>
                            <th>Hoạt động cuối</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $teams = \App\Models\Team::with(['leader', 'members'])->get();
                        @endphp
                        @forelse($teams as $team)
                        <tr>
                            <td>
                                <strong>{{ $team->name }}</strong><br>
                                <small class="text-muted">{{ $team->description }}</small>
                            </td>
                            <td>
                                @if($team->leader)
                                    {{ $team->leader->name }}<br>
                                    <small class="text-muted">{{ $team->leader->email }}</small>
                                @else
                                    <span class="text-muted">Chưa có leader</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ $team->members->count() }}</span>
                            </td>
                            <td>
                                @php
                                    $memberCount = $team->members->count();
                                    $maxMembers = $values['max_members_per_team'] ?? 50;
                                    $percentage = $maxMembers > 0 ? ($memberCount / $maxMembers) * 100 : 0;
                                @endphp
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar 
                                        @if($percentage < 50) bg-success
                                        @elseif($percentage < 80) bg-warning
                                        @else bg-danger
                                        @endif" 
                                        role="progressbar" 
                                        style="width: {{ min($percentage, 100) }}%">
                                        {{ round($percentage, 1) }}%
                                    </div>
                                </div>
                                <small class="text-muted">{{ $memberCount }}/{{ $maxMembers }}</small>
                            </td>
                            <td>
                                @if($team->status === 'active')
                                    <span class="badge bg-success">Hoạt động</span>
                                @else
                                    <span class="badge bg-secondary">{{ ucfirst($team->status) }}</span>
                                @endif
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ $team->updated_at->diffForHumans() }}
                                </small>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                Chưa có team nào được tạo
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-tools me-1"></i>
        Công cụ quản lý teams
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Kiểm tra giới hạn</h6>
                </div>
                <div class="card-body">
                    <p class="card-text">Kiểm tra teams nào đang vượt quá giới hạn members</p>
                    <button type="button" class="btn btn-warning" onclick="checkTeamLimits()">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Kiểm tra giới hạn
                    </button>
                    <div id="limitCheckResults" class="mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Phân bố roles</h6>
                </div>
                <div class="card-body">
                    @php
                        $roleStats = \App\Models\User::selectRaw('role, COUNT(*) as count')
                                                   ->groupBy('role')
                                                   ->pluck('count', 'role')
                                                   ->toArray();
                    @endphp
                    <div class="role-distribution">
                        @foreach(['admin', 'team_leader', 'seller', 'user'] as $role)
                            @php
                                $count = $roleStats[$role] ?? 0;
                                $percentage = $totalMembers > 0 ? ($count / ($totalMembers + ($roleStats['admin'] ?? 0))) * 100 : 0;
                            @endphp
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="text-capitalize">{{ str_replace('_', ' ', $role) }}:</span>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-2" style="width: 100px; height: 15px;">
                                        <div class="progress-bar bg-primary" style="width: {{ $percentage }}%"></div>
                                    </div>
                                    <span class="badge bg-secondary">{{ $count }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-cogs me-1"></i>
        Cấu hình nâng cao
    </h6>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Quy tắc và hướng dẫn</h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="teamRulesAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#memberLimits">
                                    Giới hạn thành viên
                                </button>
                            </h2>
                            <div id="memberLimits" class="accordion-collapse collapse" data-bs-parent="#teamRulesAccordion">
                                <div class="accordion-body">
                                    <ul class="small">
                                        <li>Mỗi team có thể có tối đa <strong>{{ $values['max_members_per_team'] ?? 50 }}</strong> thành viên</li>
                                        <li>Team leader không tính vào giới hạn này</li>
                                        <li>Admin có thể vượt quá giới hạn nếu cần thiết</li>
                                        <li>Khi đạt giới hạn, không thể thêm thành viên mới</li>
                                        <li>Có thể tăng giới hạn bằng cách thay đổi setting này</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#rolePermissions">
                                    Phân quyền roles
                                </button>
                            </h2>
                            <div id="rolePermissions" class="accordion-collapse collapse" data-bs-parent="#teamRulesAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>User ({{ $values['default_user_role'] === 'user' ? 'Mặc định' : '' }}):</h6>
                                            <ul class="small">
                                                <li>Xem thông tin cơ bản</li>
                                                <li>Xem báo cáo của team</li>
                                                <li>Không thể tạo/sửa dữ liệu tài chính</li>
                                                <li>Không thể quản lý team</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Seller ({{ $values['default_user_role'] === 'seller' ? 'Mặc định' : '' }}):</h6>
                                            <ul class="small">
                                                <li>Tất cả quyền của User</li>
                                                <li>Tạo và quản lý đơn hàng</li>
                                                <li>Tạo payouts và expenses</li>
                                                <li>Xem báo cáo chi tiết</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#teamManagement">
                                    Quản lý teams
                                </button>
                            </h2>
                            <div id="teamManagement" class="accordion-collapse collapse" data-bs-parent="#teamRulesAccordion">
                                <div class="accordion-body">
                                    <ul class="small">
                                        <li>Mỗi user chỉ có thể thuộc về một team</li>
                                        <li>Team leader phải là thành viên của team đó</li>
                                        <li>Team leader có thể quản lý tài chính của team</li>
                                        <li>Admin có thể truy cập tất cả teams</li>
                                        <li>Khi xóa team, cần chuyển members sang team khác</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update role description when selection changes
document.getElementById('default_user_role').addEventListener('change', function() {
    const descriptions = {
        'user': 'Quyền xem thông tin cơ bản, không thể tạo/sửa dữ liệu tài chính.',
        'seller': 'Quyền tạo và quản lý đơn hàng, xem báo cáo của team.',
    };
    
    const selectedValue = this.value;
    const description = descriptions[selectedValue] || descriptions['user'];
    
    document.getElementById('roleDescription').innerHTML = `<small>${description}</small>`;
});

function checkTeamLimits() {
    const button = event.target;
    const originalText = button.innerHTML;
    const resultsDiv = document.getElementById('limitCheckResults');
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Đang kiểm tra...';
    button.disabled = true;
    resultsDiv.style.display = 'block';
    
    // Simulate checking (in real implementation, would make AJAX call)
    setTimeout(function() {
        const maxMembers = parseInt(document.getElementById('max_members_per_team').value) || 50;
        const teams = @json($teams->map(function($team) {
            return [
                'name' => $team->name,
                'member_count' => $team->members->count()
            ];
        }));
        
        const overLimitTeams = teams.filter(team => team.member_count > maxMembers);
        
        let html = '';
        if (overLimitTeams.length === 0) {
            html = '<div class="alert alert-success"><i class="fas fa-check me-1"></i> Tất cả teams đều trong giới hạn cho phép.</div>';
        } else {
            html = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle me-1"></i> Có ' + overLimitTeams.length + ' teams vượt quá giới hạn:</div>';
            html += '<ul class="list-group list-group-flush">';
            overLimitTeams.forEach(team => {
                html += `<li class="list-group-item d-flex justify-content-between align-items-center">
                    ${team.name}
                    <span class="badge bg-danger">${team.member_count}/${maxMembers}</span>
                </li>`;
            });
            html += '</ul>';
        }
        
        resultsDiv.innerHTML = html;
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1500);
}
</script>
