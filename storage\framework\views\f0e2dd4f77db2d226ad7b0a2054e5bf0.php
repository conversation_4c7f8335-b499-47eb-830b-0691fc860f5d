<?php $__env->startSection('title', 'Quản lý đơn hàng Checkout - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý đơn hàng Checkout'); ?>

<?php $__env->startSection('page-actions'); ?>
<a href="<?php echo e(route('checkout.orders.create')); ?>" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    Thêm đơn hàng
</a>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('checkout.orders.index')); ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Mã đơn, tên khách hàng..." value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="pending" <?php echo e(request('status') == 'pending' ? 'selected' : ''); ?>>Chờ xử lý</option>
                    <option value="processing" <?php echo e(request('status') == 'processing' ? 'selected' : ''); ?>>Đang xử lý</option>
                    <option value="shipped" <?php echo e(request('status') == 'shipped' ? 'selected' : ''); ?>>Đã gửi</option>
                    <option value="delivered" <?php echo e(request('status') == 'delivered' ? 'selected' : ''); ?>>Đã giao</option>
                    <option value="completed" <?php echo e(request('status') == 'completed' ? 'selected' : ''); ?>>Hoàn thành</option>
                    <option value="cancelled" <?php echo e(request('status') == 'cancelled' ? 'selected' : ''); ?>>Đã hủy</option>
                    <option value="returned" <?php echo e(request('status') == 'returned' ? 'selected' : ''); ?>>Trả hàng</option>
                    <option value="refunded" <?php echo e(request('status') == 'refunded' ? 'selected' : ''); ?>>Hoàn tiền</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Đơn vị Checkout</label>
                <select name="checkout_unit_id" class="form-select">
                    <option value="">Tất cả</option>
                    <?php $__currentLoopData = $checkoutUnits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($unit->id); ?>" <?php echo e(request('checkout_unit_id') == $unit->id ? 'selected' : ''); ?>>
                            <?php echo e($unit->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <?php if(auth()->user()->isAdmin() && $teams->count() > 0): ?>
            <div class="col-md-2">
                <label class="form-label">Team</label>
                <select name="team_id" class="form-select">
                    <option value="">Tất cả teams</option>
                    <?php $__currentLoopData = $teams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($team->id); ?>" <?php echo e(request('team_id') == $team->id ? 'selected' : ''); ?>>
                            <?php echo e($team->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <?php endif; ?>
            <div class="col-md-2">
                <label class="form-label">Từ ngày</label>
                <input type="date" name="date_from" class="form-control" value="<?php echo e(request('date_from')); ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label">Đến ngày</label>
                <input type="date" name="date_to" class="form-control" value="<?php echo e(request('date_to')); ?>">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Bulk Actions -->
<div class="card mb-4" id="bulkActionsCard" style="display: none;">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <span id="selectedCount">0</span> đơn hàng được chọn
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group">
                    <button type="button" class="btn btn-warning btn-sm" onclick="showBulkStatusModal()">
                        <i class="fas fa-edit"></i> Cập nhật trạng thái
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                        <i class="fas fa-trash"></i> Xóa
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            Danh sách đơn hàng (<?php echo e($orders->total()); ?> đơn hàng)
        </h6>
        <div class="btn-group">
            <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Xuất Excel
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th width="30">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th>Mã đơn hàng</th>
                        <th>Khách hàng</th>
                        <th>Sản phẩm</th>
                        <th>Đơn vị Checkout</th>
                        <th>Số lượng</th>
                        <th>Tổng tiền</th>
                        <th>Trạng thái</th>
                        <th>Ngày tạo</th>
                        <th width="120">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <input type="checkbox" class="order-checkbox" value="<?php echo e($order->id); ?>" onchange="updateBulkActions()">
                        </td>
                        <td>
                            <a href="<?php echo e(route('checkout.orders.show', $order)); ?>" class="text-decoration-none">
                                <strong><?php echo e($order->order_number); ?></strong>
                            </a>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($order->customer_name); ?></strong>
                                <?php if($order->customer_email): ?>
                                    <br><small class="text-muted"><?php echo e($order->customer_email); ?></small>
                                <?php endif; ?>
                                <?php if($order->customer_phone): ?>
                                    <br><small class="text-muted"><?php echo e($order->customer_phone); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($order->product_name); ?></strong>
                                <?php if($order->product_sku): ?>
                                    <br><small class="text-muted">SKU: <?php echo e($order->product_sku); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo e($order->checkoutUnit->name); ?></span>
                        </td>
                        <td class="text-center"><?php echo e($order->quantity); ?></td>
                        <td class="text-end"><?php echo e(number_format($order->total_amount)); ?>đ</td>
                        <td>
                            <?php
                                $statusColors = [
                                    'pending' => 'warning',
                                    'processing' => 'info',
                                    'shipped' => 'primary',
                                    'delivered' => 'success',
                                    'completed' => 'success',
                                    'cancelled' => 'danger',
                                    'returned' => 'secondary',
                                    'refunded' => 'dark'
                                ];
                                $statusLabels = [
                                    'pending' => 'Chờ xử lý',
                                    'processing' => 'Đang xử lý',
                                    'shipped' => 'Đã gửi',
                                    'delivered' => 'Đã giao',
                                    'completed' => 'Hoàn thành',
                                    'cancelled' => 'Đã hủy',
                                    'returned' => 'Trả hàng',
                                    'refunded' => 'Hoàn tiền'
                                ];
                            ?>
                            <span class="badge bg-<?php echo e($statusColors[$order->status] ?? 'secondary'); ?>">
                                <?php echo e($statusLabels[$order->status] ?? $order->status); ?>

                            </span>
                        </td>
                        <td><?php echo e($order->created_at->format('d/m/Y H:i')); ?></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="<?php echo e(route('checkout.orders.show', $order)); ?>" class="btn btn-info btn-sm" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('checkout.orders.edit', $order)); ?>" class="btn btn-warning btn-sm" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-success btn-sm" onclick="showStatusModal(<?php echo e($order->id); ?>)" title="Cập nhật trạng thái">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteOrder(<?php echo e($order->id); ?>)" title="Xóa">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="10" class="text-center">Không có đơn hàng nào</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($orders->hasPages()): ?>
        <div class="d-flex justify-content-center mt-3">
            <?php echo e($orders->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cập nhật trạng thái đơn hàng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <input type="hidden" id="orderId">
                    <div class="mb-3">
                        <label class="form-label">Trạng thái mới</label>
                        <select name="status" id="newStatus" class="form-select" required>
                            <option value="pending">Chờ xử lý</option>
                            <option value="processing">Đang xử lý</option>
                            <option value="shipped">Đã gửi</option>
                            <option value="delivered">Đã giao</option>
                            <option value="completed">Hoàn thành</option>
                            <option value="cancelled">Đã hủy</option>
                            <option value="returned">Trả hàng</option>
                            <option value="refunded">Hoàn tiền</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ghi chú</label>
                        <textarea name="notes" class="form-control" rows="3" placeholder="Ghi chú về việc cập nhật trạng thái..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Cập nhật</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Status Update Modal -->
<div class="modal fade" id="bulkStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cập nhật trạng thái hàng loạt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkStatusForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Trạng thái mới</label>
                        <select name="status" id="bulkNewStatus" class="form-select" required>
                            <option value="pending">Chờ xử lý</option>
                            <option value="processing">Đang xử lý</option>
                            <option value="shipped">Đã gửi</option>
                            <option value="delivered">Đã giao</option>
                            <option value="completed">Hoàn thành</option>
                            <option value="cancelled">Đã hủy</option>
                            <option value="returned">Trả hàng</option>
                            <option value="refunded">Hoàn tiền</option>
                        </select>
                    </div>
                    <p class="text-muted">Sẽ cập nhật trạng thái cho <span id="bulkSelectedCount">0</span> đơn hàng được chọn.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Cập nhật</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Bulk actions
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.order-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.order-checkbox:checked');
    const bulkCard = document.getElementById('bulkActionsCard');
    const selectedCount = document.getElementById('selectedCount');

    if (checkboxes.length > 0) {
        bulkCard.style.display = 'block';
        selectedCount.textContent = checkboxes.length;
    } else {
        bulkCard.style.display = 'none';
    }
}

// Status update
function showStatusModal(orderId) {
    document.getElementById('orderId').value = orderId;
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

document.getElementById('statusForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const orderId = document.getElementById('orderId').value;
    const formData = new FormData(this);

    fetch(`/checkout/orders/${orderId}/update-status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Có lỗi xảy ra: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi cập nhật trạng thái');
    });
});

// Bulk status update
function showBulkStatusModal() {
    const checkboxes = document.querySelectorAll('.order-checkbox:checked');
    document.getElementById('bulkSelectedCount').textContent = checkboxes.length;
    new bootstrap.Modal(document.getElementById('bulkStatusModal')).show();
}

document.getElementById('bulkStatusForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const checkboxes = document.querySelectorAll('.order-checkbox:checked');
    const orderIds = Array.from(checkboxes).map(cb => cb.value);
    const status = document.getElementById('bulkNewStatus').value;

    fetch('/checkout/orders/bulk-update', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify({
            order_ids: orderIds,
            action: 'update_status',
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Có lỗi xảy ra: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi cập nhật trạng thái');
    });
});

// Delete functions
function deleteOrder(orderId) {
    if (confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/checkout/orders/${orderId}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function bulkDelete() {
    const checkboxes = document.querySelectorAll('.order-checkbox:checked');
    const orderIds = Array.from(checkboxes).map(cb => cb.value);

    if (confirm(`Bạn có chắc chắn muốn xóa ${orderIds.length} đơn hàng được chọn?`)) {
        fetch('/checkout/orders/bulk-update', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify({
                order_ids: orderIds,
                action: 'delete'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi xóa đơn hàng');
        });
    }
}

// Export function
function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.location.href = '<?php echo e(route("checkout.orders.index")); ?>?' + params.toString();
}
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/checkout/orders/index.blade.php ENDPATH**/ ?>