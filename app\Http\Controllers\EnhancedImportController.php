<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\Category;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class EnhancedImportController extends Controller
{
    public function index()
    {
        return view('import.enhanced.index');
    }

    public function importOrders(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240', // 10MB max
            'team_id' => 'required|exists:teams,id',
            'platform' => 'required|string',
            'platform_account' => 'required|string'
        ]);

        $file = $request->file('csv_file');
        $teamId = $request->team_id;
        $platform = $request->platform;
        $platformAccount = $request->platform_account;

        try {
            $results = $this->processOrdersCsv($file, $teamId, $platform, $platformAccount);
            
            return response()->json([
                'success' => true,
                'message' => 'Import thành công!',
                'data' => $results
            ]);
        } catch (\Exception $e) {
            Log::error('CSV Import Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    private function processOrdersCsv($file, $teamId, $platform, $platformAccount)
    {
        $csvData = array_map('str_getcsv', file($file->getPathname()));
        $headers = array_shift($csvData); // Remove header row
        
        // Normalize headers (remove BOM, trim, lowercase)
        $headers = array_map(function($header) {
            return strtolower(trim($header, "\xEF\xBB\xBF"));
        }, $headers);

        $results = [
            'total_rows' => count($csvData),
            'imported' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        // Expected column mapping from Google Sheets format
        $columnMapping = [
            'date' => ['date', 'ngày', 'ngay'],
            'order_number' => ['order number', 'order_number', 'mã đơn', 'ma don'],
            'buyer_id' => ['buyer id', 'buyer_id', 'id người mua', 'nguoi mua'],
            'product_link' => ['link sp', 'link_sp', 'product_link', 'link sản phẩm'],
            'cost_price' => ['giá gốc', 'gia goc', 'cost_price', 'cost'],
            'quantity' => ['sl', 'số lượng', 'so luong', 'quantity'],
            'ebay_account' => ['ebay account', 'ebay_account', 'tài khoản ebay'],
            'sku' => ['sku', 'mã sp', 'ma sp'],
            'supplier' => ['nguồn hàng', 'nguon hang', 'supplier', 'nhà cung cấp'],
            'images' => ['hình ảnh', 'hinh anh', 'images', 'image'],
            'checkout_unit' => ['bên co', 'ben co', 'checkout', 'bên checkout'],
            'selling_price' => ['giá ebay', 'gia ebay', 'selling_price', 'giá bán'],
            'earnings' => ['earnings', 'thu nhập', 'thu nhap', 'lợi nhuận']
        ];

        // Find column indexes
        $columnIndexes = [];
        foreach ($columnMapping as $field => $possibleNames) {
            foreach ($possibleNames as $name) {
                $index = array_search($name, $headers);
                if ($index !== false) {
                    $columnIndexes[$field] = $index;
                    break;
                }
            }
        }

        DB::transaction(function () use ($csvData, $columnIndexes, $teamId, $platform, $platformAccount, &$results) {
            foreach ($csvData as $rowIndex => $row) {
                try {
                    // Skip empty rows
                    if (empty(array_filter($row))) {
                        $results['skipped']++;
                        continue;
                    }

                    $orderData = $this->extractOrderData($row, $columnIndexes, $teamId, $platform, $platformAccount);
                    
                    if ($orderData) {
                        $this->createOrderFromCsv($orderData);
                        $results['imported']++;
                    } else {
                        $results['skipped']++;
                    }
                } catch (\Exception $e) {
                    $results['errors'][] = "Dòng " . ($rowIndex + 2) . ": " . $e->getMessage();
                    $results['skipped']++;
                }
            }
        });

        return $results;
    }

    private function extractOrderData($row, $columnIndexes, $teamId, $platform, $platformAccount)
    {
        // Extract basic order information
        $orderDate = $this->parseDate($row[$columnIndexes['date'] ?? 0] ?? '');
        $buyerId = $row[$columnIndexes['buyer_id'] ?? 1] ?? '';
        $productLink = $row[$columnIndexes['product_link'] ?? 2] ?? '';
        $costPrice = $this->parsePrice($row[$columnIndexes['cost_price'] ?? 3] ?? '0');
        $quantity = (int)($row[$columnIndexes['quantity'] ?? 4] ?? 1);
        $sku = $row[$columnIndexes['sku'] ?? 5] ?? '';
        $supplierName = $row[$columnIndexes['supplier'] ?? 6] ?? '';
        $sellingPrice = $this->parsePrice($row[$columnIndexes['selling_price'] ?? 7] ?? '0');
        $earnings = $this->parsePrice($row[$columnIndexes['earnings'] ?? 8] ?? '0');

        // Skip if essential data is missing
        if (empty($buyerId) || $sellingPrice <= 0) {
            return null;
        }

        // Generate customer info from buyer_id if not provided
        $customerName = "Customer " . $buyerId;
        $customerEmail = strtolower($buyerId) . "@example.com";
        $customerPhone = "0000000000";
        $shippingAddress = "Địa chỉ giao hàng cho " . $buyerId;

        // Find or create product
        $product = $this->findOrCreateProduct($sku, $supplierName, $costPrice, $sellingPrice);

        return [
            'order_date' => $orderDate,
            'buyer_id' => $buyerId,
            'product_link' => $productLink,
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'customer_phone' => $customerPhone,
            'shipping_address' => $shippingAddress,
            'team_id' => $teamId,
            'platform' => $platform,
            'platform_account' => $platformAccount,
            'product' => $product,
            'quantity' => $quantity,
            'selling_price' => $sellingPrice,
            'cost_price' => $costPrice,
            'earnings' => $earnings
        ];
    }

    private function createOrderFromCsv($data)
    {
        // Calculate financial data
        $subtotal = $data['selling_price'] * $data['quantity'];
        $costAmount = $data['cost_price'] * $data['quantity'];
        $platformFee = $subtotal - $data['earnings']; // Platform fee = selling price - earnings
        $profitAmount = $data['earnings'] - $costAmount;
        $profitMargin = $costAmount > 0 ? ($profitAmount / $costAmount) * 100 : 0;

        // Create order
        $order = Order::create([
            'user_id' => auth()->id(),
            'team_id' => $data['team_id'],
            'customer_name' => $data['customer_name'],
            'customer_email' => $data['customer_email'],
            'customer_phone' => $data['customer_phone'],
            'shipping_address' => $data['shipping_address'],
            'buyer_id' => $data['buyer_id'],
            'platform' => $data['platform'],
            'platform_account' => $data['platform_account'],
            'product_link' => $data['product_link'],
            'subtotal' => $subtotal,
            'total_amount' => $subtotal,
            'cost_amount' => $costAmount,
            'profit_amount' => $profitAmount,
            'platform_fee' => $platformFee,
            'profit_margin' => $profitMargin,
            'is_profitable' => $profitAmount > 0,
            'status' => 'pending',
            'created_at' => $data['order_date']
        ]);

        // Create order item
        $order->orderItems()->create([
            'product_id' => $data['product']->id,
            'product_name' => $data['product']->name,
            'product_sku' => $data['product']->sku,
            'unit_price' => $data['selling_price'],
            'quantity' => $data['quantity'],
            'total_price' => $subtotal
        ]);

        return $order;
    }

    private function findOrCreateProduct($sku, $supplierName, $costPrice, $sellingPrice)
    {
        // Try to find existing product by SKU
        $product = Product::where('sku', $sku)->first();
        
        if ($product) {
            return $product;
        }

        // Find or create supplier
        $supplier = Supplier::firstOrCreate(
            ['name' => $supplierName ?: 'Unknown Supplier'],
            [
                'contact_person' => 'N/A',
                'email' => '<EMAIL>',
                'phone' => '0000000000',
                'address' => 'Unknown Address'
            ]
        );

        // Find or create default category
        $category = Category::firstOrCreate(
            ['name' => 'Imported Products'],
            ['description' => 'Products imported from CSV']
        );

        // Create new product
        $product = Product::create([
            'name' => 'Product ' . $sku,
            'sku' => $sku ?: 'SKU-' . strtoupper(Str::random(8)),
            'description' => 'Product imported from CSV',
            'cost_price' => $costPrice,
            'selling_price' => $sellingPrice,
            'stock_quantity' => 100, // Default stock
            'category_id' => $category->id,
            'supplier_id' => $supplier->id,
            'status' => 'active'
        ]);

        return $product;
    }

    private function parseDate($dateString)
    {
        if (empty($dateString)) {
            return now();
        }

        try {
            // Try different date formats
            $formats = ['d/m/Y', 'Y-m-d', 'd-m-Y', 'm/d/Y'];
            
            foreach ($formats as $format) {
                $date = Carbon::createFromFormat($format, $dateString);
                if ($date) {
                    return $date;
                }
            }
            
            // Fallback to Carbon parse
            return Carbon::parse($dateString);
        } catch (\Exception $e) {
            return now();
        }
    }

    private function parsePrice($priceString)
    {
        // Remove currency symbols and spaces
        $cleaned = preg_replace('/[^\d.,]/', '', $priceString);
        
        // Handle different decimal separators
        if (strpos($cleaned, ',') !== false && strpos($cleaned, '.') !== false) {
            // Both comma and dot present, assume comma is thousands separator
            $cleaned = str_replace(',', '', $cleaned);
        } elseif (strpos($cleaned, ',') !== false) {
            // Only comma present, could be decimal separator
            $parts = explode(',', $cleaned);
            if (count($parts) == 2 && strlen($parts[1]) <= 2) {
                // Likely decimal separator
                $cleaned = str_replace(',', '.', $cleaned);
            } else {
                // Likely thousands separator
                $cleaned = str_replace(',', '', $cleaned);
            }
        }
        
        return (float)$cleaned;
    }

    public function downloadTemplate()
    {
        $headers = [
            'Date',
            'Order Number', 
            'Buyer ID',
            'Link SP',
            'Giá Gốc',
            'SL',
            'Ebay Account',
            'SKU',
            'Nguồn Hàng',
            'Hình Ảnh',
            'Bên CO',
            'Giá Ebay',
            'Earnings'
        ];

        $sampleData = [
            [
                '01/05/2024',
                'ORD-********-0001',
                'buyer123',
                'https://www.ebay.com/itm/*********',
                '50000',
                '2',
                'myebayaccount',
                'SKU-ABC123',
                'Supplier ABC',
                'image1.jpg',
                'Checkout Unit 1',
                '120000',
                '100000'
            ]
        ];

        $filename = 'order_import_template.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Add BOM for UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Write headers
        fputcsv($output, $headers);
        
        // Write sample data
        foreach ($sampleData as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
        exit;
    }

    public function validateCsv(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240'
        ]);

        $file = $request->file('csv_file');
        
        try {
            $csvData = array_map('str_getcsv', file($file->getPathname()));
            $headers = array_shift($csvData);
            
            // Normalize headers
            $headers = array_map(function($header) {
                return strtolower(trim($header, "\xEF\xBB\xBF"));
            }, $headers);

            $validation = [
                'total_rows' => count($csvData),
                'headers' => $headers,
                'sample_data' => array_slice($csvData, 0, 5), // First 5 rows
                'issues' => []
            ];

            // Check for required columns
            $requiredColumns = ['buyer id', 'giá ebay', 'sl'];
            foreach ($requiredColumns as $required) {
                $found = false;
                foreach ($headers as $header) {
                    if (strpos($header, $required) !== false) {
                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    $validation['issues'][] = "Không tìm thấy cột bắt buộc: {$required}";
                }
            }

            return response()->json([
                'success' => true,
                'data' => $validation
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi đọc file CSV: ' . $e->getMessage()
            ], 400);
        }
    }
}
