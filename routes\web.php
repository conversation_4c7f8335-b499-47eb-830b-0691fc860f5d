<?php

use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CheckoutDashboardController;
use App\Http\Controllers\CheckoutOrderController;
use App\Http\Controllers\CheckoutUnitController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EbayAccountController;
use App\Http\Controllers\EbayPayoutController;
use App\Http\Controllers\FinanceDashboardController;
use App\Http\Controllers\ImportController;
use App\Http\Controllers\MonthlyExpenseController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\Admin\SettingsController;
use Illuminate\Support\Facades\Route;

// Redirect root to dashboard
Route::get('/', function () {
    return redirect()->route('dashboard');
});

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'has.team'])->name('dashboard');

// Categories
Route::resource('categories', CategoryController::class)->middleware(['auth', 'has.team']);

// Suppliers
Route::resource('suppliers', SupplierController::class)->middleware(['auth', 'has.team']);

// Products
Route::resource('products', ProductController::class)->middleware(['auth', 'has.team']);
Route::post('/products/bulk-update-stock', [ProductController::class, 'bulkUpdateStock'])->middleware(['auth', 'has.team'])->name('products.bulk-update-stock');

// Orders
Route::resource('orders', OrderController::class)->middleware(['auth', 'has.team']);

// Import
Route::get('/import', [ImportController::class, 'index'])->middleware(['auth', 'has.team'])->name('import.index');
Route::post('/import/products', [ImportController::class, 'importProducts'])->middleware(['auth', 'has.team'])->name('import.products');
Route::get('/import/template', [ImportController::class, 'downloadTemplate'])->middleware(['auth', 'has.team'])->name('import.template');

// Checkout Management
Route::prefix('checkout')->name('checkout.')->middleware(['auth', 'has.team'])->group(function () {
    // Checkout Dashboard
    Route::get('/', [CheckoutDashboardController::class, 'index'])->name('dashboard');
    Route::get('/analytics', [CheckoutDashboardController::class, 'analytics'])->name('analytics');

    // Checkout Units
    Route::resource('units', CheckoutUnitController::class);
    Route::post('/units/{checkoutUnit}/sync-google-sheet', [CheckoutUnitController::class, 'syncGoogleSheet'])->name('units.sync-google-sheet');
    Route::get('/units/{checkoutUnit}/export', [CheckoutUnitController::class, 'export'])->name('units.export');

    // Checkout Orders
    Route::resource('orders', CheckoutOrderController::class);
    Route::post('/orders/{checkoutOrder}/update-status', [CheckoutOrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::post('/orders/bulk-update', [CheckoutOrderController::class, 'bulkUpdate'])->name('orders.bulk-update');
    Route::get('/orders/{checkoutOrder}/tracking', [CheckoutOrderController::class, 'tracking'])->name('orders.tracking');
});

// Finance Management
Route::prefix('finance')->name('finance.')->middleware(['auth', 'has.team'])->group(function () {
    // Finance Dashboard
    Route::get('/', [FinanceDashboardController::class, 'index'])->name('dashboard');
    Route::get('/profit-report', [FinanceDashboardController::class, 'profitReport'])->name('profit-report');

    // Teams
    Route::resource('teams', TeamController::class);
    Route::get('/teams/{team}/members', [TeamController::class, 'members'])->name('teams.members');
    Route::post('/teams/{team}/add-member', [TeamController::class, 'addMember'])->name('teams.add-member');
    Route::delete('/teams/{team}/members/{member}', [TeamController::class, 'removeMember'])->name('teams.remove-member');
    Route::post('/teams/{team}/change-leader', [TeamController::class, 'changeLeader'])->name('teams.change-leader');

    // eBay Accounts
    Route::resource('accounts', EbayAccountController::class);

    // eBay Payouts
    Route::resource('payouts', EbayPayoutController::class);
    Route::get('/payouts-quick/create', [EbayPayoutController::class, 'quickCreate'])->name('payouts.quick-create');
    Route::post('/payouts-quick/store', [EbayPayoutController::class, 'quickStore'])->name('payouts.quick-store');

    // Monthly Expenses
    Route::resource('expenses', MonthlyExpenseController::class);

    // Smart Expense Management Routes
    Route::post('/expenses/suggest-category', [MonthlyExpenseController::class, 'suggestCategory'])->name('expenses.suggest-category');
    Route::post('/expenses/{expense}/process-approval', [MonthlyExpenseController::class, 'processApproval'])->name('expenses.process-approval');
    Route::get('/expenses-analytics', [MonthlyExpenseController::class, 'analytics'])->name('expenses.analytics');
    Route::post('/expenses/bulk-approve', [MonthlyExpenseController::class, 'bulkApprove'])->name('expenses.bulk-approve');
    Route::get('/expenses-export', [MonthlyExpenseController::class, 'export'])->name('expenses.export');
});

// Notifications
Route::prefix('notifications')->name('notifications.')->middleware(['auth', 'has.team'])->group(function () {
    Route::get('/', [NotificationController::class, 'index'])->name('index');
    Route::get('/recent', [NotificationController::class, 'getRecent'])->name('recent');
    Route::get('/count', [NotificationController::class, 'getCount'])->name('count');
    Route::post('/{notification}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('mark-as-read');
    Route::post('/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('mark-all-as-read');
    Route::delete('/{notification}', [NotificationController::class, 'destroy'])->name('destroy');
});

// Admin Settings (chỉ admin mới truy cập được)
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::put('/', [SettingsController::class, 'update'])->name('update');
        Route::post('/test-api', [SettingsController::class, 'testApi'])->name('test-api');
        Route::get('/export', [SettingsController::class, 'export'])->name('export');
        Route::post('/import', [SettingsController::class, 'import'])->name('import');
        Route::post('/reset-defaults', [SettingsController::class, 'resetToDefaults'])->name('reset-defaults');
        Route::post('/initialize-defaults', [SettingsController::class, 'initializeDefaults'])->name('initialize-defaults');
        Route::post('/clear-cache', [SettingsController::class, 'clearCache'])->name('clear-cache');
        Route::get('/get-value', [SettingsController::class, 'getValue'])->name('get-value');
        Route::post('/set-value', [SettingsController::class, 'setValue'])->name('set-value');
    });
});
