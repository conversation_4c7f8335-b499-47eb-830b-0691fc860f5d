<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Thời trang nam',
                'description' => 'Quần áo, phụ kiện thời trang dành cho nam giới',
                'sort_order' => 1
            ],
            [
                'name' => 'Thời trang nữ',
                'description' => 'Quần áo, phụ kiện thời trang dành cho nữ giới',
                'sort_order' => 2
            ],
            [
                'name' => 'Điện tử',
                'description' => 'Thiết bị điện tử, công nghệ',
                'sort_order' => 3
            ],
            [
                'name' => 'Gia dụng',
                'description' => '<PERSON><PERSON> dùng gia đình, nhà bếp',
                'sort_order' => 4
            ],
            [
                'name' => 'Sức khỏe & Làm đẹp',
                'description' => 'Sản phẩm chăm sóc sức khỏe và làm đẹp',
                'sort_order' => 5
            ],
            [
                'name' => 'Thể thao',
                'description' => 'Dụng cụ thể thao, trang phục thể thao',
                'sort_order' => 6
            ],
            [
                'name' => 'Mẹ và bé',
                'description' => 'Sản phẩm dành cho mẹ và bé',
                'sort_order' => 7
            ],
            [
                'name' => 'Sách & Văn phòng phẩm',
                'description' => 'Sách, dụng cụ học tập và văn phòng',
                'sort_order' => 8
            ]
        ];

        foreach ($categories as $categoryData) {
            Category::create([
                'name' => $categoryData['name'],
                'slug' => Str::slug($categoryData['name']),
                'description' => $categoryData['description'],
                'is_active' => true,
                'sort_order' => $categoryData['sort_order']
            ]);
        }
    }
}
