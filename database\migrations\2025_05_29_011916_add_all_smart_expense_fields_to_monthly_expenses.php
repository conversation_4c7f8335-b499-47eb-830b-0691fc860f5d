<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monthly_expenses', function (Blueprint $table) {
            // Kiểm tra và thêm category nếu chưa có
            if (!Schema::hasColumn('monthly_expenses', 'category')) {
                $table->string('category')->default('other')->after('status');
            }

            // Smart categorization fields
            if (!Schema::hasColumn('monthly_expenses', 'vendor')) {
                $table->string('vendor')->nullable()->after('description');
            }
            if (!Schema::hasColumn('monthly_expenses', 'suggested_category')) {
                $table->string('suggested_category')->nullable()->after('category');
            }
            if (!Schema::hasColumn('monthly_expenses', 'categorization_confidence')) {
                $table->decimal('categorization_confidence', 5, 2)->nullable()->after('suggested_category');
            }
            if (!Schema::hasColumn('monthly_expenses', 'category_manually_corrected')) {
                $table->boolean('category_manually_corrected')->default(false)->after('categorization_confidence');
            }

            // Validation and fraud detection fields
            if (!Schema::hasColumn('monthly_expenses', 'validation_results')) {
                $table->json('validation_results')->nullable()->after('category_manually_corrected');
            }
            if (!Schema::hasColumn('monthly_expenses', 'fraud_score')) {
                $table->decimal('fraud_score', 5, 2)->default(0)->after('validation_results');
            }
            if (!Schema::hasColumn('monthly_expenses', 'risk_level')) {
                $table->string('risk_level')->default('low')->after('fraud_score');
            }
            if (!Schema::hasColumn('monthly_expenses', 'fraud_indicators')) {
                $table->json('fraud_indicators')->nullable()->after('risk_level');
            }

            // Approval workflow fields
            if (!Schema::hasColumn('monthly_expenses', 'approval_status')) {
                $table->string('approval_status')->default('pending')->after('status');
            }
            if (!Schema::hasColumn('monthly_expenses', 'approval_path')) {
                $table->json('approval_path')->nullable()->after('approval_status');
            }
            if (!Schema::hasColumn('monthly_expenses', 'approval_history')) {
                $table->json('approval_history')->nullable()->after('approval_path');
            }
            if (!Schema::hasColumn('monthly_expenses', 'approval_comments')) {
                $table->text('approval_comments')->nullable()->after('approved_by');
            }

            // Additional metadata
            if (!Schema::hasColumn('monthly_expenses', 'processed_at')) {
                $table->timestamp('processed_at')->nullable()->after('approval_comments');
            }
        });

        // Add indexes for performance
        Schema::table('monthly_expenses', function (Blueprint $table) {
            if (!$this->indexExists('monthly_expenses', 'monthly_expenses_approval_status_created_at_index')) {
                $table->index(['approval_status', 'created_at']);
            }
            if (!$this->indexExists('monthly_expenses', 'monthly_expenses_risk_level_fraud_score_index')) {
                $table->index(['risk_level', 'fraud_score']);
            }
            if (!$this->indexExists('monthly_expenses', 'monthly_expenses_category_team_id_created_at_index')) {
                $table->index(['category', 'team_id', 'created_at']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monthly_expenses', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['approval_status', 'created_at']);
            $table->dropIndex(['risk_level', 'fraud_score']);
            $table->dropIndex(['category', 'team_id', 'created_at']);

            // Drop columns
            $columns = [
                'vendor',
                'category',
                'suggested_category',
                'categorization_confidence',
                'category_manually_corrected',
                'validation_results',
                'fraud_score',
                'risk_level',
                'fraud_indicators',
                'approval_status',
                'approval_path',
                'approval_history',
                'approval_comments',
                'processed_at'
            ];

            foreach ($columns as $column) {
                if (Schema::hasColumn('monthly_expenses', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }

    private function indexExists($table, $indexName)
    {
        $indexes = \DB::select("SHOW INDEX FROM {$table}");
        foreach ($indexes as $index) {
            if ($index->Key_name === $indexName) {
                return true;
            }
        }
        return false;
    }
};
