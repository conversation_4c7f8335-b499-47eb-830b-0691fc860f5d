<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ebay_payouts', function (Blueprint $table) {
            $table->id();
            $table->string('payout_number')->unique(); // Mã payout
            $table->foreignId('ebay_account_id')->constrained()->onDelete('cascade'); // Account eBay
            $table->foreignId('team_id')->constrained()->onDelete('cascade'); // Team
            $table->foreignId('entered_by')->constrained('users')->onDelete('cascade'); // Người nhập
            $table->decimal('amount', 15, 2); // Số tiền payout
            $table->date('payout_date'); // Ngày payout từ eBay
            $table->date('received_date')->nullable(); // Ng<PERSON>y nhận tiền thực tế
            $table->string('currency', 3)->default('USD'); // Loại tiền tệ
            $table->decimal('exchange_rate', 10, 4)->nullable(); // Tỷ giá (nếu có)
            $table->decimal('amount_vnd', 15, 2)->nullable(); // Số tiền VND (sau quy đổi)
            $table->enum('status', ['pending', 'received', 'processing', 'completed'])->default('pending'); // Trạng thái
            $table->string('reference_number')->nullable(); // Số tham chiếu từ eBay
            $table->text('notes')->nullable(); // Ghi chú
            $table->json('metadata')->nullable(); // Thông tin bổ sung (JSON)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ebay_payouts');
    }
};
