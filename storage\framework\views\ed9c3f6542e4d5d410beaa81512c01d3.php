<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Dropship Manager'); ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            width: 250px;
            transform: translateX(0);
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }



        .sidebar-toggle {
            position: fixed;
            top: 20px;
            left: 15px;
            z-index: 1001;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            color: white;
        }

        .sidebar-toggle:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
        }

        .sidebar-toggle.collapsed {
            left: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .sidebar-toggle:not(.collapsed) {
            left: 210px;
        }

        /* Đảm bảo main content có padding-top để không bị che */
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            margin-left: 250px;
            transition: all 0.3s ease;
            padding-top: 20px; /* Giảm padding-top */
        }

        .main-content.expanded {
            margin-left: 0;
        }

        .top-navbar {
            margin-left: 45px; /* Tạo khoảng trống cho toggle button */
            transition: margin-left 0.3s ease;
        }

        .main-content.expanded .top-navbar {
            margin-left: 45px; /* Giữ margin khi sidebar ẩn */
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 280px;
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding-top: 20px; /* Giảm padding-top trên mobile */
            }

            .sidebar-toggle {
                left: 15px !important;
                top: 15px; /* Điều chỉnh vị trí trên mobile */
            }

            /* Fix top navbar trên mobile */
            .top-navbar {
                margin-left: 45px !important;
            }
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .badge {
            font-size: 0.75em;
        }
        .navbar-brand {
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle" type="button"
            data-bs-toggle="tooltip" data-bs-placement="right"
            title="Ẩn/Hiện sidebar">
        <i class="fas fa-times" id="toggleIcon"></i>
    </button>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <div class="container-fluid p-0">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-shipping-fast"></i>
                            Dropship Manager
                        </h4>
                        <?php if(auth()->check()): ?>
                        <div class="mt-3 p-2 bg-dark rounded">
                            <div class="text-white">
                                <strong><?php echo e(auth()->user()->name); ?></strong>
                                <br><small class="text-muted"><?php echo e(auth()->user()->role_label); ?></small>
                                <?php if(auth()->user()->team): ?>
                                    <br><small class="text-info"><?php echo e(auth()->user()->team->name); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('dashboard')); ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('products.*') ? 'active' : ''); ?>" href="<?php echo e(route('products.index')); ?>">
                                <i class="fas fa-box me-2"></i>
                                Sản phẩm
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('categories.*') ? 'active' : ''); ?>" href="<?php echo e(route('categories.index')); ?>">
                                <i class="fas fa-tags me-2"></i>
                                Danh mục
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('suppliers.*') ? 'active' : ''); ?>" href="<?php echo e(route('suppliers.index')); ?>">
                                <i class="fas fa-truck me-2"></i>
                                Nhà cung cấp
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('orders.enhanced.*') ? 'active' : ''); ?>" href="<?php echo e(route('orders.enhanced.index')); ?>">
                                <i class="fas fa-shopping-cart me-2"></i>
                                Quản lý Đơn hàng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('orders.enhanced.dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('orders.enhanced.dashboard')); ?>">
                                <i class="fas fa-chart-pie me-2"></i>
                                Dashboard Đơn hàng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('orders.*') && !request()->routeIs('orders.enhanced.*') ? 'active' : ''); ?>" href="<?php echo e(route('orders.index')); ?>">
                                <i class="fas fa-list me-2"></i>
                                Đơn hàng cũ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('import.enhanced.*') ? 'active' : ''); ?>" href="<?php echo e(route('import.enhanced.index')); ?>">
                                <i class="fas fa-file-csv me-2"></i>
                                Import Đơn hàng CSV
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('import.*') && !request()->routeIs('import.enhanced.*') ? 'active' : ''); ?>" href="<?php echo e(route('import.index')); ?>">
                                <i class="fas fa-file-import me-2"></i>
                                Import Sản phẩm
                            </a>
                        </li>

                        <!-- Checkout Management -->
                        <li class="nav-item mt-3">
                            <h6 class="text-white-50 text-uppercase small mb-2">Quản lý Checkout</h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('checkout.dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('checkout.dashboard')); ?>">
                                <i class="fas fa-chart-line me-2"></i>
                                Dashboard Checkout
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('checkout.units.*') ? 'active' : ''); ?>" href="<?php echo e(route('checkout.units.index')); ?>">
                                <i class="fas fa-users me-2"></i>
                                Đơn vị Checkout
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('checkout.orders.*') ? 'active' : ''); ?>" href="<?php echo e(route('checkout.orders.index')); ?>">
                                <i class="fas fa-clipboard-list me-2"></i>
                                Đơn hàng Checkout
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('checkout.analytics') ? 'active' : ''); ?>" href="<?php echo e(route('checkout.analytics')); ?>">
                                <i class="fas fa-analytics me-2"></i>
                                Phân tích Checkout
                            </a>
                        </li>

                        <!-- Finance Management -->
                        <li class="nav-item mt-3">
                            <h6 class="text-white-50 text-uppercase small mb-2">Quản lý Tài chính</h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('finance.dashboard') ? 'active' : ''); ?>" href="<?php echo e(route('finance.dashboard')); ?>">
                                <i class="fas fa-chart-pie me-2"></i>
                                Dashboard Tài chính
                            </a>
                        </li>
                        <?php if(auth()->check() && (auth()->user()->isAdmin() || auth()->user()->isTeamLeader())): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('finance.teams.*') ? 'active' : ''); ?>" href="<?php echo e(auth()->user()->isAdmin() ? route('finance.teams.index') : route('finance.teams.show', auth()->user()->team_id)); ?>">
                                <i class="fas fa-users-cog me-2"></i>
                                <?php echo e(auth()->user()->isAdmin() ? 'Quản lý Teams' : 'Team của tôi'); ?>

                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('finance.accounts.*') ? 'active' : ''); ?>" href="<?php echo e(route('finance.accounts.index')); ?>">
                                <i class="fab fa-ebay me-2"></i>
                                Tài khoản eBay
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('finance.payouts.*') ? 'active' : ''); ?>" href="<?php echo e(route('finance.payouts.index')); ?>">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Payout eBay
                            </a>
                        </li>
                        <?php if(auth()->check() && auth()->user()->isSeller()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo e(route('finance.payouts.quick-create')); ?>">
                                <i class="fas fa-plus-circle me-2"></i>
                                Nhập Payout nhanh
                            </a>
                        </li>
                        <?php endif; ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('finance.expenses.*') ? 'active' : ''); ?>" href="<?php echo e(route('finance.expenses.index')); ?>">
                                <i class="fas fa-receipt me-2"></i>
                                Chi phí hàng tháng
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('finance.profit-report') ? 'active' : ''); ?>" href="<?php echo e(route('finance.profit-report')); ?>">
                                <i class="fas fa-chart-line me-2"></i>
                                Báo cáo Lợi nhuận
                            </a>
                        </li>

                        <!-- Admin Settings -->
                        <?php if(auth()->check() && auth()->user()->isAdmin()): ?>
                        <li class="nav-item mt-3">
                            <h6 class="text-white-50 text-uppercase small mb-2">Quản trị hệ thống</h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo e(request()->routeIs('admin.settings.*') ? 'active' : ''); ?>" href="<?php echo e(route('admin.settings.index')); ?>">
                                <i class="fas fa-cogs me-2"></i>
                                Cài đặt hệ thống
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="main-content" id="mainContent">
                <div class="px-4">
                <!-- Top navbar -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom top-navbar">
                    <h1 class="h2"><?php echo $__env->yieldContent('page-title', 'Dashboard'); ?></h1>
                    <div class="d-flex align-items-center">
                        <!-- Notifications Dropdown -->
                        <?php if(auth()->check()): ?>
                        <div class="dropdown me-3">
                            <button class="btn btn-outline-secondary position-relative" type="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="notification-count" style="display: none;">
                                    0
                                </span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                <li class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span>Thông báo</span>
                                    <button class="btn btn-sm btn-link p-0" onclick="markAllAsRead()">Đánh dấu tất cả đã đọc</button>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <div id="notification-list">
                                    <li class="dropdown-item text-center text-muted">
                                        <i class="fas fa-spinner fa-spin"></i> Đang tải...
                                    </li>
                                </div>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-center" href="<?php echo e(route('notifications.index')); ?>">
                                        <i class="fas fa-list me-1"></i> Xem tất cả thông báo
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <?php endif; ?>

                        <div class="btn-toolbar mb-2 mb-md-0">
                            <?php echo $__env->yieldContent('page-actions'); ?>
                        </div>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if($errors->any()): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <ul class="mb-0">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Page content -->
                <?php echo $__env->yieldContent('content'); ?>
                </div>
            </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Auto hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);

        // CSRF token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Sidebar Toggle Functionality
        class SidebarToggle {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.toggleButton = document.getElementById('sidebarToggle');
                this.toggleIcon = document.getElementById('toggleIcon');
                this.overlay = document.getElementById('sidebarOverlay');
                this.isMobile = window.innerWidth <= 768;

                this.init();
            }

            init() {
                // Load saved state from localStorage
                this.loadSidebarState();

                // Event listeners
                this.toggleButton.addEventListener('click', () => this.toggleSidebar());
                this.overlay.addEventListener('click', () => this.closeSidebar());

                // Handle window resize
                window.addEventListener('resize', () => this.handleResize());

                // Keyboard support (Ctrl + B to toggle sidebar)
                document.addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'b') {
                        e.preventDefault();
                        this.toggleSidebar();
                    }

                    // ESC key to close mobile sidebar
                    if (e.key === 'Escape' && this.isMobile && this.sidebar.classList.contains('show')) {
                        this.closeSidebar();
                    }
                });

                // Auto-close sidebar on mobile when clicking nav links
                if (this.isMobile) {
                    this.sidebar.addEventListener('click', (e) => {
                        if (e.target.classList.contains('nav-link')) {
                            this.closeSidebar();
                        }
                    });
                }

                // Update initial state
                this.updateToggleButton();
            }

            toggleSidebar() {
                if (this.isMobile) {
                    this.toggleMobileSidebar();
                } else {
                    this.toggleDesktopSidebar();
                }
            }

            toggleDesktopSidebar() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.showSidebar();
                } else {
                    this.hideSidebar();
                }

                // Save state
                this.saveSidebarState(!isCollapsed);
            }

            toggleMobileSidebar() {
                const isVisible = this.sidebar.classList.contains('show');

                if (isVisible) {
                    this.closeSidebar();
                } else {
                    this.showMobileSidebar();
                }
            }

            showSidebar() {
                this.sidebar.classList.remove('collapsed');
                this.mainContent.classList.remove('expanded');
                this.toggleButton.classList.remove('collapsed');
                this.updateToggleIcon(false);
            }

            hideSidebar() {
                this.sidebar.classList.add('collapsed');
                this.mainContent.classList.add('expanded');
                this.toggleButton.classList.add('collapsed');
                this.updateToggleIcon(true);
            }

            showMobileSidebar() {
                this.sidebar.classList.add('show');
                this.overlay.classList.add('show');
                this.updateToggleIcon(false);
                document.body.style.overflow = 'hidden';
            }

            closeSidebar() {
                this.sidebar.classList.remove('show');
                this.overlay.classList.remove('show');
                this.updateToggleIcon(true);
                document.body.style.overflow = '';
            }

            updateToggleIcon(isCollapsed) {
                if (this.isMobile) {
                    this.toggleIcon.className = isCollapsed ? 'fas fa-bars' : 'fas fa-times';
                } else {
                    this.toggleIcon.className = isCollapsed ? 'fas fa-bars' : 'fas fa-times';
                }
            }

            updateToggleButton() {
                if (this.isMobile) {
                    this.toggleIcon.className = 'fas fa-bars';
                    this.toggleButton.classList.add('collapsed');
                } else {
                    const isCollapsed = this.sidebar.classList.contains('collapsed');
                    this.updateToggleIcon(isCollapsed);
                    this.toggleButton.classList.toggle('collapsed', isCollapsed);
                }
            }

            handleResize() {
                const wasMobile = this.isMobile;
                this.isMobile = window.innerWidth <= 768;

                if (wasMobile !== this.isMobile) {
                    // Reset states when switching between mobile/desktop
                    this.sidebar.classList.remove('show', 'collapsed');
                    this.mainContent.classList.remove('expanded');
                    this.overlay.classList.remove('show');
                    document.body.style.overflow = '';

                    if (!this.isMobile) {
                        // Load desktop state
                        this.loadSidebarState();
                    }

                    this.updateToggleButton();
                }
            }

            saveSidebarState(isCollapsed) {
                if (!this.isMobile) {
                    localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
                }
            }

            loadSidebarState() {
                if (!this.isMobile) {
                    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';

                    if (isCollapsed) {
                        this.hideSidebar();
                    } else {
                        this.showSidebar();
                    }
                }
            }
        }

        // Initialize sidebar toggle when DOM is ready
        $(document).ready(function() {
            new SidebarToggle();

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });

        // Notification functions
        function loadNotifications() {
            $.get('/notifications/recent', function(data) {
                updateNotificationDropdown(data.notifications);
                updateNotificationCount(data.unread_count);
            }).fail(function() {
                $('#notification-list').html('<li class="dropdown-item text-center text-muted">Lỗi khi tải thông báo</li>');
            });
        }

        function updateNotificationDropdown(notifications) {
            let html = '';

            if (notifications.length === 0) {
                html = '<li class="dropdown-item text-center text-muted">Không có thông báo mới</li>';
            } else {
                notifications.forEach(function(notification) {
                    const readClass = notification.is_read ? '' : 'bg-light';
                    const priorityBadge = notification.priority !== 'medium' ?
                        `<span class="badge bg-${notification.priority_color} badge-sm">${notification.priority}</span>` : '';

                    html += `
                        <li class="dropdown-item ${readClass}" style="white-space: normal;">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">${notification.title} ${priorityBadge}</h6>
                                    <p class="mb-1 small">${notification.message}</p>
                                    <small class="text-muted">${notification.time_ago}</small>
                                </div>
                                <div class="ms-2">
                                    ${!notification.is_read ? `<button class="btn btn-sm btn-outline-primary" onclick="markAsRead(${notification.id})"><i class="fas fa-check"></i></button>` : ''}
                                </div>
                            </div>
                            ${notification.action_url ? `<a href="${notification.action_url}" class="stretched-link"></a>` : ''}
                        </li>
                    `;
                });
            }

            $('#notification-list').html(html);
        }

        function updateNotificationCount(count) {
            const badge = $('#notification-count');
            if (count > 0) {
                badge.text(count).show();
            } else {
                badge.hide();
            }
        }

        function markAsRead(notificationId) {
            $.post(`/notifications/${notificationId}/mark-as-read`, function(data) {
                if (data.success) {
                    updateNotificationCount(data.unread_count);
                    loadNotifications(); // Reload to update UI
                }
            });
        }

        function markAllAsRead() {
            $.post('/notifications/mark-all-as-read', function(data) {
                if (data.success) {
                    updateNotificationCount(0);
                    loadNotifications(); // Reload to update UI
                }
            });
        }

        // Load notifications when dropdown is opened
        $('#notificationDropdown').on('click', function() {
            loadNotifications();
        });

        // Load notifications on page load
        $(document).ready(function() {
            loadNotifications();

            // Auto refresh notifications every 30 seconds
            setInterval(function() {
                $.get('/notifications/count', function(data) {
                    updateNotificationCount(data.unread_count);
                });
            }, 30000);
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/layouts/app.blade.php ENDPATH**/ ?>