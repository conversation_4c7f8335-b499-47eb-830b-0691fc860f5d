<?php

namespace App\Http\Controllers;

use App\Models\EbayPayout;
use App\Models\MonthlyExpense;
use App\Models\Team;
use App\Services\CacheService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FinanceDashboardController extends Controller
{
    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    public function index(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        $teamId = $request->get('team_id');

        // Kiểm tra quyền truy cập
        if (!$user->canViewFinance($teamId)) {
            abort(403, 'Bạn không có quyền xem dữ liệu tài chính của team này.');
        }

        // Nếu không phải admin và không chọn team, chỉ hiển thị team của user
        if (!$user->isAdmin() && !$teamId) {
            $teamId = $user->team_id;
        }

        // Team leader chỉ có thể xem team của mình
        if ($user->isTeamLeader() && $teamId && $teamId != $user->team_id) {
            abort(403, 'Team leader chỉ có thể xem tài chính của team mình.');
        }

        // Thống kê tổng quan (sử dụng cache)
        $stats = $this->cacheService->cacheDashboardStats($teamId);

        // Thống kê theo team
        $teamStats = $this->getTeamStats($teamId);

        // Thống kê theo thời gian
        $timeStats = $this->getTimeStats($period, $teamId);

        // Payout và expense gần đây
        $recentPayouts = $this->getRecentPayouts($teamId);
        $recentExpenses = $this->getRecentExpenses($teamId);

        // Danh sách teams để filter
        $teams = $user->getViewableTeams();

        return view('finance.dashboard', compact(
            'stats',
            'teamStats',
            'timeStats',
            'recentPayouts',
            'recentExpenses',
            'teams',
            'period',
            'teamId'
        ));
    }

    private function getOverallStats($teamId = null)
    {
        $payoutQuery = EbayPayout::where('status', 'completed');
        $expenseQuery = MonthlyExpense::where('status', 'paid');

        if ($teamId) {
            $payoutQuery->where('team_id', $teamId);
            $expenseQuery->where('team_id', $teamId);
        }

        $totalPayouts = $payoutQuery->sum('amount_vnd');
        $totalExpenses = $expenseQuery->sum('amount');
        $profit = $totalPayouts - $totalExpenses;

        // Thống kê tháng hiện tại
        $currentMonth = now();
        $monthlyPayouts = $payoutQuery->whereYear('payout_date', $currentMonth->year)
                                     ->whereMonth('payout_date', $currentMonth->month)
                                     ->sum('amount_vnd');

        $monthlyExpenses = $expenseQuery->where('expense_month', $currentMonth->format('Y-m'))
                                       ->sum('amount');

        $monthlyProfit = $monthlyPayouts - $monthlyExpenses;

        return [
            'total_payouts' => $totalPayouts,
            'total_expenses' => $totalExpenses,
            'total_profit' => $profit,
            'monthly_payouts' => $monthlyPayouts,
            'monthly_expenses' => $monthlyExpenses,
            'monthly_profit' => $monthlyProfit,
            'profit_margin' => $totalPayouts > 0 ? round(($profit / $totalPayouts) * 100, 2) : 0,
        ];
    }

    private function getTeamStats($teamId = null)
    {
        $query = Team::with(['ebayPayouts', 'monthlyExpenses']);

        if ($teamId) {
            $query->where('id', $teamId);
        }

        return $query->active()->get()->map(function ($team) {
            $payouts = $team->ebayPayouts()->where('status', 'completed')->sum('amount_vnd');
            $expenses = $team->monthlyExpenses()->where('status', 'paid')->sum('amount');
            $profit = $payouts - $expenses;

            return [
                'id' => $team->id,
                'name' => $team->name,
                'code' => $team->code,
                'payouts' => $payouts,
                'expenses' => $expenses,
                'profit' => $profit,
                'profit_margin' => $payouts > 0 ? round(($profit / $payouts) * 100, 2) : 0,
            ];
        });
    }

    private function getTimeStats($period, $teamId = null)
    {
        $now = Carbon::now();
        $data = [];

        switch ($period) {
            case 'week':
                // 7 ngày gần nhất
                for ($i = 6; $i >= 0; $i--) {
                    $date = $now->copy()->subDays($i);
                    $data[] = $this->getStatsForDate($date, $teamId);
                }
                break;

            case 'month':
                // 30 ngày gần nhất
                for ($i = 29; $i >= 0; $i--) {
                    $date = $now->copy()->subDays($i);
                    $data[] = $this->getStatsForDate($date, $teamId);
                }
                break;

            case 'year':
                // 12 tháng gần nhất
                for ($i = 11; $i >= 0; $i--) {
                    $date = $now->copy()->subMonths($i);
                    $data[] = $this->getStatsForMonth($date, $teamId);
                }
                break;
        }

        return $data;
    }

    private function getStatsForDate($date, $teamId = null)
    {
        $payoutQuery = EbayPayout::whereDate('payout_date', $date)->where('status', 'completed');
        $expenseQuery = MonthlyExpense::whereDate('expense_date', $date)->where('status', 'paid');

        if ($teamId) {
            $payoutQuery->where('team_id', $teamId);
            $expenseQuery->where('team_id', $teamId);
        }

        $payouts = $payoutQuery->sum('amount_vnd');
        $expenses = $expenseQuery->sum('amount');

        return [
            'date' => $date->format('d/m'),
            'payouts' => $payouts,
            'expenses' => $expenses,
            'profit' => $payouts - $expenses,
        ];
    }

    private function getStatsForMonth($date, $teamId = null)
    {
        $payoutQuery = EbayPayout::whereYear('payout_date', $date->year)
                                ->whereMonth('payout_date', $date->month)
                                ->where('status', 'completed');

        $expenseQuery = MonthlyExpense::where('expense_month', $date->format('Y-m'))
                                     ->where('status', 'paid');

        if ($teamId) {
            $payoutQuery->where('team_id', $teamId);
            $expenseQuery->where('team_id', $teamId);
        }

        $payouts = $payoutQuery->sum('amount_vnd');
        $expenses = $expenseQuery->sum('amount');

        return [
            'date' => $date->format('m/Y'),
            'payouts' => $payouts,
            'expenses' => $expenses,
            'profit' => $payouts - $expenses,
        ];
    }

    private function getRecentPayouts($teamId = null)
    {
        $query = EbayPayout::with(['ebayAccount', 'team', 'enteredBy']);

        if ($teamId) {
            $query->where('team_id', $teamId);
        }

        return $query->orderBy('created_at', 'desc')->limit(10)->get();
    }

    private function getRecentExpenses($teamId = null)
    {
        $query = MonthlyExpense::with(['team', 'createdBy']);

        if ($teamId) {
            $query->where('team_id', $teamId);
        }

        return $query->orderBy('created_at', 'desc')->limit(10)->get();
    }

    public function profitReport(Request $request)
    {
        $user = Auth::user();
        $teamId = $request->get('team_id');
        $monthFrom = $request->get('month_from', now()->subMonths(11)->format('Y-m'));
        $monthTo = $request->get('month_to', now()->format('Y-m'));

        // Kiểm tra quyền truy cập
        if (!$user->canViewFinance($teamId)) {
            abort(403, 'Bạn không có quyền xem báo cáo lợi nhuận của team này.');
        }

        // Team leader chỉ có thể xem team của mình
        if ($user->isTeamLeader() && $teamId && $teamId != $user->team_id) {
            abort(403, 'Team leader chỉ có thể xem báo cáo lợi nhuận của team mình.');
        }

        // Parse dates
        $startDate = Carbon::createFromFormat('Y-m', $monthFrom)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $monthTo)->endOfMonth();

        // Get monthly data
        $monthlyData = $this->getMonthlyProfitRange($teamId, $startDate, $endDate);

        // Calculate summary
        $summary = [
            'total_payouts' => array_sum(array_column($monthlyData, 'payouts')),
            'total_expenses' => array_sum(array_column($monthlyData, 'expenses')),
        ];
        $summary['profit'] = $summary['total_payouts'] - $summary['total_expenses'];
        $summary['profit_margin'] = $summary['total_payouts'] > 0 ?
            round(($summary['profit'] / $summary['total_payouts']) * 100, 2) : 0;

        // Team breakdown (if admin viewing all teams)
        $teamBreakdown = [];
        if (!$teamId && $user->isAdmin()) {
            $teams = \App\Models\Team::all();
            foreach ($teams as $team) {
                $teamData = $this->getMonthlyProfitRange($team->id, $startDate, $endDate);
                $teamProfit = array_sum(array_column($teamData, 'profit'));
                if ($teamProfit != 0) {
                    $teamBreakdown[$team->name] = $teamProfit;
                }
            }
        }

        $teams = $user->getViewableTeams();

        return view('finance.profit-report', compact(
            'monthlyData',
            'summary',
            'teamBreakdown',
            'teams',
            'teamId',
            'monthFrom',
            'monthTo'
        ));
    }

    private function getProfitReport($teamId, $year, $quarter = null)
    {
        $data = [];

        if ($quarter) {
            // Báo cáo theo quý
            $startMonth = ($quarter - 1) * 3 + 1;
            $endMonth = $quarter * 3;

            for ($month = $startMonth; $month <= $endMonth; $month++) {
                $data[] = $this->getMonthlyProfitData($teamId, $year, $month);
            }
        } else {
            // Báo cáo theo năm (12 tháng)
            for ($month = 1; $month <= 12; $month++) {
                $data[] = $this->getMonthlyProfitData($teamId, $year, $month);
            }
        }

        return $data;
    }

    private function getMonthlyProfitRange($teamId, $startDate, $endDate)
    {
        $data = [];
        $current = $startDate->copy();
        $previousProfit = null;

        while ($current <= $endDate) {
            $year = $current->year;
            $month = $current->month;

            $payoutQuery = EbayPayout::whereYear('payout_date', $year)
                                    ->whereMonth('payout_date', $month)
                                    ->where('status', 'completed');

            $expenseQuery = MonthlyExpense::where('expense_month', $current->format('Y-m'))
                                         ->where('status', 'paid');

            if ($teamId) {
                $payoutQuery->where('team_id', $teamId);
                $expenseQuery->where('team_id', $teamId);
            }

            $payouts = $payoutQuery->sum('amount_vnd');
            $expenses = $expenseQuery->sum('amount');
            $profit = $payouts - $expenses;

            // Calculate growth
            $growth = 0;
            if ($previousProfit !== null && $previousProfit != 0) {
                $growth = (($profit - $previousProfit) / abs($previousProfit)) * 100;
            }

            $data[] = [
                'month' => $current->format('m/Y'),
                'payouts' => $payouts,
                'expenses' => $expenses,
                'profit' => $profit,
                'profit_margin' => $payouts > 0 ? round(($profit / $payouts) * 100, 2) : 0,
                'growth' => $growth,
            ];

            $previousProfit = $profit;
            $current->addMonth();
        }

        return $data;
    }

    private function getMonthlyProfitData($teamId, $year, $month)
    {
        $payoutQuery = EbayPayout::whereYear('payout_date', $year)
                                ->whereMonth('payout_date', $month)
                                ->where('status', 'completed');

        $expenseQuery = MonthlyExpense::where('expense_month', $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT))
                                     ->where('status', 'paid');

        if ($teamId) {
            $payoutQuery->where('team_id', $teamId);
            $expenseQuery->where('team_id', $teamId);
        }

        $payouts = $payoutQuery->sum('amount_vnd');
        $expenses = $expenseQuery->sum('amount');
        $profit = $payouts - $expenses;

        return [
            'month' => $month,
            'month_name' => Carbon::create($year, $month, 1)->format('m/Y'),
            'payouts' => $payouts,
            'expenses' => $expenses,
            'profit' => $profit,
            'profit_margin' => $payouts > 0 ? round(($profit / $payouts) * 100, 2) : 0,
        ];
    }
}
