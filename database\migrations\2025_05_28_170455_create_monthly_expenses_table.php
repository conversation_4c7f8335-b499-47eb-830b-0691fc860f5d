<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('monthly_expenses', function (Blueprint $table) {
            $table->id();
            $table->string('expense_number')->unique(); // Mã chi phí
            $table->foreignId('team_id')->constrained()->onDelete('cascade'); // Team
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade'); // Người tạo
            $table->enum('expense_type', [
                'checkout_fee',      // Tiền checkout
                'proxy_renewal',     // <PERSON>ia hạn proxy
                'hide_my_acc',       // Hide My Acc subscription
                'salary',            // Lương nhân viên
                'bonus',             // Thưởng nhân viên
                'other'              // Chi phí khác
            ]); // Loại chi phí
            $table->string('title'); // Tiêu đề chi phí
            $table->text('description')->nullable(); // Mô tả chi tiết
            $table->decimal('amount', 15, 2); // Số tiền
            $table->string('currency', 3)->default('VND'); // Loại tiền tệ
            $table->date('expense_date'); // Ngày phát sinh chi phí
            $table->year('expense_month'); // Tháng chi phí (YYYY-MM)
            $table->enum('frequency', ['one_time', 'monthly', 'quarterly', 'yearly'])->default('one_time'); // Tần suất
            $table->enum('status', ['pending', 'approved', 'paid', 'cancelled'])->default('pending'); // Trạng thái
            $table->foreignId('checkout_unit_id')->nullable()->constrained()->onDelete('set null'); // Liên kết checkout unit (nếu có)
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null'); // Người duyệt
            $table->timestamp('approved_at')->nullable(); // Thời gian duyệt
            $table->text('notes')->nullable(); // Ghi chú
            $table->json('attachments')->nullable(); // File đính kèm (JSON)
            $table->json('metadata')->nullable(); // Thông tin bổ sung (JSON)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('monthly_expenses');
    }
};
