<?php $__env->startSection('title', '<PERSON><PERSON><PERSON><PERSON> lý danh mục - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý danh mục'); ?>

<?php $__env->startSection('page-actions'); ?>
<a href="<?php echo e(route('categories.create')); ?>" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    Thêm danh mục
</a>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Danh sách danh mục</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="<?php echo e(route('categories.index')); ?>" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control form-control-sm" name="search" 
                               placeholder="Tìm kiếm..." value="<?php echo e(request('search')); ?>">
                    </div>
                    <div class="col-auto">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                            <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="<?php echo e(route('categories.index')); ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="text-decoration-none text-dark">
                                Tên danh mục
                                <?php if(request('sort_by') == 'name'): ?>
                                    <i class="fas fa-sort-<?php echo e(request('sort_order') == 'asc' ? 'up' : 'down'); ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>Mô tả</th>
                        <th>
                            <a href="<?php echo e(request()->fullUrlWithQuery(['sort_by' => 'sort_order', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc'])); ?>" 
                               class="text-decoration-none text-dark">
                                Thứ tự
                                <?php if(request('sort_by') == 'sort_order'): ?>
                                    <i class="fas fa-sort-<?php echo e(request('sort_order') == 'asc' ? 'up' : 'down'); ?>"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th>Số sản phẩm</th>
                        <th>Trạng thái</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <?php if($category->image): ?>
                                <img src="<?php echo e(asset('storage/' . $category->image)); ?>" 
                                     alt="<?php echo e($category->name); ?>" 
                                     class="img-thumbnail" 
                                     style="width: 50px; height: 50px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div>
                                <strong><?php echo e($category->name); ?></strong>
                            </div>
                            <small class="text-muted"><?php echo e($category->slug); ?></small>
                        </td>
                        <td>
                            <?php if($category->description): ?>
                                <?php echo e(Str::limit($category->description, 80)); ?>

                            <?php else: ?>
                                <span class="text-muted">Chưa có mô tả</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-secondary"><?php echo e($category->sort_order); ?></span>
                        </td>
                        <td>
                            <span class="badge bg-info"><?php echo e($category->products_count); ?></span>
                        </td>
                        <td>
                            <?php if($category->is_active): ?>
                                <span class="badge bg-success">Hoạt động</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Không hoạt động</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small class="text-muted"><?php echo e($category->created_at->format('d/m/Y')); ?></small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?php echo e(route('categories.show', $category)); ?>" class="btn btn-outline-info" title="Xem">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('categories.edit', $category)); ?>" class="btn btn-outline-primary" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('categories.destroy', $category)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger" title="Xóa"
                                            onclick="return confirm('Bạn có chắc chắn muốn xóa danh mục này?\n\nLưu ý: Chỉ có thể xóa danh mục không có sản phẩm nào.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-tags fa-3x mb-3"></i>
                                <p>Chưa có danh mục nào</p>
                                <a href="<?php echo e(route('categories.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Thêm danh mục đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if($categories->hasPages()): ?>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị <?php echo e($categories->firstItem()); ?> - <?php echo e($categories->lastItem()); ?> 
                trong tổng số <?php echo e($categories->total()); ?> danh mục
            </div>
            <div>
                <?php echo e($categories->appends(request()->query())->links()); ?>

            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary"><?php echo e($categories->total()); ?></h5>
                <p class="card-text">Tổng danh mục</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success"><?php echo e($categories->where('is_active', true)->count()); ?></h5>
                <p class="card-text">Đang hoạt động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info"><?php echo e($categories->sum('products_count')); ?></h5>
                <p class="card-text">Tổng sản phẩm</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning"><?php echo e($categories->where('products_count', 0)->count()); ?></h5>
                <p class="card-text">Danh mục trống</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/categories/index.blade.php ENDPATH**/ ?>