@extends('layouts.app')

@section('title', '<PERSON> tiết đơn hàng #' . $order->order_number)
@section('page-title', '<PERSON> tiết đơn hàng #' . $order->order_number)

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('orders.enhanced.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Quay lại danh sách
    </a>
    <a href="{{ route('orders.enhanced.edit', $order) }}" class="btn btn-primary">
        <i class="fas fa-edit me-1"></i>
        Chỉnh sửa
    </a>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-cog me-1"></i>
            <PERSON><PERSON> t<PERSON>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="updateStatus('confirmed')">
                <i class="fas fa-check me-1"></i> Xác nhận đơn hàng
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="updateStatus('processing')">
                <i class="fas fa-cogs me-1"></i> Chuyển sang xử lý
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="updateStatus('shipped')">
                <i class="fas fa-shipping-fast me-1"></i> Đánh dấu đã gửi
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="updateStatus('delivered')">
                <i class="fas fa-check-circle me-1"></i> Đánh dấu đã giao
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="#" onclick="updateStatus('cancelled')">
                <i class="fas fa-times me-1"></i> Hủy đơn hàng
            </a></li>
        </ul>
    </div>
</div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Left Column - Order Details -->
        <div class="col-lg-8">
            <!-- Order Status & Timeline -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-timeline me-2"></i>
                        Trạng thái đơn hàng
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <span class="badge bg-{{ $order->status_badge }} me-2 fs-6">
                                    {{ ucfirst($order->status) }}
                                </span>
                                <span class="text-muted">Trạng thái hiện tại</span>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <span class="badge bg-{{ $order->payment_status == 'paid' ? 'success' : 'warning' }} me-2">
                                    {{ ucfirst($order->payment_status) }}
                                </span>
                                <span class="text-muted">Thanh toán</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="timeline">
                                <div class="timeline-item {{ $order->created_at ? 'completed' : '' }}">
                                    <i class="fas fa-plus-circle"></i>
                                    <span>Tạo đơn hàng</span>
                                    <small class="text-muted">{{ $order->created_at->format('d/m/Y H:i') }}</small>
                                </div>
                                @if($order->confirmed_at)
                                <div class="timeline-item completed">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Xác nhận</span>
                                    <small class="text-muted">{{ $order->confirmed_at->format('d/m/Y H:i') }}</small>
                                </div>
                                @endif
                                @if($order->shipped_at)
                                <div class="timeline-item completed">
                                    <i class="fas fa-shipping-fast"></i>
                                    <span>Đã gửi hàng</span>
                                    <small class="text-muted">{{ $order->shipped_at->format('d/m/Y H:i') }}</small>
                                </div>
                                @endif
                                @if($order->delivered_at)
                                <div class="timeline-item completed">
                                    <i class="fas fa-check-double"></i>
                                    <span>Đã giao hàng</span>
                                    <small class="text-muted">{{ $order->delivered_at->format('d/m/Y H:i') }}</small>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Platform Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-store me-2"></i>
                        Thông tin Platform
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Platform:</strong>
                            <div class="d-flex align-items-center mt-1">
                                <i class="{{ $order->platform_icon ?? 'fas fa-shopping-cart' }} me-2"></i>
                                {{ ucfirst($order->platform) }}
                            </div>
                        </div>
                        <div class="col-md-3">
                            <strong>Tài khoản:</strong>
                            <div class="mt-1">{{ $order->platform_account }}</div>
                        </div>
                        <div class="col-md-3">
                            <strong>Buyer ID:</strong>
                            <div class="mt-1">{{ $order->buyer_id ?: 'N/A' }}</div>
                        </div>
                        <div class="col-md-3">
                            <strong>ID ngoài:</strong>
                            <div class="mt-1">{{ $order->external_order_id ?: 'N/A' }}</div>
                        </div>
                    </div>
                    @if($order->product_link)
                    <div class="row mt-3">
                        <div class="col-12">
                            <strong>Link sản phẩm:</strong>
                            <div class="mt-1">
                                <a href="{{ $order->product_link }}" target="_blank" class="text-decoration-none">
                                    {{ $order->product_link }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Customer Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        Thông tin khách hàng
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Tên khách hàng:</strong>
                            <div class="mt-1">{{ $order->customer_name }}</div>
                            
                            <strong class="mt-3 d-block">Email:</strong>
                            <div class="mt-1">
                                <a href="mailto:{{ $order->customer_email }}">{{ $order->customer_email }}</a>
                            </div>
                            
                            <strong class="mt-3 d-block">Số điện thoại:</strong>
                            <div class="mt-1">
                                <a href="tel:{{ $order->customer_phone }}">{{ $order->customer_phone }}</a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <strong>Địa chỉ giao hàng:</strong>
                            <div class="mt-1 p-2 bg-light rounded">
                                {{ $order->shipping_address }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Sản phẩm đặt hàng
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Sản phẩm</th>
                                    <th>SKU</th>
                                    <th>Supplier</th>
                                    <th>Số lượng</th>
                                    <th>Giá gốc</th>
                                    <th>Giá bán</th>
                                    <th>Tổng</th>
                                    <th>Lợi nhuận</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($order->orderItems as $item)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($item->product && $item->product->images)
                                                <img src="{{ asset('storage/' . $item->product->images[0]) }}" 
                                                     alt="{{ $item->product_name }}" 
                                                     class="rounded me-2" 
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                            @endif
                                            <div>
                                                <div class="fw-bold">{{ $item->product_name }}</div>
                                                @if($item->product)
                                                    <small class="text-muted">{{ $item->product->category->name ?? 'N/A' }}</small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $item->product_sku }}</td>
                                    <td>{{ $item->product->supplier->name ?? 'N/A' }}</td>
                                    <td>{{ $item->quantity }}</td>
                                    <td>{{ number_format($item->product->cost_price ?? 0, 0, ',', '.') }}đ</td>
                                    <td>{{ number_format($item->unit_price, 0, ',', '.') }}đ</td>
                                    <td>{{ number_format($item->total_price, 0, ',', '.') }}đ</td>
                                    <td>
                                        @php
                                            $itemProfit = $item->product ? 
                                                ($item->unit_price - $item->product->cost_price) * $item->quantity : 0;
                                        @endphp
                                        <span class="text-{{ $itemProfit >= 0 ? 'success' : 'danger' }} fw-bold">
                                            {{ number_format($itemProfit, 0, ',', '.') }}đ
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            @if($order->notes)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        Ghi chú
                    </h5>
                </div>
                <div class="card-body">
                    <div class="p-2 bg-light rounded">
                        {{ $order->notes }}
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Right Column - Summary & Actions -->
        <div class="col-lg-4">
            <!-- Financial Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Tổng kết tài chính
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tổng giá bán:</span>
                        <span class="fw-bold">{{ number_format($order->subtotal, 0, ',', '.') }}đ</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tổng giá gốc:</span>
                        <span>{{ number_format($order->cost_amount, 0, ',', '.') }}đ</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Phí platform:</span>
                        <span>{{ number_format($order->platform_fee, 0, ',', '.') }}đ</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Phí vận chuyển:</span>
                        <span>{{ number_format($order->shipping_fee, 0, ',', '.') }}đ</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-2 fs-5 fw-bold">
                        <span>Tổng tiền:</span>
                        <span class="text-primary">{{ number_format($order->total_amount, 0, ',', '.') }}đ</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-success">Lợi nhuận:</span>
                        <span class="text-{{ $order->profit_amount >= 0 ? 'success' : 'danger' }} fw-bold">
                            {{ number_format($order->profit_amount, 0, ',', '.') }}đ
                        </span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-info">Margin:</span>
                        <span class="text-info">{{ number_format($order->profit_margin, 1) }}%</span>
                    </div>
                    
                    @if($order->profit_amount < 0)
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>Cảnh báo:</strong> Đơn hàng này bị lỗ!
                    </div>
                    @endif
                </div>
            </div>

            <!-- Assignment Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>
                        Thông tin phân công
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Team:</strong>
                        <div class="mt-1">
                            <span class="badge bg-primary">{{ $order->team->name ?? 'N/A' }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <strong>Người tạo:</strong>
                        <div class="mt-1">{{ $order->user->name ?? 'N/A' }}</div>
                    </div>
                    <div class="mb-3">
                        <strong>Được giao cho:</strong>
                        <div class="mt-1">
                            @if($order->assigned_to)
                                {{ $order->assignedUser->name ?? 'N/A' }}
                            @else
                                <span class="text-muted">Chưa được giao</span>
                            @endif
                        </div>
                    </div>
                    <div>
                        <strong>Ngày tạo:</strong>
                        <div class="mt-1">{{ $order->created_at->format('d/m/Y H:i') }}</div>
                    </div>
                </div>
            </div>

            <!-- Tracking Information -->
            @if($order->tracking_info)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Thông tin vận chuyển
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($order->tracking_info as $key => $value)
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                        <span>{{ $value }}</span>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Thao tác nhanh
                    </h5>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-outline-primary w-100 mb-2" onclick="printOrder()">
                        <i class="fas fa-print me-1"></i>
                        In đơn hàng
                    </button>
                    <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="copyOrderInfo()">
                        <i class="fas fa-copy me-1"></i>
                        Copy thông tin
                    </button>
                    <button type="button" class="btn btn-outline-success w-100" onclick="sendEmail()">
                        <i class="fas fa-envelope me-1"></i>
                        Gửi email khách hàng
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
    border-left: 2px solid #dee2e6;
}

.timeline-item:last-child {
    border-left: none;
}

.timeline-item.completed {
    border-left-color: #28a745;
}

.timeline-item i {
    position: absolute;
    left: -9px;
    top: 0;
    background: #fff;
    color: #dee2e6;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
}

.timeline-item.completed i {
    color: #28a745;
    border-color: #28a745;
}

.timeline-item span {
    display: block;
    font-weight: 500;
}

.timeline-item small {
    display: block;
    margin-top: 2px;
}
</style>
@endpush

@push('scripts')
<script>
function updateStatus(status) {
    if (confirm(`Bạn có chắc chắn muốn cập nhật trạng thái thành "${status}"?`)) {
        fetch(`{{ route('orders.enhanced.update', $order) }}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                status: status,
                _method: 'PUT'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi cập nhật trạng thái');
        });
    }
}

function printOrder() {
    window.print();
}

function copyOrderInfo() {
    const orderInfo = `
Đơn hàng: {{ $order->order_number }}
Khách hàng: {{ $order->customer_name }}
Platform: {{ ucfirst($order->platform) }} ({{ $order->platform_account }})
Tổng tiền: {{ number_format($order->total_amount, 0, ',', '.') }}đ
Lợi nhuận: {{ number_format($order->profit_amount, 0, ',', '.') }}đ
Trạng thái: {{ ucfirst($order->status) }}
    `.trim();
    
    navigator.clipboard.writeText(orderInfo).then(() => {
        alert('Đã copy thông tin đơn hàng!');
    });
}

function sendEmail() {
    const subject = `Thông tin đơn hàng #{{ $order->order_number }}`;
    const body = `Xin chào {{ $order->customer_name }},\n\nCảm ơn bạn đã đặt hàng. Thông tin đơn hàng của bạn:\n\nMã đơn hàng: {{ $order->order_number }}\nTổng tiền: {{ number_format($order->total_amount, 0, ',', '.') }}đ\nTrạng thái: {{ ucfirst($order->status) }}\n\nTrân trọng,\nDropship Manager`;
    
    window.location.href = `mailto:{{ $order->customer_email }}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
}
</script>
@endpush
@endsection
