<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Team;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // <PERSON><PERSON><PERSON>i<PERSON>, đ<PERSON><PERSON> bảo có ít nhất một team
        if (Team::count() == 0) {
            $defaultTeam = Team::create([
                'name' => 'Default Team',
                'code' => 'DEFAULT',
                'description' => 'Team mặc định cho users chưa được phân team',
                'status' => 'active'
            ]);
        } else {
            $defaultTeam = Team::first();
        }

        // Gán tất cả users chưa có team vào default team
        User::whereNull('team_id')->update(['team_id' => $defaultTeam->id]);

        // Drop foreign key constraint trước khi modify
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['team_id']);
        });

        // Modify column thành NOT NULL
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('team_id')->nullable(false)->change();
        });

        // Tạo lại foreign key constraint với CASCADE
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('team_id')->references('id')->on('teams')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->change();
        });
    }
};
