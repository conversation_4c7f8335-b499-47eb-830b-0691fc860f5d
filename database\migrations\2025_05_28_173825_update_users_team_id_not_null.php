<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Team;
use App\Models\User;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> bảo có ít nhất một team
        if (Team::count() == 0) {
            $defaultTeam = Team::create([
                'name' => 'Default Team',
                'code' => 'DEFAULT',
                'description' => 'Team mặc định cho users chưa được phân team',
                'status' => 'active'
            ]);
        } else {
            $defaultTeam = Team::first();
        }

        // Gán tất cả users chưa có team vào default team
        User::whereNull('team_id')->update(['team_id' => $defaultTeam->id]);

        // Cập nhật constraint để team_id không được null
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->change();
        });
    }
};
