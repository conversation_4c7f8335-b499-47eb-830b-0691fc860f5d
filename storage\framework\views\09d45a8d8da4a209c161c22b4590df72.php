<?php $__env->startSection('title', 'Dashboard Tà<PERSON> chính - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Dashboard Tài chính'); ?>

<?php $__env->startSection('page-actions'); ?>
<div class="btn-group" role="group">
    <?php if(auth()->user()->isSeller()): ?>
        <a href="<?php echo e(route('finance.payouts.quick-create')); ?>" class="btn btn-success">
            <i class="fas fa-plus me-1"></i>
            Nhập Payout nhanh
        </a>
    <?php endif; ?>
    <a href="<?php echo e(route('finance.profit-report')); ?>" class="btn btn-info">
        <i class="fas fa-chart-line me-1"></i>
        Báo c<PERSON>o <PERSON>
    </a>
    <a href="<?php echo e(route('finance.expenses.create')); ?>" class="btn btn-warning">
        <i class="fas fa-plus me-1"></i>
        Thêm Chi phí
    </a>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Filter Controls -->
<?php if(auth()->user()->isAdmin()): ?>
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('finance.dashboard')); ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Team</label>
                <select name="team_id" class="form-select">
                    <option value="">Tất cả teams</option>
                    <?php $__currentLoopData = $teams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($team->id); ?>" <?php echo e(request('team_id') == $team->id ? 'selected' : ''); ?>>
                            <?php echo e($team->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Thời gian</label>
                <select name="period" class="form-select">
                    <option value="week" <?php echo e($period == 'week' ? 'selected' : ''); ?>>7 ngày gần nhất</option>
                    <option value="month" <?php echo e($period == 'month' ? 'selected' : ''); ?>>30 ngày gần nhất</option>
                    <option value="year" <?php echo e($period == 'year' ? 'selected' : ''); ?>>12 tháng gần nhất</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i>
                    Lọc
                </button>
                <a href="<?php echo e(route('finance.dashboard')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Reset
                </a>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Tổng Payout
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['total_payouts'])); ?>đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Tổng Chi phí
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['total_expenses'])); ?>đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-<?php echo e($stats['total_profit'] >= 0 ? 'primary' : 'warning'); ?> shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-<?php echo e($stats['total_profit'] >= 0 ? 'primary' : 'warning'); ?> text-uppercase mb-1">
                            Lợi nhuận tổng
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo e(number_format($stats['total_profit'])); ?>đ
                            <small class="text-muted">(<?php echo e($stats['profit_margin']); ?>%)</small>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Lợi nhuận tháng này
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['monthly_profit'])); ?>đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Stats -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Payout tháng này
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['monthly_payouts'])); ?>đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Chi phí tháng này
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e(number_format($stats['monthly_expenses'])); ?>đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Tỷ lệ Lợi nhuận
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['profit_margin']); ?>%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Time Series Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Biểu đồ Tài chính theo thời gian</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="financeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Performance Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Hiệu suất Teams</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="teamChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tables Row -->
<div class="row">
    <!-- Team Stats Table -->
    <?php if(auth()->user()->isAdmin()): ?>
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Thống kê theo Team</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Team</th>
                                <th>Payout</th>
                                <th>Chi phí</th>
                                <th>Lợi nhuận</th>
                                <th>Tỷ lệ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $teamStats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <strong><?php echo e($team['name']); ?></strong>
                                    <br><small class="text-muted"><?php echo e($team['code']); ?></small>
                                </td>
                                <td>
                                    <span class="text-success"><?php echo e(number_format($team['payouts'])); ?>đ</span>
                                </td>
                                <td>
                                    <span class="text-danger"><?php echo e(number_format($team['expenses'])); ?>đ</span>
                                </td>
                                <td>
                                    <span class="text-<?php echo e($team['profit'] >= 0 ? 'primary' : 'warning'); ?>">
                                        <?php echo e(number_format($team['profit'])); ?>đ
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo e($team['profit_margin'] >= 0 ? 'success' : 'danger'); ?>">
                                        <?php echo e($team['profit_margin']); ?>%
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center">Chưa có dữ liệu</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Recent Transactions -->
    <div class="col-lg-<?php echo e(auth()->user()->isAdmin() ? '6' : '12'); ?> mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Giao dịch gần đây</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Loại</th>
                                <th>Mô tả</th>
                                <th>Số tiền</th>
                                <th>Ngày</th>
                                <th>Trạng thái</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $recentPayouts->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payout): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><span class="badge bg-success">Payout</span></td>
                                <td><?php echo e($payout->ebayAccount->account_name); ?></td>
                                <td class="text-success">+<?php echo e($payout->formatted_amount_vnd); ?></td>
                                <td><?php echo e($payout->payout_date->format('d/m/Y')); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo e($payout->status == 'completed' ? 'success' : 'warning'); ?>">
                                        <?php echo e($payout->status_label); ?>

                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                            <?php $__currentLoopData = $recentExpenses->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $expense): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><span class="badge bg-danger">Chi phí</span></td>
                                <td><?php echo e($expense->title); ?></td>
                                <td class="text-danger">-<?php echo e($expense->formatted_amount); ?></td>
                                <td><?php echo e($expense->expense_date->format('d/m/Y')); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo e($expense->status == 'paid' ? 'success' : 'warning'); ?>">
                                        <?php echo e($expense->status_label); ?>

                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Finance Chart
const financeCtx = document.getElementById('financeChart').getContext('2d');
const financeChart = new Chart(financeCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($timeStats, 'date')); ?>,
        datasets: [{
            label: 'Payout',
            data: <?php echo json_encode(array_column($timeStats, 'payouts')); ?>,
            borderColor: 'rgb(34, 197, 94)',
            backgroundColor: 'rgba(34, 197, 94, 0.1)',
            tension: 0.1
        }, {
            label: 'Chi phí',
            data: <?php echo json_encode(array_column($timeStats, 'expenses')); ?>,
            borderColor: 'rgb(239, 68, 68)',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            tension: 0.1
        }, {
            label: 'Lợi nhuận',
            data: <?php echo json_encode(array_column($timeStats, 'profit')); ?>,
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat('vi-VN').format(value) + 'đ';
                    }
                }
            }
        }
    }
});

// Team Chart
<?php if(auth()->user()->isAdmin()): ?>
const teamCtx = document.getElementById('teamChart').getContext('2d');
const teamChart = new Chart(teamCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode($teamStats->pluck('name')); ?>,
        datasets: [{
            data: <?php echo json_encode($teamStats->pluck('profit')); ?>,
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619', '#e02d1b'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: false
        },
        cutoutPercentage: 80,
    },
});
<?php endif; ?>
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/finance/dashboard.blade.php ENDPATH**/ ?>