<?php $__env->startSection('title', 'Quản lý Nhà cung cấp - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý Nhà cung cấp'); ?>

<?php $__env->startSection('content'); ?>
<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('suppliers.index')); ?>" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Tên, công ty, email..." value="<?php echo e(request('search')); ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                    <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Ngừng hoạt động</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Sắp xếp</label>
                <select name="sort_by" class="form-select">
                    <option value="created_at" <?php echo e(request('sort_by') == 'created_at' ? 'selected' : ''); ?>>Ngày tạo</option>
                    <option value="name" <?php echo e(request('sort_by') == 'name' ? 'selected' : ''); ?>>Tên</option>
                    <option value="products_count" <?php echo e(request('sort_by') == 'products_count' ? 'selected' : ''); ?>>Số sản phẩm</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Suppliers Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách Nhà cung cấp</h6>
        <a href="<?php echo e(route('suppliers.create')); ?>" class="btn btn-primary btn-sm">
            <i class="fas fa-plus"></i> Thêm mới
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Tên</th>
                        <th>Công ty</th>
                        <th>Email</th>
                        <th>Điện thoại</th>
                        <th>Số sản phẩm</th>
                        <th>Hoa hồng</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $suppliers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <strong><?php echo e($supplier->name); ?></strong>
                        </td>
                        <td><?php echo e($supplier->company_name ?? '-'); ?></td>
                        <td><?php echo e($supplier->email); ?></td>
                        <td><?php echo e($supplier->phone ?? '-'); ?></td>
                        <td>
                            <span class="badge bg-info"><?php echo e($supplier->products_count); ?></span>
                        </td>
                        <td><?php echo e($supplier->commission_rate); ?>%</td>
                        <td>
                            <span class="badge bg-<?php echo e($supplier->status == 'active' ? 'success' : 'secondary'); ?>">
                                <?php echo e($supplier->status == 'active' ? 'Hoạt động' : 'Ngừng hoạt động'); ?>

                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo e(route('suppliers.show', $supplier)); ?>" class="btn btn-info btn-sm" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('suppliers.edit', $supplier)); ?>" class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?php echo e(route('suppliers.destroy', $supplier)); ?>" method="POST" class="d-inline" 
                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa nhà cung cấp này?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-danger btn-sm" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="8" class="text-center">Không có nhà cung cấp nào</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($suppliers->hasPages()): ?>
        <div class="d-flex justify-content-center">
            <?php echo e($suppliers->appends(request()->query())->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/suppliers/index.blade.php ENDPATH**/ ?>