<?php

namespace App\Http\Controllers;

use App\Models\EbayAccount;
use App\Models\Team;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EbayAccountController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Check if user is authenticated and has team
        if (!$user || !$user->team_id) {
            return redirect()->route('dashboard')->with('error', 'Please ensure you are assigned to a team.');
        }

        $query = EbayAccount::with(['team', 'seller']);

        // Apply team filter based on user role
        if (!$user->isAdmin()) {
            $query->where('team_id', $user->team_id);
        } elseif ($request->filled('team_id')) {
            $query->where('team_id', $request->team_id);
        }

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('account_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('store_name', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('seller_id')) {
            $query->where('seller_id', $request->seller_id);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $accounts = $query->paginate(20);

        // Get teams and sellers for filters
        $teams = $user->isAdmin() ? Team::all() : collect();
        $sellers = $user->isAdmin() ? User::where('role', 'seller')->get() : collect();

        return view('finance.accounts.index', compact('accounts', 'teams', 'sellers'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();

        // Check if user is authenticated and has team
        if (!$user || !$user->team_id) {
            return redirect()->route('dashboard')->with('error', 'Please ensure you are assigned to a team.');
        }

        $teams = $user->isAdmin() ? Team::all() : collect([$user->team]);
        $sellers = User::where('role', 'seller')->get();

        return view('finance.accounts.create', compact('teams', 'sellers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'seller_id' => 'nullable|exists:users,id',
            'account_name' => 'required|string|max:255',
            'email' => 'required|email|unique:ebay_accounts,email',
            'store_name' => 'nullable|string|max:255',
            'store_url' => 'nullable|url',
            'registration_date' => 'nullable|date',
            'status' => 'required|in:active,suspended,limited,closed',
            'notes' => 'nullable|string',
        ]);

        // Check permission
        if (!$user->isAdmin() && $validated['team_id'] != $user->team_id) {
            abort(403, 'Unauthorized access to team data');
        }

        $validated['created_by'] = $user->id;

        EbayAccount::create($validated);

        return redirect()->route('finance.accounts.index')
            ->with('success', 'Tài khoản eBay đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(EbayAccount $account)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $account->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to account data');
        }

        $account->load(['team', 'seller', 'payouts' => function($query) {
            $query->orderBy('payout_date', 'desc')->limit(10);
        }]);

        return view('finance.accounts.show', compact('account'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EbayAccount $account)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $account->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to account data');
        }

        $teams = $user->isAdmin() ? Team::all() : collect([$user->team]);
        $sellers = User::where('role', 'seller')->get();

        return view('finance.accounts.edit', compact('account', 'teams', 'sellers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EbayAccount $account)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $account->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to account data');
        }

        $validated = $request->validate([
            'team_id' => 'required|exists:teams,id',
            'seller_id' => 'nullable|exists:users,id',
            'account_name' => 'required|string|max:255',
            'email' => 'required|email|unique:ebay_accounts,email,' . $account->id,
            'store_name' => 'nullable|string|max:255',
            'store_url' => 'nullable|url',
            'registration_date' => 'nullable|date',
            'status' => 'required|in:active,suspended,limited,closed',
            'notes' => 'nullable|string',
        ]);

        // Check permission for team change
        if (!$user->isAdmin() && $validated['team_id'] != $user->team_id) {
            abort(403, 'Unauthorized team assignment');
        }

        $account->update($validated);

        return redirect()->route('finance.accounts.index')
            ->with('success', 'Tài khoản eBay đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EbayAccount $account)
    {
        $user = Auth::user();

        // Check permission
        if (!$user->isAdmin() && $account->team_id != $user->team_id) {
            abort(403, 'Unauthorized access to account data');
        }

        // Check if account has payouts
        if ($account->payouts()->count() > 0) {
            return redirect()->route('finance.accounts.index')
                ->with('error', 'Không thể xóa tài khoản có payout. Vui lòng xóa tất cả payout trước.');
        }

        $account->delete();

        return redirect()->route('finance.accounts.index')
            ->with('success', 'Tài khoản eBay đã được xóa thành công!');
    }
}
