<?php $__env->startSection('title', 'Quản lý đơn vị Checkout - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý đơn vị Checkout'); ?>

<?php $__env->startSection('page-actions'); ?>
<a href="<?php echo e(route('checkout.units.create')); ?>" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    Thêm đơn vị Checkout
</a>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Danh sách đơn vị Checkout</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="<?php echo e(route('checkout.units.index')); ?>" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control form-control-sm" name="search" 
                               placeholder="Tìm kiếm..." value="<?php echo e(request('search')); ?>">
                    </div>
                    <div class="col-auto">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                            <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="<?php echo e(route('checkout.units.index')); ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Tên đơn vị</th>
                        <th>Mã đơn vị</th>
                        <th>Tỷ lệ Checkout</th>
                        <th>Tổng đơn hàng</th>
                        <th>Hoàn thành</th>
                        <th>Sự cố</th>
                        <th>Tổng tiền</th>
                        <th>Đã thanh toán</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $checkoutUnits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <div>
                                <strong><?php echo e($unit->name); ?></strong>
                                <?php if($unit->contact_person): ?>
                                    <br><small class="text-muted"><?php echo e($unit->contact_person); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td><code><?php echo e($unit->code); ?></code></td>
                        <td>
                            <span class="badge bg-info"><?php echo e($unit->checkout_rate); ?>%</span>
                        </td>
                        <td>
                            <span class="badge bg-primary"><?php echo e($unit->checkout_orders_count); ?></span>
                        </td>
                        <td>
                            <span class="badge bg-success"><?php echo e($unit->completed_orders); ?></span>
                        </td>
                        <td>
                            <span class="badge bg-warning"><?php echo e($unit->problematic_orders); ?></span>
                        </td>
                        <td>
                            <strong><?php echo e(number_format($unit->total_checkout_amount)); ?>đ</strong>
                        </td>
                        <td>
                            <span class="text-success"><?php echo e(number_format($unit->total_paid_amount)); ?>đ</span>
                            <?php if($unit->pending_payment_amount > 0): ?>
                                <br><small class="text-danger">Còn nợ: <?php echo e(number_format($unit->pending_payment_amount)); ?>đ</small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($unit->status == 'active'): ?>
                                <span class="badge bg-success">Hoạt động</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Không hoạt động</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?php echo e(route('checkout.units.show', $unit)); ?>" class="btn btn-outline-info" title="Xem">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('checkout.units.edit', $unit)); ?>" class="btn btn-outline-primary" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if($unit->google_sheet_url): ?>
                                    <button type="button" class="btn btn-outline-success" title="Đồng bộ Google Sheets"
                                            onclick="syncGoogleSheet(<?php echo e($unit->id); ?>)">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                <?php endif; ?>
                                <a href="<?php echo e(route('checkout.units.export', $unit)); ?>" class="btn btn-outline-warning" title="Xuất dữ liệu">
                                    <i class="fas fa-download"></i>
                                </a>
                                <form action="<?php echo e(route('checkout.units.destroy', $unit)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger" title="Xóa"
                                            onclick="return confirm('Bạn có chắc chắn muốn xóa đơn vị checkout này?\n\nLưu ý: Chỉ có thể xóa đơn vị không có đơn hàng nào.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>Chưa có đơn vị checkout nào</p>
                                <a href="<?php echo e(route('checkout.units.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Thêm đơn vị checkout đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if($checkoutUnits->hasPages()): ?>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị <?php echo e($checkoutUnits->firstItem()); ?> - <?php echo e($checkoutUnits->lastItem()); ?> 
                trong tổng số <?php echo e($checkoutUnits->total()); ?> đơn vị
            </div>
            <div>
                <?php echo e($checkoutUnits->appends(request()->query())->links()); ?>

            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary"><?php echo e($checkoutUnits->total()); ?></h5>
                <p class="card-text">Tổng đơn vị</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success"><?php echo e($checkoutUnits->where('status', 'active')->count()); ?></h5>
                <p class="card-text">Đang hoạt động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info"><?php echo e($checkoutUnits->sum('checkout_orders_count')); ?></h5>
                <p class="card-text">Tổng đơn hàng</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning"><?php echo e($checkoutUnits->where('checkout_orders_count', 0)->count()); ?></h5>
                <p class="card-text">Chưa có đơn hàng</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
function syncGoogleSheet(unitId) {
    if (!confirm('Bạn có muốn đồng bộ dữ liệu từ Google Sheets?')) {
        return;
    }

    // Show loading
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch(`/checkout/units/${unitId}/sync-google-sheet`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Đồng bộ dữ liệu thành công!');
            location.reload();
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi đồng bộ dữ liệu!');
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/checkout/units/index.blade.php ENDPATH**/ ?>