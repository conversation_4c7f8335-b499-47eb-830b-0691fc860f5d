<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checkout_payments', function (Blueprint $table) {
            $table->id();
            $table->string('payment_number')->unique(); // Mã thanh toán
            $table->foreignId('checkout_unit_id')->constrained()->onDelete('cascade'); // Đơn vị checkout
            $table->foreignId('checkout_order_id')->nullable()->constrained()->onDelete('set null'); // Đơn checkout (nếu thanh toán cho 1 đơn cụ thể)
            $table->enum('payment_type', ['order', 'bulk', 'commission', 'adjustment']); // Loại thanh toán
            $table->decimal('amount', 12, 2); // Số tiền
            $table->enum('payment_method', ['bank_transfer', 'cash', 'e_wallet', 'other'])->default('bank_transfer'); // Phương thức thanh toán
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending'); // Trạng thái
            $table->text('description')->nullable(); // Mô tả
            $table->string('reference_number')->nullable(); // Số tham chiếu (mã giao dịch ngân hàng)
            $table->json('payment_details')->nullable(); // Chi tiết thanh toán (JSON)
            $table->date('payment_date'); // Ngày thanh toán
            $table->timestamp('processed_at')->nullable(); // Thời gian xử lý
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null'); // Người xử lý
            $table->text('notes')->nullable(); // Ghi chú
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checkout_payments');
    }
};
