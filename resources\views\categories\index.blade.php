@extends('layouts.app')

@section('title', '<PERSON><PERSON>ả<PERSON> lý danh mục - Dropship Manager')
@section('page-title', 'Quản lý danh mục')

@section('page-actions')
<a href="{{ route('categories.create') }}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    Thêm danh mục
</a>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Danh sách danh mục</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('categories.index') }}" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control form-control-sm" name="search" 
                               placeholder="Tìm kiếm..." value="{{ request('search') }}">
                    </div>
                    <div class="col-auto">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Hoạt động</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="{{ route('categories.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Hình ảnh</th>
                        <th>
                            <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc']) }}" 
                               class="text-decoration-none text-dark">
                                Tên danh mục
                                @if(request('sort_by') == 'name')
                                    <i class="fas fa-sort-{{ request('sort_order') == 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </a>
                        </th>
                        <th>Mô tả</th>
                        <th>
                            <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'sort_order', 'sort_order' => request('sort_order') == 'asc' ? 'desc' : 'asc']) }}" 
                               class="text-decoration-none text-dark">
                                Thứ tự
                                @if(request('sort_by') == 'sort_order')
                                    <i class="fas fa-sort-{{ request('sort_order') == 'asc' ? 'up' : 'down' }}"></i>
                                @endif
                            </a>
                        </th>
                        <th>Số sản phẩm</th>
                        <th>Trạng thái</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($categories as $category)
                    <tr>
                        <td>
                            @if($category->image)
                                <img src="{{ asset('storage/' . $category->image) }}" 
                                     alt="{{ $category->name }}" 
                                     class="img-thumbnail" 
                                     style="width: 50px; height: 50px; object-fit: cover;">
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            @endif
                        </td>
                        <td>
                            <div>
                                <strong>{{ $category->name }}</strong>
                            </div>
                            <small class="text-muted">{{ $category->slug }}</small>
                        </td>
                        <td>
                            @if($category->description)
                                {{ Str::limit($category->description, 80) }}
                            @else
                                <span class="text-muted">Chưa có mô tả</span>
                            @endif
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ $category->sort_order }}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $category->products_count }}</span>
                        </td>
                        <td>
                            @if($category->is_active)
                                <span class="badge bg-success">Hoạt động</span>
                            @else
                                <span class="badge bg-secondary">Không hoạt động</span>
                            @endif
                        </td>
                        <td>
                            <small class="text-muted">{{ $category->created_at->format('d/m/Y') }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ route('categories.show', $category) }}" class="btn btn-outline-info" title="Xem">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('categories.edit', $category) }}" class="btn btn-outline-primary" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('categories.destroy', $category) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger" title="Xóa"
                                            onclick="return confirm('Bạn có chắc chắn muốn xóa danh mục này?\n\nLưu ý: Chỉ có thể xóa danh mục không có sản phẩm nào.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-tags fa-3x mb-3"></i>
                                <p>Chưa có danh mục nào</p>
                                <a href="{{ route('categories.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Thêm danh mục đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($categories->hasPages())
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị {{ $categories->firstItem() }} - {{ $categories->lastItem() }} 
                trong tổng số {{ $categories->total() }} danh mục
            </div>
            <div>
                {{ $categories->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ $categories->total() }}</h5>
                <p class="card-text">Tổng danh mục</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ $categories->where('is_active', true)->count() }}</h5>
                <p class="card-text">Đang hoạt động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ $categories->sum('products_count') }}</h5>
                <p class="card-text">Tổng sản phẩm</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ $categories->where('products_count', 0)->count() }}</h5>
                <p class="card-text">Danh mục trống</p>
            </div>
        </div>
    </div>
</div>
@endsection
