@extends('layouts.app')

@section('title', 'Tạo đơn hàng mới')
@section('page-title', 'Tạo đơn hàng mới')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('orders.enhanced.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Quay lại danh sách
    </a>
    <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
        <i class="fas fa-file-import me-1"></i>
        Import từ CSV
    </button>
</div>
@endsection

@section('content')
<div class="container-fluid">
    <form action="{{ route('orders.enhanced.store') }}" method="POST" id="orderForm" enctype="multipart/form-data">
        @csrf
        
        <div class="row">
            <!-- Left Column - Order Information -->
            <div class="col-lg-8">
                <!-- Platform & Account Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-store me-2"></i>
                            Thông tin Platform
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Platform <span class="text-danger">*</span></label>
                                <select class="form-select" name="platform" id="platform" required>
                                    <option value="">-- Chọn Platform --</option>
                                    <option value="ebay" {{ old('platform') == 'ebay' ? 'selected' : '' }}>
                                        <i class="fab fa-ebay"></i> eBay
                                    </option>
                                    <option value="amazon" {{ old('platform') == 'amazon' ? 'selected' : '' }}>
                                        Amazon
                                    </option>
                                    <option value="shopify" {{ old('platform') == 'shopify' ? 'selected' : '' }}>
                                        Shopify
                                    </option>
                                    <option value="facebook" {{ old('platform') == 'facebook' ? 'selected' : '' }}>
                                        Facebook Marketplace
                                    </option>
                                    <option value="other" {{ old('platform') == 'other' ? 'selected' : '' }}>
                                        Khác
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Tài khoản Platform <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="platform_account" 
                                       value="{{ old('platform_account') }}" 
                                       placeholder="Tên tài khoản trên platform" required>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Buyer ID</label>
                                <input type="text" class="form-control" name="buyer_id" 
                                       value="{{ old('buyer_id') }}" 
                                       placeholder="ID người mua">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">ID đơn hàng ngoài</label>
                                <input type="text" class="form-control" name="external_order_id" 
                                       value="{{ old('external_order_id') }}" 
                                       placeholder="ID đơn hàng từ platform">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Link sản phẩm</label>
                                <input type="url" class="form-control" name="product_link" 
                                       value="{{ old('product_link') }}" 
                                       placeholder="https://...">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Thông tin khách hàng
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Tên khách hàng <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="customer_name" 
                                       value="{{ old('customer_name') }}" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" name="customer_email" 
                                       value="{{ old('customer_email') }}" required>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">Số điện thoại <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" name="customer_phone" 
                                       value="{{ old('customer_phone') }}" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Địa chỉ giao hàng <span class="text-danger">*</span></label>
                                <textarea class="form-control" name="shipping_address" rows="3" required>{{ old('shipping_address') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            Sản phẩm đặt hàng
                        </h5>
                        <button type="button" class="btn btn-sm btn-primary" onclick="addOrderItem()">
                            <i class="fas fa-plus me-1"></i>
                            Thêm sản phẩm
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="orderItems">
                            <!-- Order items will be added here dynamically -->
                        </div>
                        <div class="alert alert-info" id="noItemsAlert">
                            <i class="fas fa-info-circle me-1"></i>
                            Chưa có sản phẩm nào. Nhấn "Thêm sản phẩm" để bắt đầu.
                        </div>
                    </div>
                </div>

                <!-- Financial Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-dollar-sign me-2"></i>
                            Thông tin tài chính
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">Phí platform</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="platform_fee" 
                                           value="{{ old('platform_fee', 0) }}" min="0" step="0.01" 
                                           onchange="calculateTotals()">
                                    <span class="input-group-text">đ</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Phí vận chuyển</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" name="shipping_fee" 
                                           value="{{ old('shipping_fee', 0) }}" min="0" step="0.01" 
                                           onchange="calculateTotals()">
                                    <span class="input-group-text">đ</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Ghi chú</label>
                                <textarea class="form-control" name="notes" rows="2">{{ old('notes') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Summary & Actions -->
            <div class="col-lg-4">
                <!-- Assignment -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-cog me-2"></i>
                            Phân công
                        </h5>
                    </div>
                    <div class="card-body">
                        @if(auth()->user()->isAdmin())
                        <div class="mb-3">
                            <label class="form-label">Team</label>
                            <select class="form-select" name="team_id" required>
                                @foreach($teams as $team)
                                    <option value="{{ $team->id }}" 
                                            {{ old('team_id', auth()->user()->team_id) == $team->id ? 'selected' : '' }}>
                                        {{ $team->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @else
                        <input type="hidden" name="team_id" value="{{ auth()->user()->team_id }}">
                        <div class="mb-3">
                            <label class="form-label">Team</label>
                            <input type="text" class="form-control" value="{{ auth()->user()->team->name }}" readonly>
                        </div>
                        @endif
                        
                        <div class="mb-3">
                            <label class="form-label">Giao cho</label>
                            <select class="form-select" name="assigned_to">
                                <option value="">-- Chưa giao --</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" 
                                            {{ old('assigned_to') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }} ({{ $user->team->name ?? 'No team' }})
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            Tổng kết đơn hàng
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tổng giá bán:</span>
                            <span id="subtotalDisplay">0đ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tổng giá gốc:</span>
                            <span id="costTotalDisplay">0đ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí platform:</span>
                            <span id="platformFeeDisplay">0đ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí vận chuyển:</span>
                            <span id="shippingFeeDisplay">0đ</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-2 fw-bold">
                            <span>Tổng tiền:</span>
                            <span id="totalAmountDisplay">0đ</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-success">Lợi nhuận:</span>
                            <span id="profitDisplay" class="text-success fw-bold">0đ</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-info">Margin:</span>
                            <span id="marginDisplay" class="text-info">0%</span>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-save me-1"></i>
                            Tạo đơn hàng
                        </button>
                        <a href="{{ route('orders.enhanced.index') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-1"></i>
                            Hủy bỏ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Order Item Template -->
<template id="orderItemTemplate">
    <div class="order-item border rounded p-3 mb-3" data-index="">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <h6 class="mb-0">Sản phẩm #<span class="item-number"></span></h6>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOrderItem(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <label class="form-label">Sản phẩm <span class="text-danger">*</span></label>
                <select class="form-select product-select" name="items[][product_id]" required onchange="updateProductInfo(this)">
                    <option value="">-- Chọn sản phẩm --</option>
                    @foreach($products as $product)
                        <option value="{{ $product->id }}" 
                                data-cost="{{ $product->cost_price }}" 
                                data-price="{{ $product->selling_price }}"
                                data-sku="{{ $product->sku }}"
                                data-supplier="{{ $product->supplier->name ?? '' }}">
                            {{ $product->name }} ({{ $product->sku }})
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Số lượng <span class="text-danger">*</span></label>
                <input type="number" class="form-control quantity-input" name="items[][quantity]" 
                       min="1" value="1" required onchange="updateItemTotal(this)">
            </div>
            <div class="col-md-3">
                <label class="form-label">Giá bán <span class="text-danger">*</span></label>
                <div class="input-group">
                    <input type="number" class="form-control selling-price-input" name="items[][selling_price]" 
                           min="0" step="0.01" required onchange="updateItemTotal(this)">
                    <span class="input-group-text">đ</span>
                </div>
            </div>
        </div>
        
        <div class="row mt-2">
            <div class="col-md-3">
                <small class="text-muted">SKU: <span class="sku-display">-</span></small>
            </div>
            <div class="col-md-3">
                <small class="text-muted">Giá gốc: <span class="cost-display">-</span></small>
            </div>
            <div class="col-md-3">
                <small class="text-muted">Supplier: <span class="supplier-display">-</span></small>
            </div>
            <div class="col-md-3">
                <small class="text-success fw-bold">Tổng: <span class="item-total-display">0đ</span></small>
            </div>
        </div>
    </div>
</template>

@push('styles')
<style>
.order-item {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}
.order-item:hover {
    background-color: #e9ecef;
}
.product-select {
    max-width: 100%;
}
</style>
@endpush

@push('scripts')
<script>
let itemIndex = 0;
let products = @json($products);

// Add new order item
function addOrderItem() {
    const template = document.getElementById('orderItemTemplate');
    const clone = template.content.cloneNode(true);
    
    // Update index and item number
    const orderItem = clone.querySelector('.order-item');
    orderItem.setAttribute('data-index', itemIndex);
    clone.querySelector('.item-number').textContent = itemIndex + 1;
    
    // Update input names with proper index
    const inputs = clone.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.name) {
            input.name = input.name.replace('[]', `[${itemIndex}]`);
        }
    });
    
    document.getElementById('orderItems').appendChild(clone);
    document.getElementById('noItemsAlert').style.display = 'none';
    
    itemIndex++;
    calculateTotals();
}

// Remove order item
function removeOrderItem(button) {
    const orderItem = button.closest('.order-item');
    orderItem.remove();
    
    // Check if no items left
    const remainingItems = document.querySelectorAll('.order-item');
    if (remainingItems.length === 0) {
        document.getElementById('noItemsAlert').style.display = 'block';
    }
    
    // Renumber items
    remainingItems.forEach((item, index) => {
        item.querySelector('.item-number').textContent = index + 1;
    });
    
    calculateTotals();
}

// Update product information when product is selected
function updateProductInfo(select) {
    const option = select.selectedOptions[0];
    const orderItem = select.closest('.order-item');
    
    if (option.value) {
        const costPrice = parseFloat(option.dataset.cost) || 0;
        const sellingPrice = parseFloat(option.dataset.price) || 0;
        const sku = option.dataset.sku || '-';
        const supplier = option.dataset.supplier || '-';
        
        // Update displays
        orderItem.querySelector('.sku-display').textContent = sku;
        orderItem.querySelector('.cost-display').textContent = formatCurrency(costPrice);
        orderItem.querySelector('.supplier-display').textContent = supplier;
        
        // Update selling price input
        orderItem.querySelector('.selling-price-input').value = sellingPrice;
        
        updateItemTotal(select);
    } else {
        // Clear displays
        orderItem.querySelector('.sku-display').textContent = '-';
        orderItem.querySelector('.cost-display').textContent = '-';
        orderItem.querySelector('.supplier-display').textContent = '-';
        orderItem.querySelector('.selling-price-input').value = '';
        orderItem.querySelector('.item-total-display').textContent = '0đ';
    }
}

// Update item total
function updateItemTotal(input) {
    const orderItem = input.closest('.order-item');
    const quantity = parseFloat(orderItem.querySelector('.quantity-input').value) || 0;
    const sellingPrice = parseFloat(orderItem.querySelector('.selling-price-input').value) || 0;
    
    const itemTotal = quantity * sellingPrice;
    orderItem.querySelector('.item-total-display').textContent = formatCurrency(itemTotal);
    
    calculateTotals();
}

// Calculate all totals
function calculateTotals() {
    let subtotal = 0;
    let costTotal = 0;
    
    document.querySelectorAll('.order-item').forEach(item => {
        const productSelect = item.querySelector('.product-select');
        const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
        const sellingPrice = parseFloat(item.querySelector('.selling-price-input').value) || 0;
        
        subtotal += quantity * sellingPrice;
        
        if (productSelect.selectedOptions[0]) {
            const costPrice = parseFloat(productSelect.selectedOptions[0].dataset.cost) || 0;
            costTotal += quantity * costPrice;
        }
    });
    
    const platformFee = parseFloat(document.querySelector('input[name="platform_fee"]').value) || 0;
    const shippingFee = parseFloat(document.querySelector('input[name="shipping_fee"]').value) || 0;
    const totalAmount = subtotal + shippingFee;
    const profit = subtotal - costTotal - platformFee;
    const margin = costTotal > 0 ? (profit / costTotal) * 100 : 0;
    
    // Update displays
    document.getElementById('subtotalDisplay').textContent = formatCurrency(subtotal);
    document.getElementById('costTotalDisplay').textContent = formatCurrency(costTotal);
    document.getElementById('platformFeeDisplay').textContent = formatCurrency(platformFee);
    document.getElementById('shippingFeeDisplay').textContent = formatCurrency(shippingFee);
    document.getElementById('totalAmountDisplay').textContent = formatCurrency(totalAmount);
    document.getElementById('profitDisplay').textContent = formatCurrency(profit);
    document.getElementById('marginDisplay').textContent = margin.toFixed(1) + '%';
    
    // Update profit color
    const profitElement = document.getElementById('profitDisplay');
    profitElement.className = profit >= 0 ? 'text-success fw-bold' : 'text-danger fw-bold';
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('vi-VN').format(amount) + 'đ';
}

// Initialize with one item
document.addEventListener('DOMContentLoaded', function() {
    addOrderItem();
});

// Form validation
document.getElementById('orderForm').addEventListener('submit', function(e) {
    const orderItems = document.querySelectorAll('.order-item');
    if (orderItems.length === 0) {
        e.preventDefault();
        alert('Vui lòng thêm ít nhất một sản phẩm!');
        return false;
    }
    
    // Validate each item
    let isValid = true;
    orderItems.forEach(item => {
        const productSelect = item.querySelector('.product-select');
        const quantity = item.querySelector('.quantity-input');
        const sellingPrice = item.querySelector('.selling-price-input');
        
        if (!productSelect.value || !quantity.value || !sellingPrice.value) {
            isValid = false;
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('Vui lòng điền đầy đủ thông tin cho tất cả sản phẩm!');
        return false;
    }
});
</script>
@endpush
@endsection
