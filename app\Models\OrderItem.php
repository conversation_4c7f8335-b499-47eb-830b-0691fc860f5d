<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'unit_price',
        'quantity',
        'total_price',
        'product_attributes'
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'quantity' => 'integer',
        'product_attributes' => 'array'
    ];

    // Tự động tính total_price khi tạo order item mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($orderItem) {
            $orderItem->total_price = $orderItem->unit_price * $orderItem->quantity;
        });

        static::updating(function ($orderItem) {
            $orderItem->total_price = $orderItem->unit_price * $orderItem->quantity;
        });
    }

    // Relationship với Order
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    // Relationship với Product
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    // Tính profit cho item này
    public function getProfitAttribute()
    {
        if ($this->product && $this->product->cost_price > 0) {
            return ($this->unit_price - $this->product->cost_price) * $this->quantity;
        }
        return 0;
    }

    // Tính profit margin cho item này
    public function getProfitMarginAttribute()
    {
        if ($this->product && $this->product->cost_price > 0) {
            return (($this->unit_price - $this->product->cost_price) / $this->product->cost_price) * 100;
        }
        return 0;
    }
}
