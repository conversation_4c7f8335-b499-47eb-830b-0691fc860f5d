<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure all users have a team_id
        // First, create a default team if none exists
        $defaultTeam = DB::table('teams')->first();
        
        if (!$defaultTeam) {
            $teamId = DB::table('teams')->insertGetId([
                'name' => 'Default Team',
                'description' => 'Default team for users without team assignment',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        } else {
            $teamId = $defaultTeam->id;
        }
        
        // Update all users without team_id to have the default team
        DB::table('users')
            ->whereNull('team_id')
            ->update(['team_id' => $teamId]);
        
        // Make team_id NOT NULL
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('team_id')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Make team_id nullable again
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('team_id')->nullable()->change();
        });
    }
};
