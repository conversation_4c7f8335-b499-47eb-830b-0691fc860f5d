<?php

namespace App\Services;

use App\Models\Team;
use App\Models\User;
use App\Models\EbayPayout;
use App\Models\MonthlyExpense;
use App\Models\CheckoutUnit;
use App\Models\CheckoutOrder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CacheService
{
    // Cache durations (in seconds)
    const DASHBOARD_CACHE_DURATION = 300; // 5 minutes
    const TEAM_STATS_CACHE_DURATION = 600; // 10 minutes
    const FINANCIAL_STATS_CACHE_DURATION = 900; // 15 minutes
    const USER_NOTIFICATIONS_CACHE_DURATION = 60; // 1 minute

    /**
     * Cache dashboard statistics
     */
    public function cacheDashboardStats($teamId = null)
    {
        $cacheKey = 'dashboard_stats_' . ($teamId ?? 'all');
        
        return Cache::remember($cacheKey, self::DASHBOARD_CACHE_DURATION, function () use ($teamId) {
            return $this->generateDashboardStats($teamId);
        });
    }

    /**
     * Cache team financial statistics
     */
    public function cacheTeamFinancialStats($teamId)
    {
        $cacheKey = "team_financial_stats_{$teamId}";
        
        return Cache::remember($cacheKey, self::FINANCIAL_STATS_CACHE_DURATION, function () use ($teamId) {
            return $this->generateTeamFinancialStats($teamId);
        });
    }

    /**
     * Cache checkout unit statistics
     */
    public function cacheCheckoutUnitStats($unitId)
    {
        $cacheKey = "checkout_unit_stats_{$unitId}";
        
        return Cache::remember($cacheKey, self::TEAM_STATS_CACHE_DURATION, function () use ($unitId) {
            return $this->generateCheckoutUnitStats($unitId);
        });
    }

    /**
     * Cache user notification count
     */
    public function cacheUserNotificationCount($userId)
    {
        $cacheKey = "user_notifications_count_{$userId}";
        
        return Cache::remember($cacheKey, self::USER_NOTIFICATIONS_CACHE_DURATION, function () use ($userId) {
            return \App\Models\Notification::where('user_id', $userId)->unread()->count();
        });
    }

    /**
     * Cache monthly profit data
     */
    public function cacheMonthlyProfitData($teamId, $year, $month)
    {
        $cacheKey = "monthly_profit_{$teamId}_{$year}_{$month}";
        
        return Cache::remember($cacheKey, self::FINANCIAL_STATS_CACHE_DURATION, function () use ($teamId, $year, $month) {
            return $this->generateMonthlyProfitData($teamId, $year, $month);
        });
    }

    /**
     * Cache exchange rates
     */
    public function cacheExchangeRate($fromCurrency, $toCurrency, $rate)
    {
        $cacheKey = "exchange_rate_{$fromCurrency}_{$toCurrency}";
        Cache::put($cacheKey, $rate, 3600); // 1 hour
    }

    /**
     * Get cached exchange rate
     */
    public function getCachedExchangeRate($fromCurrency, $toCurrency)
    {
        $cacheKey = "exchange_rate_{$fromCurrency}_{$toCurrency}";
        return Cache::get($cacheKey);
    }

    /**
     * Invalidate cache when data changes
     */
    public function invalidateTeamCache($teamId)
    {
        $patterns = [
            "dashboard_stats_{$teamId}",
            "team_financial_stats_{$teamId}",
            "monthly_profit_{$teamId}_*"
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // For wildcard patterns, we need to find and delete matching keys
                $this->invalidateByPattern($pattern);
            } else {
                Cache::forget($pattern);
            }
        }

        // Also invalidate global dashboard stats
        Cache::forget('dashboard_stats_all');
    }

    /**
     * Invalidate checkout unit cache
     */
    public function invalidateCheckoutUnitCache($unitId)
    {
        Cache::forget("checkout_unit_stats_{$unitId}");
        
        // Also invalidate related team cache
        $unit = CheckoutUnit::find($unitId);
        if ($unit && $unit->team_id) {
            $this->invalidateTeamCache($unit->team_id);
        }
    }

    /**
     * Invalidate user notification cache
     */
    public function invalidateUserNotificationCache($userId)
    {
        Cache::forget("user_notifications_count_{$userId}");
    }

    /**
     * Invalidate all dashboard caches
     */
    public function invalidateAllDashboardCache()
    {
        $this->invalidateByPattern('dashboard_stats_*');
        $this->invalidateByPattern('team_financial_stats_*');
        $this->invalidateByPattern('checkout_unit_stats_*');
    }

    /**
     * Generate dashboard statistics
     */
    private function generateDashboardStats($teamId = null)
    {
        $payoutQuery = EbayPayout::where('status', 'completed');
        $expenseQuery = MonthlyExpense::where('status', 'paid');
        $checkoutOrderQuery = CheckoutOrder::query();

        if ($teamId) {
            $payoutQuery->where('team_id', $teamId);
            $expenseQuery->where('team_id', $teamId);
            // For checkout orders, we need to join with checkout_units
            $checkoutOrderQuery->whereHas('checkoutUnit', function($q) use ($teamId) {
                $q->where('team_id', $teamId);
            });
        }

        $totalPayouts = $payoutQuery->sum('amount_vnd');
        $totalExpenses = $expenseQuery->sum('amount');
        $totalProfit = $totalPayouts - $totalExpenses;

        // Monthly stats
        $currentMonth = now();
        $monthlyPayouts = $payoutQuery->whereYear('payout_date', $currentMonth->year)
                                     ->whereMonth('payout_date', $currentMonth->month)
                                     ->sum('amount_vnd');
        
        $monthlyExpenses = $expenseQuery->where('expense_month', $currentMonth->format('Y-m'))
                                       ->sum('amount');

        // Checkout stats
        $totalCheckoutOrders = $checkoutOrderQuery->count();
        $completedCheckoutOrders = $checkoutOrderQuery->where('status', 'completed')->count();

        return [
            'total_payouts' => $totalPayouts,
            'total_expenses' => $totalExpenses,
            'total_profit' => $totalProfit,
            'monthly_payouts' => $monthlyPayouts,
            'monthly_expenses' => $monthlyExpenses,
            'monthly_profit' => $monthlyPayouts - $monthlyExpenses,
            'profit_margin' => $totalPayouts > 0 ? round(($totalProfit / $totalPayouts) * 100, 2) : 0,
            'total_checkout_orders' => $totalCheckoutOrders,
            'completed_checkout_orders' => $completedCheckoutOrders,
            'checkout_completion_rate' => $totalCheckoutOrders > 0 ? round(($completedCheckoutOrders / $totalCheckoutOrders) * 100, 2) : 0,
            'cached_at' => now()
        ];
    }

    /**
     * Generate team financial statistics
     */
    private function generateTeamFinancialStats($teamId)
    {
        $team = Team::find($teamId);
        if (!$team) {
            return null;
        }

        $payouts = $team->ebayPayouts()->where('status', 'completed')->sum('amount_vnd');
        $expenses = $team->monthlyExpenses()->where('status', 'paid')->sum('amount');
        $profit = $payouts - $expenses;

        // Get monthly data for the last 12 months
        $monthlyData = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthPayouts = $team->getMonthlyPayouts($date->year, $date->month);
            $monthExpenses = $team->getMonthlyExpenses($date->year, $date->month);
            
            $monthlyData[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->format('m/Y'),
                'payouts' => $monthPayouts,
                'expenses' => $monthExpenses,
                'profit' => $monthPayouts - $monthExpenses
            ];
        }

        return [
            'team_id' => $teamId,
            'team_name' => $team->name,
            'total_payouts' => $payouts,
            'total_expenses' => $expenses,
            'total_profit' => $profit,
            'profit_margin' => $payouts > 0 ? round(($profit / $payouts) * 100, 2) : 0,
            'monthly_data' => $monthlyData,
            'member_count' => $team->members()->count(),
            'active_accounts' => $team->ebayAccounts()->where('status', 'active')->count(),
            'cached_at' => now()
        ];
    }

    /**
     * Generate checkout unit statistics
     */
    private function generateCheckoutUnitStats($unitId)
    {
        $unit = CheckoutUnit::find($unitId);
        if (!$unit) {
            return null;
        }

        $totalOrders = $unit->checkoutOrders()->count();
        $completedOrders = $unit->checkoutOrders()->where('status', 'completed')->count();
        $problematicOrders = $unit->checkoutOrders()->whereIn('status', ['cancelled', 'returned', 'refunded'])->count();
        
        $totalAmount = $unit->checkoutOrders()->sum('total_amount');
        $paidAmount = $unit->checkoutPayments()->where('status', 'completed')->sum('amount');

        return [
            'unit_id' => $unitId,
            'unit_name' => $unit->name,
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'problematic_orders' => $problematicOrders,
            'completion_rate' => $totalOrders > 0 ? round(($completedOrders / $totalOrders) * 100, 2) : 0,
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount,
            'pending_amount' => $totalAmount - $paidAmount,
            'cached_at' => now()
        ];
    }

    /**
     * Generate monthly profit data
     */
    private function generateMonthlyProfitData($teamId, $year, $month)
    {
        $payouts = EbayPayout::where('team_id', $teamId)
                            ->whereYear('payout_date', $year)
                            ->whereMonth('payout_date', $month)
                            ->where('status', 'completed')
                            ->sum('amount_vnd');

        $expenses = MonthlyExpense::where('team_id', $teamId)
                                 ->where('expense_month', $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT))
                                 ->where('status', 'paid')
                                 ->sum('amount');

        return [
            'team_id' => $teamId,
            'year' => $year,
            'month' => $month,
            'payouts' => $payouts,
            'expenses' => $expenses,
            'profit' => $payouts - $expenses,
            'profit_margin' => $payouts > 0 ? round((($payouts - $expenses) / $payouts) * 100, 2) : 0,
            'cached_at' => now()
        ];
    }

    /**
     * Invalidate cache by pattern (for Redis)
     */
    private function invalidateByPattern($pattern)
    {
        try {
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys(config('cache.prefix') . ':' . $pattern);
                
                foreach ($keys as $key) {
                    $cleanKey = str_replace(config('cache.prefix') . ':', '', $key);
                    Cache::forget($cleanKey);
                }
            } else {
                // For non-Redis cache drivers, we can't use patterns
                // So we'll just clear all cache (not ideal but works)
                Cache::flush();
            }
        } catch (\Exception $e) {
            // If pattern invalidation fails, just clear all cache
            Cache::flush();
        }
    }

    /**
     * Warm up cache for important data
     */
    public function warmUpCache()
    {
        // Warm up dashboard stats
        $this->cacheDashboardStats();

        // Warm up team stats for all active teams
        $teams = Team::where('status', 'active')->get();
        foreach ($teams as $team) {
            $this->cacheTeamFinancialStats($team->id);
        }

        // Warm up checkout unit stats
        $units = CheckoutUnit::where('status', 'active')->get();
        foreach ($units as $unit) {
            $this->cacheCheckoutUnitStats($unit->id);
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats()
    {
        try {
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $info = $redis->info();
                
                return [
                    'driver' => 'redis',
                    'used_memory' => $info['used_memory_human'] ?? 'N/A',
                    'connected_clients' => $info['connected_clients'] ?? 'N/A',
                    'total_commands_processed' => $info['total_commands_processed'] ?? 'N/A',
                    'keyspace_hits' => $info['keyspace_hits'] ?? 'N/A',
                    'keyspace_misses' => $info['keyspace_misses'] ?? 'N/A'
                ];
            }
            
            return [
                'driver' => config('cache.default'),
                'status' => 'active'
            ];
        } catch (\Exception $e) {
            return [
                'driver' => config('cache.default'),
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
}
