@extends('layouts.app')

@section('title', 'Import dữ liệu - Dropship Manager')
@section('page-title', 'Import dữ liệu')

@section('page-actions')
<a href="{{ route('import.template') }}" class="btn btn-success">
    <i class="fas fa-download me-1"></i>
    Tải template CSV
</a>
@endsection

@section('content')
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-import me-2"></i>
                    Import sản phẩm từ file CSV
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ route('import.products') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="mb-4">
                        <label for="csv_file" class="form-label">Chọn file CSV</label>
                        <input type="file" class="form-control @error('csv_file') is-invalid @enderror" 
                               id="csv_file" name="csv_file" accept=".csv,.txt" required>
                        @error('csv_file')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            Chỉ chấp nhận file CSV với dung lượng tối đa 10MB.
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>
                            Bắt đầu import
                        </button>
                    </div>
                </form>

                @if(session('import_errors') && count(session('import_errors')) > 0)
                    <div class="mt-4">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Có lỗi xảy ra trong quá trình import:</h6>
                            <ul class="mb-0">
                                @foreach(session('import_errors') as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Hướng dẫn
                </h5>
            </div>
            <div class="card-body">
                <h6>Cách sử dụng:</h6>
                <ol class="small">
                    <li>Tải template CSV mẫu bằng cách nhấn nút "Tải template CSV"</li>
                    <li>Mở file template bằng Excel hoặc Google Sheets</li>
                    <li>Điền thông tin sản phẩm vào các cột tương ứng</li>
                    <li>Lưu file dưới định dạng CSV</li>
                    <li>Upload file CSV và nhấn "Bắt đầu import"</li>
                </ol>

                <h6 class="mt-4">Các cột bắt buộc:</h6>
                <ul class="small">
                    <li><strong>name:</strong> Tên sản phẩm</li>
                    <li><strong>cost_price:</strong> Giá gốc</li>
                    <li><strong>selling_price:</strong> Giá bán</li>
                    <li><strong>category_name:</strong> Tên danh mục</li>
                    <li><strong>supplier_name:</strong> Tên nhà cung cấp</li>
                </ul>

                <h6 class="mt-4">Lưu ý:</h6>
                <ul class="small text-muted">
                    <li>Nếu danh mục hoặc nhà cung cấp chưa tồn tại, hệ thống sẽ tự động tạo mới</li>
                    <li>Nếu SKU trống, hệ thống sẽ tự động tạo SKU ngẫu nhiên</li>
                    <li>Nếu sản phẩm đã tồn tại (theo SKU), hệ thống sẽ cập nhật thông tin</li>
                    <li>Giá tiền có thể có dấu phẩy (,) để phân cách hàng nghìn</li>
                </ul>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Thống kê import
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ \App\Models\Product::count() }}</h4>
                            <small class="text-muted">Tổng sản phẩm</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ \App\Models\Category::count() }}</h4>
                        <small class="text-muted">Tổng danh mục</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-info mb-1">{{ \App\Models\Supplier::count() }}</h4>
                            <small class="text-muted">Nhà cung cấp</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning mb-1">{{ \App\Models\Product::whereRaw('stock_quantity <= min_stock_level')->count() }}</h4>
                        <small class="text-muted">Sắp hết hàng</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    Cấu trúc file CSV mẫu
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>name</th>
                                <th>sku</th>
                                <th>description</th>
                                <th>cost_price</th>
                                <th>selling_price</th>
                                <th>compare_price</th>
                                <th>stock_quantity</th>
                                <th>category_name</th>
                                <th>supplier_name</th>
                                <th>status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-muted">
                                <td>Áo thun nam</td>
                                <td>SKU-001</td>
                                <td>Áo thun nam cotton 100%</td>
                                <td>50000</td>
                                <td>100000</td>
                                <td>120000</td>
                                <td>100</td>
                                <td>Thời trang nam</td>
                                <td>Công ty ABC</td>
                                <td>active</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p class="text-muted small mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    Đây chỉ là một phần của các cột có sẵn. File template đầy đủ sẽ có thêm nhiều cột khác như 
                    short_description, weight, dimensions, supplier_email, is_featured, seo_title, seo_description.
                </p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.getElementById('csv_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileSize = file.size / 1024 / 1024; // MB
        if (fileSize > 10) {
            alert('File quá lớn! Vui lòng chọn file nhỏ hơn 10MB.');
            e.target.value = '';
            return;
        }
        
        const fileName = file.name;
        const fileExtension = fileName.split('.').pop().toLowerCase();
        if (!['csv', 'txt'].includes(fileExtension)) {
            alert('Vui lòng chọn file CSV (.csv hoặc .txt)');
            e.target.value = '';
            return;
        }
    }
});
</script>
@endpush
