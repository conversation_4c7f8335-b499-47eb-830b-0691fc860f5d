<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Team;
use App\Models\User;
use App\Models\MonthlyExpense;
use App\Models\CheckoutUnit;
use Illuminate\Support\Facades\Hash;

class SmartExpenseSeeder extends Seeder
{
    public function run()
    {
        // Tạo teams nếu chưa có
        $team1 = Team::firstOrCreate([
            'name' => 'Team Alpha'
        ], [
            'description' => 'Team Alpha - Dropshipping Team',
            'leader_id' => null,
            'status' => 'active'
        ]);

        $team2 = Team::firstOrCreate([
            'name' => 'Team Beta'
        ], [
            'description' => 'Team Beta - Dropshipping Team',
            'leader_id' => null,
            'status' => 'active'
        ]);

        // Tạo users với roles khác nhau
        $admin = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Admin User',
            'password' => Hash::make('password'),
            'team_id' => $team1->id,
            'role' => 'admin'
        ]);

        $teamLeader1 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Team Leader Alpha',
            'password' => Hash::make('password'),
            'team_id' => $team1->id,
            'role' => 'team_leader'
        ]);

        $teamLeader2 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Team Leader Beta',
            'password' => Hash::make('password'),
            'team_id' => $team2->id,
            'role' => 'team_leader'
        ]);

        $financeManager = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Finance Manager',
            'password' => Hash::make('password'),
            'team_id' => $team1->id,
            'role' => 'admin' // Thay đổi thành admin để tránh lỗi ENUM
        ]);

        $seller1 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Seller One',
            'password' => Hash::make('password'),
            'team_id' => $team1->id,
            'role' => 'seller'
        ]);

        $seller2 = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Seller Two',
            'password' => Hash::make('password'),
            'team_id' => $team2->id,
            'role' => 'seller'
        ]);

        // Update team leaders
        $team1->update(['leader_id' => $teamLeader1->id]);
        $team2->update(['leader_id' => $teamLeader2->id]);

        // Tạo checkout units
        $checkoutUnit1 = CheckoutUnit::firstOrCreate([
            'name' => 'Checkout Unit Alpha',
            'team_id' => $team1->id
        ], [
            'rate' => 15.0,
            'description' => 'Main checkout unit for Team Alpha',
            'status' => 'active'
        ]);

        $checkoutUnit2 = CheckoutUnit::firstOrCreate([
            'name' => 'Checkout Unit Beta',
            'team_id' => $team2->id
        ], [
            'rate' => 12.0,
            'description' => 'Main checkout unit for Team Beta',
            'status' => 'active'
        ]);

        // Tạo sample expenses với smart features
        $this->createSampleExpenses($team1, $seller1, $checkoutUnit1);
        $this->createSampleExpenses($team2, $seller2, $checkoutUnit2);

        $this->command->info('Smart Expense sample data created successfully!');
    }

    private function createSampleExpenses($team, $user, $checkoutUnit)
    {
        $expenses = [
            [
                'title' => 'Facebook Ads Campaign Q4',
                'description' => 'Facebook advertising campaign for holiday season products',
                'vendor' => 'Facebook Inc.',
                'amount' => 5000000,
                'category' => 'advertising',
                'expense_type' => 'advertising',
                'risk_level' => 'low',
                'fraud_score' => 15
            ],
            [
                'title' => 'GHN Shipping Costs',
                'description' => 'Giao hang nhanh shipping fees for customer orders',
                'vendor' => 'Giao Hang Nhanh',
                'amount' => 2500000,
                'category' => 'shipping',
                'expense_type' => 'shipping',
                'risk_level' => 'low',
                'fraud_score' => 10
            ],
            [
                'title' => 'Customer Refund - Order #12345',
                'description' => 'Refund for damaged product complaint',
                'vendor' => null,
                'amount' => 800000,
                'category' => 'refunds',
                'expense_type' => 'refunds',
                'risk_level' => 'medium',
                'fraud_score' => 25
            ],
            [
                'title' => 'Shopify Monthly Subscription',
                'description' => 'Monthly subscription fee for Shopify store',
                'vendor' => 'Shopify Inc.',
                'amount' => 1200000,
                'category' => 'tools_software',
                'expense_type' => 'tools_software',
                'risk_level' => 'low',
                'fraud_score' => 5
            ],
            [
                'title' => 'Office Supplies Purchase',
                'description' => 'Printer paper, pens, and office equipment',
                'vendor' => 'Office Depot',
                'amount' => 600000,
                'category' => 'office_supplies',
                'expense_type' => 'office_supplies',
                'risk_level' => 'low',
                'fraud_score' => 8
            ],
            [
                'title' => 'Internet & Phone Bills',
                'description' => 'Monthly internet and phone service charges',
                'vendor' => 'Viettel',
                'amount' => 1500000,
                'category' => 'utilities',
                'expense_type' => 'utilities',
                'risk_level' => 'low',
                'fraud_score' => 5
            ],
            [
                'title' => 'Business Trip to Supplier',
                'description' => 'Travel expenses for supplier meeting in Ho Chi Minh City',
                'vendor' => null,
                'amount' => 3200000,
                'category' => 'travel',
                'expense_type' => 'travel',
                'risk_level' => 'medium',
                'fraud_score' => 20
            ],
            [
                'title' => 'Team Lunch Meeting',
                'description' => 'Lunch meeting with potential suppliers',
                'vendor' => 'Golden Dragon Restaurant',
                'amount' => 450000,
                'category' => 'meals',
                'expense_type' => 'meals',
                'risk_level' => 'low',
                'fraud_score' => 12
            ],
            [
                'title' => 'Suspicious Large Payment',
                'description' => 'Large payment with unclear description',
                'vendor' => 'Unknown Vendor',
                'amount' => 15000000,
                'category' => 'other',
                'expense_type' => 'other',
                'risk_level' => 'high',
                'fraud_score' => 85
            ]
        ];

        foreach ($expenses as $index => $expenseData) {
            $expense = MonthlyExpense::create([
                'expense_number' => 'EXP-' . $team->id . '-' . date('Y') . '-' . str_pad($index + 1, 4, '0', STR_PAD_LEFT),
                'team_id' => $team->id,
                'created_by' => $user->id,
                'expense_type' => $expenseData['expense_type'],
                'title' => $expenseData['title'],
                'description' => $expenseData['description'],
                'vendor' => $expenseData['vendor'],
                'amount' => $expenseData['amount'],
                'currency' => 'VND',
                'expense_date' => now()->subDays(rand(1, 30)),
                'expense_month' => now()->format('Y-m'),
                'frequency' => 'one_time',
                'status' => $index < 6 ? 'approved' : 'pending',
                'category' => $expenseData['category'],
                'suggested_category' => $expenseData['category'],
                'categorization_confidence' => rand(75, 95),
                'category_manually_corrected' => false,
                'validation_results' => [
                    'overall_status' => $expenseData['risk_level'] === 'high' ? 'failed' : 'passed',
                    'flags' => $expenseData['risk_level'] === 'high' ? [
                        [
                            'type' => 'high_amount_alert',
                            'message' => 'Số tiền cao bất thường',
                            'severity' => 'high'
                        ]
                    ] : [],
                    'warnings' => [],
                    'errors' => []
                ],
                'fraud_score' => $expenseData['fraud_score'],
                'risk_level' => $expenseData['risk_level'],
                'fraud_indicators' => $expenseData['fraud_score'] > 50 ? ['large_round_amount', 'unknown_vendor'] : [],
                'approval_status' => $index < 6 ? 'approved' : 'pending',
                'approval_path' => $expenseData['risk_level'] === 'high' ? [
                    [
                        'level' => 'fraud_review',
                        'approver_role' => 'finance_manager',
                        'required_actions' => ['verify_receipt', 'contact_vendor'],
                        'timeout_hours' => 24
                    ]
                ] : [],
                'approval_history' => $index < 6 ? [
                    [
                        'step' => [
                            'level' => 'team_leader_approval',
                            'approver_role' => 'team_leader'
                        ],
                        'decision' => 'approved',
                        'approver_id' => $team->leader_id,
                        'approved_at' => now()->subDays(rand(1, 5)),
                        'comments' => 'Approved - legitimate business expense',
                        'processing_time' => rand(30, 180)
                    ]
                ] : [],
                'checkout_unit_id' => rand(0, 1) ? $checkoutUnit->id : null,
                'approved_by' => $index < 6 ? $team->leader_id : null,
                'approved_at' => $index < 6 ? now()->subDays(rand(1, 5)) : null,
                'notes' => 'Sample expense for testing Smart Expense Management System',
                'metadata' => [
                    'source' => 'seeder',
                    'test_data' => true
                ],
                'processed_at' => $index < 6 ? now()->subDays(rand(1, 5)) : null
            ]);
        }
    }
}
