#!/bin/bash

echo "========================================"
echo "   DROPSHIP MANAGER - AUTO INSTALLER"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check PHP
echo "[1/6] Checking PHP and Composer..."
if ! command -v php &> /dev/null; then
    echo -e "${RED}ERROR: PHP not found! Please install PHP first.${NC}"
    exit 1
fi

# Check Composer
if ! command -v composer &> /dev/null; then
    echo -e "${RED}ERROR: Composer not found! Please install Composer first.${NC}"
    exit 1
fi

echo -e "${GREEN}✓ PHP and Composer found${NC}"

# Install dependencies
echo "[2/6] Installing dependencies..."
composer install --no-dev --optimize-autoloader

# Setup environment
echo "[3/6] Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo -e "${GREEN}✓ .env file created from .env.example${NC}"
fi

# Generate key
echo "[4/6] Generating application key..."
php artisan key:generate

# Create storage link
echo "[5/6] Creating storage link..."
php artisan storage:link

# Set permissions
echo "[6/6] Setting permissions..."
chmod -R 755 storage
chmod -R 755 bootstrap/cache

echo
echo -e "${GREEN}Setup completed!${NC}"
echo
echo "========================================"
echo "           NEXT STEPS"
echo "========================================"
echo "1. Start MySQL service"
echo "2. Create database 'dropshipping_db'"
echo "3. Run: php artisan migrate"
echo "4. Run: php artisan db:seed"
echo "5. Run: php artisan serve"
echo "6. Visit: http://localhost:8000"
echo "========================================"
echo
