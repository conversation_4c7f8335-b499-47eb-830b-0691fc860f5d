<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CheckoutPayment extends Model
{
    protected $fillable = [
        'payment_number',
        'checkout_unit_id',
        'checkout_order_id',
        'payment_type',
        'amount',
        'payment_method',
        'status',
        'description',
        'reference_number',
        'payment_details',
        'payment_date',
        'processed_at',
        'processed_by',
        'notes'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'payment_details' => 'array',
        'payment_date' => 'date',
        'processed_at' => 'datetime'
    ];

    // Tự động tạo payment number khi tạo payment mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->payment_number)) {
                $payment->payment_number = 'PAY-' . date('Ymd') . '-' . str_pad(static::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);
            }
        });
    }

    // Relationship với CheckoutUnit
    public function checkoutUnit(): BelongsTo
    {
        return $this->belongsTo(CheckoutUnit::class);
    }

    // Relationship với CheckoutOrder
    public function checkoutOrder(): BelongsTo
    {
        return $this->belongsTo(CheckoutOrder::class);
    }

    // Relationship với User (người xử lý)
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    // Scope để lấy các payment theo status
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Scope để lấy các payment đã hoàn thành
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Scope để lấy các payment theo loại
    public function scopeByType($query, $type)
    {
        return $query->where('payment_type', $type);
    }

    // Lấy trạng thái hiển thị
    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Chờ xử lý',
            'completed' => 'Đã hoàn thành',
            'failed' => 'Thất bại',
            'cancelled' => 'Đã hủy'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    // Lấy loại thanh toán hiển thị
    public function getPaymentTypeLabelAttribute()
    {
        $labels = [
            'order' => 'Thanh toán đơn hàng',
            'bulk' => 'Thanh toán hàng loạt',
            'commission' => 'Hoa hồng',
            'adjustment' => 'Điều chỉnh'
        ];

        return $labels[$this->payment_type] ?? $this->payment_type;
    }

    // Lấy phương thức thanh toán hiển thị
    public function getPaymentMethodLabelAttribute()
    {
        $labels = [
            'bank_transfer' => 'Chuyển khoản ngân hàng',
            'cash' => 'Tiền mặt',
            'e_wallet' => 'Ví điện tử',
            'other' => 'Khác'
        ];

        return $labels[$this->payment_method] ?? $this->payment_method;
    }

    // Kiểm tra payment có thể hủy không
    public function getCanCancelAttribute()
    {
        return $this->status === 'pending';
    }

    // Kiểm tra payment có thể xử lý không
    public function getCanProcessAttribute()
    {
        return $this->status === 'pending';
    }
}
