<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Order;
use App\Models\Product;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Thống kê tổng quan
        $stats = [
            'total_products' => Product::count(),
            'total_orders' => Order::count(),
            'total_suppliers' => Supplier::count(),
            'total_categories' => Category::count(),
            'pending_orders' => Order::where('status', 'pending')->count(),
            'low_stock_products' => Product::whereRaw('stock_quantity <= min_stock_level')->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'monthly_revenue' => Order::where('payment_status', 'paid')
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('total_amount'),
        ];

        // Đơn hàng gần đây
        $recent_orders = Order::with(['orderItems.product'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Sản phẩm bán chạy
        $top_products = Product::select('products.id', 'products.name', 'products.sku', 'products.price', 'products.stock_quantity', DB::raw('SUM(order_items.quantity) as total_sold'))
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.status', '!=', 'cancelled')
            ->groupBy('products.id', 'products.name', 'products.sku', 'products.price', 'products.stock_quantity')
            ->orderBy('total_sold', 'desc')
            ->limit(10)
            ->get();

        // Sản phẩm sắp hết hàng
        $low_stock_products = Product::whereRaw('stock_quantity <= min_stock_level')
            ->orderBy('stock_quantity', 'asc')
            ->limit(10)
            ->get();

        // Doanh thu theo tháng (12 tháng gần nhất)
        $monthly_revenue = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $revenue = Order::where('payment_status', 'paid')
                ->whereMonth('created_at', $date->month)
                ->whereYear('created_at', $date->year)
                ->sum('total_amount');

            $monthly_revenue[] = [
                'month' => $date->format('M Y'),
                'revenue' => $revenue
            ];
        }

        return view('dashboard.index', compact(
            'stats',
            'recent_orders',
            'top_products',
            'low_stock_products',
            'monthly_revenue'
        ));
    }
}
