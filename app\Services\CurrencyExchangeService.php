<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CurrencyExchangeService
{
    private $apiKey;
    private $baseUrl = 'https://api.exchangerate-api.com/v4/latest/';
    private $cachePrefix = 'exchange_rate_';
    private $cacheDuration = 3600; // 1 hour

    public function __construct()
    {
        $this->apiKey = env('EXCHANGE_RATE_API_KEY');
    }

    /**
     * Get exchange rate from one currency to another
     */
    public function getExchangeRate($fromCurrency, $toCurrency)
    {
        try {
            $cacheKey = $this->cachePrefix . strtoupper($fromCurrency) . '_' . strtoupper($toCurrency);
            
            // Try to get from cache first
            $cachedRate = Cache::get($cacheKey);
            if ($cachedRate) {
                return [
                    'success' => true,
                    'rate' => $cachedRate,
                    'from' => strtoupper($fromCurrency),
                    'to' => strtoupper($toCurrency),
                    'cached' => true
                ];
            }

            // If not in cache, fetch from API
            $rate = $this->fetchExchangeRate($fromCurrency, $toCurrency);
            
            if ($rate) {
                // Cache the rate
                Cache::put($cacheKey, $rate, $this->cacheDuration);
                
                return [
                    'success' => true,
                    'rate' => $rate,
                    'from' => strtoupper($fromCurrency),
                    'to' => strtoupper($toCurrency),
                    'cached' => false
                ];
            }

            // If API fails, use fallback rates
            return $this->getFallbackRate($fromCurrency, $toCurrency);

        } catch (\Exception $e) {
            Log::error('Exchange rate fetch failed: ' . $e->getMessage());
            return $this->getFallbackRate($fromCurrency, $toCurrency);
        }
    }

    /**
     * Fetch exchange rate from external API
     */
    private function fetchExchangeRate($fromCurrency, $toCurrency)
    {
        try {
            $fromCurrency = strtoupper($fromCurrency);
            $toCurrency = strtoupper($toCurrency);

            // If same currency, return 1
            if ($fromCurrency === $toCurrency) {
                return 1.0;
            }

            // Try multiple APIs for reliability
            $apis = [
                $this->fetchFromExchangeRateApi($fromCurrency, $toCurrency),
                $this->fetchFromFreeApi($fromCurrency, $toCurrency),
                $this->fetchFromVietcombank($fromCurrency, $toCurrency)
            ];

            foreach ($apis as $rate) {
                if ($rate !== null) {
                    return $rate;
                }
            }

            return null;

        } catch (\Exception $e) {
            Log::error('API exchange rate fetch failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fetch from ExchangeRate-API
     */
    private function fetchFromExchangeRateApi($fromCurrency, $toCurrency)
    {
        try {
            $url = $this->baseUrl . $fromCurrency;
            $response = Http::timeout(10)->get($url);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['rates'][$toCurrency])) {
                    return floatval($data['rates'][$toCurrency]);
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::warning('ExchangeRate-API failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fetch from free currency API
     */
    private function fetchFromFreeApi($fromCurrency, $toCurrency)
    {
        try {
            $url = "https://api.fxratesapi.com/latest?base={$fromCurrency}&symbols={$toCurrency}";
            $response = Http::timeout(10)->get($url);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['rates'][$toCurrency])) {
                    return floatval($data['rates'][$toCurrency]);
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::warning('FxRatesAPI failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Fetch from Vietcombank (for VND rates)
     */
    private function fetchFromVietcombank($fromCurrency, $toCurrency)
    {
        try {
            // Only handle USD to VND for now
            if ($fromCurrency === 'USD' && $toCurrency === 'VND') {
                $url = 'https://portal.vietcombank.com.vn/Usercontrols/TVPortal.TyGia/pXML.aspx';
                $response = Http::timeout(10)->get($url);

                if ($response->successful()) {
                    $xml = simplexml_load_string($response->body());
                    if ($xml) {
                        foreach ($xml->Exrate as $rate) {
                            if ((string)$rate['CurrencyCode'] === 'USD') {
                                $sellRate = (string)$rate['Sell'];
                                return floatval(str_replace(',', '', $sellRate));
                            }
                        }
                    }
                }
            }

            return null;
        } catch (\Exception $e) {
            Log::warning('Vietcombank API failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get fallback exchange rates (static rates as backup)
     */
    private function getFallbackRate($fromCurrency, $toCurrency)
    {
        $fromCurrency = strtoupper($fromCurrency);
        $toCurrency = strtoupper($toCurrency);

        // Static fallback rates (should be updated periodically)
        $fallbackRates = [
            'USD_VND' => 24000,
            'EUR_VND' => 26000,
            'GBP_VND' => 30000,
            'JPY_VND' => 160,
            'USD_EUR' => 0.85,
            'USD_GBP' => 0.75,
            'EUR_USD' => 1.18,
            'GBP_USD' => 1.33
        ];

        $key = $fromCurrency . '_' . $toCurrency;
        $reverseKey = $toCurrency . '_' . $fromCurrency;

        if (isset($fallbackRates[$key])) {
            return [
                'success' => true,
                'rate' => $fallbackRates[$key],
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'fallback' => true
            ];
        } elseif (isset($fallbackRates[$reverseKey])) {
            return [
                'success' => true,
                'rate' => 1 / $fallbackRates[$reverseKey],
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'fallback' => true
            ];
        }

        // If no fallback available, return error
        return [
            'success' => false,
            'message' => "Không tìm thấy tỷ giá cho {$fromCurrency} -> {$toCurrency}",
            'rate' => null
        ];
    }

    /**
     * Convert amount from one currency to another
     */
    public function convertAmount($amount, $fromCurrency, $toCurrency)
    {
        $rateResult = $this->getExchangeRate($fromCurrency, $toCurrency);
        
        if ($rateResult['success']) {
            return [
                'success' => true,
                'original_amount' => $amount,
                'converted_amount' => $amount * $rateResult['rate'],
                'rate' => $rateResult['rate'],
                'from' => $rateResult['from'],
                'to' => $rateResult['to'],
                'cached' => $rateResult['cached'] ?? false,
                'fallback' => $rateResult['fallback'] ?? false
            ];
        }

        return $rateResult;
    }

    /**
     * Get current USD to VND rate (most common conversion)
     */
    public function getUsdToVndRate()
    {
        return $this->getExchangeRate('USD', 'VND');
    }

    /**
     * Convert USD to VND
     */
    public function convertUsdToVnd($usdAmount)
    {
        return $this->convertAmount($usdAmount, 'USD', 'VND');
    }

    /**
     * Get multiple exchange rates at once
     */
    public function getMultipleRates($baseCurrency, $targetCurrencies)
    {
        $rates = [];
        
        foreach ($targetCurrencies as $targetCurrency) {
            $result = $this->getExchangeRate($baseCurrency, $targetCurrency);
            $rates[$targetCurrency] = $result;
        }

        return $rates;
    }

    /**
     * Clear exchange rate cache
     */
    public function clearCache($fromCurrency = null, $toCurrency = null)
    {
        if ($fromCurrency && $toCurrency) {
            $cacheKey = $this->cachePrefix . strtoupper($fromCurrency) . '_' . strtoupper($toCurrency);
            Cache::forget($cacheKey);
        } else {
            // Clear all exchange rate cache
            $keys = Cache::getRedis()->keys($this->cachePrefix . '*');
            foreach ($keys as $key) {
                Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
            }
        }
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies()
    {
        return [
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'GBP' => 'British Pound',
            'JPY' => 'Japanese Yen',
            'VND' => 'Vietnamese Dong',
            'CNY' => 'Chinese Yuan',
            'KRW' => 'South Korean Won',
            'THB' => 'Thai Baht',
            'SGD' => 'Singapore Dollar',
            'MYR' => 'Malaysian Ringgit'
        ];
    }

    /**
     * Format currency amount
     */
    public function formatAmount($amount, $currency)
    {
        $currency = strtoupper($currency);
        
        switch ($currency) {
            case 'VND':
                return number_format($amount, 0) . ' ₫';
            case 'USD':
                return '$' . number_format($amount, 2);
            case 'EUR':
                return '€' . number_format($amount, 2);
            case 'GBP':
                return '£' . number_format($amount, 2);
            case 'JPY':
                return '¥' . number_format($amount, 0);
            default:
                return number_format($amount, 2) . ' ' . $currency;
        }
    }
}
