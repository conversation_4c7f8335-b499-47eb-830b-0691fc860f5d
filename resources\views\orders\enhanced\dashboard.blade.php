@extends('layouts.app')

@section('title', 'Dashboard Đơn hàng')
@section('page-title', 'Dashboard Đơn hàng')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('orders.enhanced.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Tạo đơn hàng mới
    </a>
    <a href="{{ route('orders.enhanced.index') }}" class="btn btn-outline-primary">
        <i class="fas fa-list me-1"></i>
        Danh sách đơn hàng
    </a>
</div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Hôm nay
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['today']['orders']) }} đơn
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($stats['today']['revenue'], 0, ',', '.') }}đ doanh thu
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Tuần này
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['week']['orders']) }} đơn
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($stats['week']['revenue'], 0, ',', '.') }}đ doanh thu
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Tháng này
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['month']['orders']) }} đơn
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($stats['month']['revenue'], 0, ',', '.') }}đ doanh thu
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Lợi nhuận tháng
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['month']['profit'], 0, ',', '.') }}đ
                            </div>
                            <div class="text-xs text-muted">
                                @if($stats['month']['revenue'] > 0)
                                    {{ number_format(($stats['month']['profit'] / $stats['month']['revenue']) * 100, 1) }}% margin
                                @endif
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Platform Distribution Chart -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Phân bố theo Platform</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="platformChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        @foreach($stats['platforms'] as $platform => $count)
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> {{ ucfirst($platform) }}: {{ $count }}
                        </span>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Performing Accounts -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Top Accounts (Lợi nhuận)</h6>
                </div>
                <div class="card-body">
                    @foreach($stats['top_accounts'] as $account)
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-3">
                                <i class="fab fa-ebay text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $account->platform_account }}</h6>
                                <small class="text-muted">eBay Account</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <h6 class="mb-0 text-success">{{ number_format($account->profit, 0, ',', '.') }}đ</h6>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Đơn hàng gần đây</h6>
                    <a href="{{ route('orders.enhanced.index') }}" class="btn btn-sm btn-primary">
                        Xem tất cả
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Mã đơn</th>
                                    <th>Ngày</th>
                                    <th>Platform</th>
                                    <th>Khách hàng</th>
                                    <th>Giá trị</th>
                                    <th>Lợi nhuận</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $recentOrders = \App\Models\Order::with(['user', 'team'])
                                        ->when(!auth()->user()->isAdmin(), function($q) {
                                            return $q->where('team_id', auth()->user()->team_id);
                                        })
                                        ->orderBy('created_at', 'desc')
                                        ->limit(10)
                                        ->get();
                                @endphp
                                
                                @forelse($recentOrders as $order)
                                <tr>
                                    <td>
                                        <a href="{{ route('orders.enhanced.show', $order) }}" class="text-decoration-none">
                                            {{ $order->order_number }}
                                        </a>
                                    </td>
                                    <td>{{ $order->created_at->format('d/m/Y') }}</td>
                                    <td>
                                        <i class="{{ $order->platform_icon ?? 'fas fa-shopping-cart' }} me-1"></i>
                                        {{ ucfirst($order->platform) }}
                                    </td>
                                    <td>{{ $order->customer_name }}</td>
                                    <td>{{ number_format($order->total_amount, 0, ',', '.') }}đ</td>
                                    <td>
                                        <span class="text-{{ $order->profit_amount > 0 ? 'success' : 'danger' }}">
                                            {{ number_format($order->profit_amount, 0, ',', '.') }}đ
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $order->status_badge }}">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('orders.enhanced.show', $order) }}" class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('orders.enhanced.edit', $order) }}" class="btn btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center text-muted">
                                        Chưa có đơn hàng nào
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.avatar-sm {
    width: 40px;
    height: 40px;
}
</style>
@endpush

@push('scripts')
<script>
// Platform Distribution Chart
var ctx = document.getElementById("platformChart");
var platformChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: {!! json_encode(array_keys($stats['platforms']->toArray())) !!},
        datasets: [{
            data: {!! json_encode(array_values($stats['platforms']->toArray())) !!},
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf', '#f4b619', '#e02d1b'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        maintainAspectRatio: false,
        tooltips: {
            backgroundColor: "rgb(255,255,255)",
            bodyFontColor: "#858796",
            borderColor: '#dddfeb',
            borderWidth: 1,
            xPadding: 15,
            yPadding: 15,
            displayColors: false,
            caretPadding: 10,
        },
        legend: {
            display: false
        },
        cutoutPercentage: 80,
    },
});
</script>
@endpush
@endsection
