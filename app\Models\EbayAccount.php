<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class EbayAccount extends Model
{
    protected $fillable = [
        'account_name',
        'email',
        'ebay_user_id',
        'team_id',
        'seller_id',
        'status',
        'payout_schedule',
        'last_payout_date',
        'total_payouts',
        'notes',
        'settings'
    ];

    protected $casts = [
        'last_payout_date' => 'date',
        'total_payouts' => 'decimal:2',
        'settings' => 'array'
    ];

    // Relationship với Team
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    // Relationship với User (seller)
    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    // Relationship với EbayPayouts
    public function ebayPayouts(): HasMany
    {
        return $this->hasMany(EbayPayout::class);
    }

    // Scope để lấy các account đang active
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Scope để lấy account theo team
    public function scopeByTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    // Tính tổng payout đã nhận
    public function getTotalReceivedPayoutsAttribute()
    {
        return $this->ebayPayouts()->where('status', 'completed')->sum('amount_vnd');
    }

    // Tính payout trong tháng hiện tại
    public function getCurrentMonthPayoutsAttribute()
    {
        return $this->ebayPayouts()
            ->whereYear('payout_date', now()->year)
            ->whereMonth('payout_date', now()->month)
            ->where('status', 'completed')
            ->sum('amount_vnd');
    }

    // Kiểm tra ngày payout tiếp theo
    public function getNextPayoutDateAttribute()
    {
        if (!$this->last_payout_date) {
            return null;
        }

        $lastPayout = Carbon::parse($this->last_payout_date);

        switch ($this->payout_schedule) {
            case 'weekly':
                return $lastPayout->addWeek();
            case 'bi_weekly':
                return $lastPayout->addWeeks(2);
            case 'monthly':
                return $lastPayout->addMonth();
            default:
                return null;
        }
    }

    // Kiểm tra có payout nào đang pending không
    public function hasPendingPayouts()
    {
        return $this->ebayPayouts()->where('status', 'pending')->exists();
    }

    // Lấy trạng thái hiển thị
    public function getStatusLabelAttribute()
    {
        $labels = [
            'active' => 'Hoạt động',
            'suspended' => 'Bị đình chỉ',
            'inactive' => 'Không hoạt động'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    // Lấy lịch payout hiển thị
    public function getPayoutScheduleLabelAttribute()
    {
        $labels = [
            'weekly' => 'Hàng tuần',
            'bi_weekly' => 'Hai tuần một lần',
            'monthly' => 'Hàng tháng'
        ];

        return $labels[$this->payout_schedule] ?? $this->payout_schedule;
    }

    // Tính số ngày từ lần payout cuối
    public function getDaysSinceLastPayoutAttribute()
    {
        if (!$this->last_payout_date) {
            return null;
        }

        return Carbon::parse($this->last_payout_date)->diffInDays(now());
    }

    // Kiểm tra có quá hạn payout không
    public function getIsOverdueAttribute()
    {
        $nextPayoutDate = $this->next_payout_date;
        if (!$nextPayoutDate) {
            return false;
        }

        return now()->gt($nextPayoutDate);
    }
}
