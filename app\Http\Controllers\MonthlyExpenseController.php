<?php

namespace App\Http\Controllers;

use App\Models\MonthlyExpense;
use App\Models\Team;
use App\Models\CheckoutUnit;
use App\Services\SmartExpenseCategorizer;
use App\Services\ExpenseValidationEngine;
use App\Services\SmartApprovalWorkflow;
use App\Services\ExpenseAnalyticsDashboard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MonthlyExpenseController extends Controller
{
    private $categorizer;
    private $validator;
    private $approvalWorkflow;
    private $analytics;

    public function __construct(
        SmartExpenseCategorizer $categorizer,
        ExpenseValidationEngine $validator,
        SmartApprovalWorkflow $approvalWorkflow,
        ExpenseAnalyticsDashboard $analytics
    ) {
        $this->categorizer = $categorizer;
        $this->validator = $validator;
        $this->approvalWorkflow = $approvalWorkflow;
        $this->analytics = $analytics;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $teamId = $request->get('team_id', $user->team_id);

        // Check permission
        if (!$user->hasRole('admin') && $user->team_id != $teamId) {
            abort(403, 'Unauthorized access to team data');
        }

        $query = MonthlyExpense::with(['team', 'createdBy', 'approvedBy', 'checkoutUnit'])
            ->where('team_id', $teamId);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        if ($request->filled('month')) {
            $query->where('expense_month', $request->month);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('vendor', 'like', "%{$search}%")
                  ->orWhere('expense_number', 'like', "%{$search}%");
            });
        }

        $expenses = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get analytics for the current team
        $analytics = $this->analytics->generateRealTimeInsights($teamId, 'current_month');

        return view('expenses.index', compact('expenses', 'analytics', 'teamId'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();
        $teams = $user->hasRole('admin') ? Team::all() : collect([$user->team]);
        $checkoutUnits = CheckoutUnit::where('team_id', $user->team_id)->get();

        return view('expenses.create', compact('teams', 'checkoutUnits'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'team_id' => 'required|exists:teams,id',
            'expense_type' => 'required|string',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'vendor' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'expense_date' => 'required|date',
            'frequency' => 'required|string',
            'checkout_unit_id' => 'nullable|exists:checkout_units,id',
            'notes' => 'nullable|string',
            'attachments' => 'nullable|array'
        ]);

        try {
            DB::beginTransaction();

            // Smart categorization
            $categorization = $this->categorizer->categorizeExpense(
                $request->description,
                $request->amount,
                $request->vendor,
                $request->team_id
            );

            // Create expense with smart categorization
            $expenseData = $request->all();
            $expenseData['created_by'] = Auth::id();
            $expenseData['category'] = $categorization['suggested_category'];
            $expenseData['suggested_category'] = $categorization['suggested_category'];
            $expenseData['categorization_confidence'] = $categorization['confidence'];
            $expenseData['category_manually_corrected'] = false;

            $expense = MonthlyExpense::create($expenseData);

            // Validation and fraud detection
            $validationResults = $this->validator->validateExpense($expense);
            $expense->update([
                'validation_results' => $validationResults,
                'fraud_score' => $validationResults['fraud_detection']['fraud_score'],
                'risk_level' => $validationResults['fraud_detection']['risk_level'],
                'fraud_indicators' => $validationResults['fraud_detection']['indicators']
            ]);

            // Determine approval path
            $approvalPath = $this->approvalWorkflow->determineApprovalPath($expense);
            $expense->update([
                'approval_status' => $approvalPath['auto_approved'] ? 'auto_approved' : 'pending',
                'approval_path' => $approvalPath['approval_path'] ?? [],
                'approved_at' => $approvalPath['auto_approved'] ? now() : null,
                'approved_by' => $approvalPath['auto_approved'] ? null : null
            ]);

            DB::commit();

            $message = $approvalPath['auto_approved'] ?
                'Expense created and auto-approved successfully!' :
                'Expense created and sent for approval!';

            return redirect()->route('expenses.show', $expense)
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating expense: ' . $e->getMessage());

            return back()->withInput()
                ->with('error', 'Error creating expense. Please try again.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $expense = MonthlyExpense::with(['team', 'createdBy', 'approvedBy', 'checkoutUnit'])
            ->findOrFail($id);

        // Check permission
        $user = Auth::user();
        if (!$user->hasRole('admin') && $user->team_id != $expense->team_id) {
            abort(403, 'Unauthorized access to expense');
        }

        // Get approval status
        $approvalStatus = $this->approvalWorkflow->getApprovalStatus($expense);

        return view('expenses.show', compact('expense', 'approvalStatus'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $expense = MonthlyExpense::findOrFail($id);

        // Check permission
        $user = Auth::user();
        if (!$user->hasRole('admin') && $user->team_id != $expense->team_id) {
            abort(403, 'Unauthorized access to expense');
        }

        // Check if expense can be edited
        if (!$expense->can_edit) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be edited in its current status.');
        }

        $teams = $user->hasRole('admin') ? Team::all() : collect([$user->team]);
        $checkoutUnits = CheckoutUnit::where('team_id', $expense->team_id)->get();

        return view('expenses.edit', compact('expense', 'teams', 'checkoutUnits'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $expense = MonthlyExpense::findOrFail($id);

        // Check permission
        $user = Auth::user();
        if (!$user->hasRole('admin') && $user->team_id != $expense->team_id) {
            abort(403, 'Unauthorized access to expense');
        }

        if (!$expense->can_edit) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be edited in its current status.');
        }

        $request->validate([
            'expense_type' => 'required|string',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'vendor' => 'nullable|string|max:255',
            'amount' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'expense_date' => 'required|date',
            'frequency' => 'required|string',
            'category' => 'required|string',
            'checkout_unit_id' => 'nullable|exists:checkout_units,id',
            'notes' => 'nullable|string',
            'attachments' => 'nullable|array'
        ]);

        try {
            DB::beginTransaction();

            $originalCategory = $expense->category;
            $newCategory = $request->category;

            // Check if category was manually changed
            if ($originalCategory !== $newCategory && $expense->suggested_category === $originalCategory) {
                // Learn from user correction
                $this->categorizer->learnFromUserCorrection(
                    [
                        'suggested_category' => $expense->suggested_category,
                        'confidence' => $expense->categorization_confidence
                    ],
                    $newCategory,
                    [
                        'description' => $request->description,
                        'amount' => $request->amount,
                        'vendor' => $request->vendor,
                        'team_id' => $expense->team_id
                    ]
                );

                $expense->category_manually_corrected = true;
            }

            // Update expense
            $expense->update($request->all());

            // Re-validate if significant changes
            if ($expense->wasChanged(['amount', 'description', 'category'])) {
                $validationResults = $this->validator->validateExpense($expense);
                $expense->update([
                    'validation_results' => $validationResults,
                    'fraud_score' => $validationResults['fraud_detection']['fraud_score'],
                    'risk_level' => $validationResults['fraud_detection']['risk_level'],
                    'fraud_indicators' => $validationResults['fraud_detection']['indicators']
                ]);

                // Reset approval if risk level changed significantly
                if ($expense->wasChanged(['risk_level']) && $expense->approval_status === 'pending') {
                    $approvalPath = $this->approvalWorkflow->determineApprovalPath($expense);
                    $expense->update([
                        'approval_path' => $approvalPath['approval_path'] ?? [],
                        'approval_history' => [] // Reset approval history
                    ]);
                }
            }

            DB::commit();

            return redirect()->route('expenses.show', $expense)
                ->with('success', 'Expense updated successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating expense: ' . $e->getMessage());

            return back()->withInput()
                ->with('error', 'Error updating expense. Please try again.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $expense = MonthlyExpense::findOrFail($id);

        // Check permission
        $user = Auth::user();
        if (!$user->hasRole('admin') && $user->team_id != $expense->team_id) {
            abort(403, 'Unauthorized access to expense');
        }

        if (!$expense->can_cancel) {
            return redirect()->route('expenses.show', $expense)
                ->with('error', 'This expense cannot be deleted in its current status.');
        }

        try {
            $expense->delete();

            return redirect()->route('expenses.index')
                ->with('success', 'Expense deleted successfully!');

        } catch (\Exception $e) {
            Log::error('Error deleting expense: ' . $e->getMessage());

            return back()->with('error', 'Error deleting expense. Please try again.');
        }
    }

    /**
     * Get smart categorization suggestion for expense
     */
    public function suggestCategory(Request $request)
    {
        $request->validate([
            'description' => 'required|string',
            'amount' => 'required|numeric',
            'vendor' => 'nullable|string',
            'team_id' => 'required|exists:teams,id'
        ]);

        try {
            $categorization = $this->categorizer->categorizeExpense(
                $request->description,
                $request->amount,
                $request->vendor,
                $request->team_id
            );

            return response()->json([
                'success' => true,
                'categorization' => $categorization
            ]);

        } catch (\Exception $e) {
            Log::error('Error in category suggestion: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Unable to suggest category'
            ], 500);
        }
    }

    /**
     * Process approval decision
     */
    public function processApproval(Request $request, string $id)
    {
        $request->validate([
            'decision' => 'required|in:approved,rejected,request_changes',
            'comments' => 'nullable|string'
        ]);

        try {
            $expense = MonthlyExpense::findOrFail($id);
            $user = Auth::user();

            $result = $this->approvalWorkflow->processApproval(
                $expense->id,
                $user->id,
                $request->decision,
                $request->comments
            );

            $message = match($request->decision) {
                'approved' => 'Expense approved successfully!',
                'rejected' => 'Expense rejected.',
                'request_changes' => 'Changes requested for expense.'
            };

            return redirect()->route('expenses.show', $expense)
                ->with('success', $message);

        } catch (\Exception $e) {
            Log::error('Error processing approval: ' . $e->getMessage());

            return back()->with('error', 'Error processing approval: ' . $e->getMessage());
        }
    }

    /**
     * Get expense analytics dashboard
     */
    public function analytics(Request $request)
    {
        $user = Auth::user();
        $teamId = $request->get('team_id', $user->team_id);
        $period = $request->get('period', 'current_month');

        // Check permission
        if (!$user->hasRole('admin') && $user->team_id != $teamId) {
            abort(403, 'Unauthorized access to team analytics');
        }

        $analytics = $this->analytics->generateRealTimeInsights($teamId, $period);

        if ($request->ajax()) {
            return response()->json($analytics);
        }

        return view('expenses.analytics', compact('analytics', 'teamId', 'period'));
    }

    /**
     * Bulk approve low-risk expenses
     */
    public function bulkApprove(Request $request)
    {
        $user = Auth::user();

        if (!$user->hasRole(['admin', 'finance_manager', 'team_leader'])) {
            abort(403, 'Unauthorized to bulk approve expenses');
        }

        try {
            $processed = $this->approvalWorkflow->autoProcessLowRiskExpenses();

            return response()->json([
                'success' => true,
                'processed' => $processed,
                'message' => "Successfully processed {$processed} low-risk expenses"
            ]);

        } catch (\Exception $e) {
            Log::error('Error in bulk approval: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'error' => 'Error processing bulk approval'
            ], 500);
        }
    }

    /**
     * Export expenses to CSV/Excel
     */
    public function export(Request $request)
    {
        $user = Auth::user();
        $teamId = $request->get('team_id', $user->team_id);

        // Check permission
        if (!$user->hasRole('admin') && $user->team_id != $teamId) {
            abort(403, 'Unauthorized access to team data');
        }

        $query = MonthlyExpense::with(['team', 'createdBy', 'approvedBy'])
            ->where('team_id', $teamId);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }
        if ($request->filled('month')) {
            $query->where('expense_month', $request->month);
        }

        $expenses = $query->orderBy('created_at', 'desc')->get();

        $filename = "expenses_team_{$teamId}_" . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($expenses) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Expense Number', 'Title', 'Description', 'Vendor', 'Category',
                'Amount', 'Currency', 'Expense Date', 'Status', 'Approval Status',
                'Risk Level', 'Fraud Score', 'Created By', 'Approved By',
                'Created At', 'Approved At'
            ]);

            // CSV data
            foreach ($expenses as $expense) {
                fputcsv($file, [
                    $expense->expense_number,
                    $expense->title,
                    $expense->description,
                    $expense->vendor,
                    $expense->category,
                    $expense->amount,
                    $expense->currency,
                    $expense->expense_date,
                    $expense->status,
                    $expense->approval_status,
                    $expense->risk_level,
                    $expense->fraud_score,
                    $expense->createdBy->name ?? '',
                    $expense->approvedBy->name ?? '',
                    $expense->created_at,
                    $expense->approved_at
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
