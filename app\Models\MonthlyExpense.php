<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MonthlyExpense extends Model
{
    protected $fillable = [
        'expense_number',
        'team_id',
        'created_by',
        'expense_type',
        'title',
        'description',
        'vendor',
        'amount',
        'currency',
        'expense_date',
        'expense_month',
        'frequency',
        'status',
        'category',
        'suggested_category',
        'categorization_confidence',
        'category_manually_corrected',
        'validation_results',
        'fraud_score',
        'risk_level',
        'fraud_indicators',
        'approval_status',
        'approval_path',
        'approval_history',
        'checkout_unit_id',
        'approved_by',
        'approved_at',
        'approval_comments',
        'notes',
        'attachments',
        'metadata',
        'processed_at'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'expense_date' => 'date',
        'approved_at' => 'datetime',
        'processed_at' => 'datetime',
        'categorization_confidence' => 'decimal:2',
        'fraud_score' => 'decimal:2',
        'category_manually_corrected' => 'boolean',
        'validation_results' => 'array',
        'fraud_indicators' => 'array',
        'approval_path' => 'array',
        'approval_history' => 'array',
        'attachments' => 'array',
        'metadata' => 'array'
    ];

    // Tự động tạo expense number khi tạo expense mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($expense) {
            if (empty($expense->expense_number)) {
                $expense->expense_number = 'EXP-' . date('Ymd') . '-' . str_pad(static::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);
            }

            // Tự động set expense_month từ expense_date nếu chưa có
            if (empty($expense->expense_month) && $expense->expense_date) {
                $expense->expense_month = date('Y-m', strtotime($expense->expense_date));
            }
        });
    }

    // Relationship với Team
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    // Relationship với User (người tạo)
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Relationship với User (người duyệt)
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Accessors
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'pending' => 'Chờ duyệt',
            'approved' => 'Đã duyệt',
            'paid' => 'Đã thanh toán',
            'cancelled' => 'Đã hủy',
            default => 'Không xác định'
        };
    }

    public function getExpenseTypeLabelAttribute()
    {
        return match($this->expense_type) {
            'checkout_fee' => 'Tiền checkout',
            'advertising' => 'Quảng cáo',
            'shipping' => 'Vận chuyển',
            'refunds' => 'Hoàn tiền',
            'tools_software' => 'Công cụ/Phần mềm',
            'office_supplies' => 'Văn phòng phẩm',
            'utilities' => 'Tiện ích',
            'travel' => 'Du lịch',
            'meals' => 'Ăn uống',
            'other' => 'Khác',
            default => 'Không xác định'
        };
    }

    public function getFrequencyLabelAttribute()
    {
        return match($this->frequency) {
            'one_time' => 'Một lần',
            'monthly' => 'Hàng tháng',
            'quarterly' => 'Hàng quý',
            'yearly' => 'Hàng năm',
            default => 'Không xác định'
        };
    }

    public function getFormattedMonthAttribute()
    {
        if (!$this->expense_month) return null;

        try {
            return \Carbon\Carbon::createFromFormat('Y-m', $this->expense_month)->format('m/Y');
        } catch (\Exception $e) {
            return $this->expense_month;
        }
    }

    // Business Logic Methods
    public function getCanEditAttribute()
    {
        // Can edit if status is pending or if user has admin role
        if (auth()->user()->hasRole('admin')) {
            return true;
        }

        return in_array($this->status, ['pending']) &&
               in_array($this->approval_status, ['pending', 'changes_requested']);
    }

    public function getCanApproveAttribute()
    {
        return $this->approval_status === 'pending' &&
               $this->status === 'pending';
    }

    public function getCanCancelAttribute()
    {
        return in_array($this->status, ['pending']) ||
               auth()->user()->hasRole('admin');
    }

    // Mutators
    public function setExpenseMonthAttribute($value)
    {
        if ($this->expense_date) {
            $this->attributes['expense_month'] = $this->expense_date->format('Y-m');
        } elseif ($value) {
            $this->attributes['expense_month'] = $value;
        }
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeByTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByRiskLevel($query, $riskLevel)
    {
        return $query->where('risk_level', $riskLevel);
    }

    public function scopeHighRisk($query)
    {
        return $query->where('risk_level', 'high');
    }

    public function scopeLowRisk($query)
    {
        return $query->where('risk_level', 'low');
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    public function scopeLastMonth($query)
    {
        return $query->whereMonth('created_at', now()->subMonth()->month)
                    ->whereYear('created_at', now()->subMonth()->year);
    }

    // Relationship với CheckoutUnit (nếu có)
    public function checkoutUnit(): BelongsTo
    {
        return $this->belongsTo(CheckoutUnit::class);
    }

    // Scope để lấy expense theo status
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }



    // Scope để lấy expense đã thanh toán
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    // Scope để lấy expense theo team
    public function scopeByTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    // Scope để lấy expense theo tháng
    public function scopeByMonth($query, $month)
    {
        return $query->where('expense_month', $month);
    }

    // Scope để lấy expense theo loại
    public function scopeByType($query, $type)
    {
        return $query->where('expense_type', $type);
    }

    // Lấy loại chi phí hiển thị
    public function getExpenseTypeLabelAttribute()
    {
        $labels = [
            'checkout_fee' => 'Tiền checkout',
            'proxy_renewal' => 'Gia hạn proxy',
            'hide_my_acc' => 'Hide My Acc subscription',
            'salary' => 'Lương nhân viên',
            'bonus' => 'Thưởng nhân viên',
            'other' => 'Chi phí khác'
        ];

        return $labels[$this->expense_type] ?? $this->expense_type;
    }

    // Lấy trạng thái hiển thị
    public function getStatusLabelAttribute()
    {
        $labels = [
            'pending' => 'Chờ duyệt',
            'approved' => 'Đã duyệt',
            'paid' => 'Đã thanh toán',
            'cancelled' => 'Đã hủy'
        ];

        return $labels[$this->status] ?? $this->status;
    }

    // Lấy tần suất hiển thị
    public function getFrequencyLabelAttribute()
    {
        $labels = [
            'one_time' => 'Một lần',
            'monthly' => 'Hàng tháng',
            'quarterly' => 'Hàng quý',
            'yearly' => 'Hàng năm'
        ];

        return $labels[$this->frequency] ?? $this->frequency;
    }

    // Kiểm tra expense có thể chỉnh sửa không
    public function getCanEditAttribute()
    {
        return in_array($this->status, ['pending']);
    }

    // Kiểm tra expense có thể duyệt không
    public function getCanApproveAttribute()
    {
        return $this->status === 'pending';
    }

    // Kiểm tra expense có thể hủy không
    public function getCanCancelAttribute()
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    // Format số tiền hiển thị
    public function getFormattedAmountAttribute()
    {
        return number_format($this->amount, 0) . ' ' . $this->currency;
    }

    // Lấy tháng hiển thị
    public function getFormattedMonthAttribute()
    {
        return date('m/Y', strtotime($this->expense_month . '-01'));
    }
}
