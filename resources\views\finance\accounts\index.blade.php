@extends('layouts.app')

@section('title', 'Q<PERSON>ản lý T<PERSON> khoản eBay - Dropship Manager')
@section('page-title', 'Quản lý Tài khoản eBay')

@section('content')
<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('finance.accounts.index') }}" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Tìm kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Tên tài khoản, email, store..." value="{{ request('search') }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">T<PERSON><PERSON> cả</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}><PERSON>ạ<PERSON> động</option>
                    <option value="suspended" {{ request('status') == 'suspended' ? 'selected' : '' }}>Tạm khóa</option>
                    <option value="limited" {{ request('status') == 'limited' ? 'selected' : '' }}>Hạn chế</option>
                    <option value="closed" {{ request('status') == 'closed' ? 'selected' : '' }}>Đóng</option>
                </select>
            </div>
            @if(auth()->user()->isAdmin())
            <div class="col-md-2">
                <label class="form-label">Team</label>
                <select name="team_id" class="form-select">
                    <option value="">Tất cả teams</option>
                    @foreach($teams ?? [] as $team)
                        <option value="{{ $team->id }}" {{ request('team_id') == $team->id ? 'selected' : '' }}>
                            {{ $team->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Seller</label>
                <select name="seller_id" class="form-select">
                    <option value="">Tất cả sellers</option>
                    @foreach($sellers ?? [] as $seller)
                        <option value="{{ $seller->id }}" {{ request('seller_id') == $seller->id ? 'selected' : '' }}>
                            {{ $seller->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            @endif
            <div class="col-md-3">
                <label class="form-label">Sắp xếp</label>
                <select name="sort_by" class="form-select">
                    <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Ngày tạo</option>
                    <option value="account_name" {{ request('sort_by') == 'account_name' ? 'selected' : '' }}>Tên tài khoản</option>
                    <option value="total_payouts" {{ request('sort_by') == 'total_payouts' ? 'selected' : '' }}>Tổng payout</option>
                    <option value="last_payout_date" {{ request('sort_by') == 'last_payout_date' ? 'selected' : '' }}>Payout gần nhất</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Accounts Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách Tài khoản eBay</h6>
        <div class="btn-group">
            <a href="{{ route('finance.accounts.create') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Thêm mới
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Tài khoản</th>
                        <th>Team</th>
                        <th>Seller</th>
                        <th>Store</th>
                        <th>Tài chính</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($accounts ?? [] as $account)
                    <tr>
                        <td>
                            <div>
                                <strong>{{ $account->account_name }}</strong>
                                <br><small class="text-muted">{{ $account->email }}</small>
                                @if($account->registration_date)
                                    <br><small class="text-info">Đăng ký: {{ $account->registration_date->format('d/m/Y') }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $account->team->name ?? 'N/A' }}</span>
                        </td>
                        <td>
                            @if($account->seller)
                                <div>
                                    <strong>{{ $account->seller->name }}</strong>
                                    <br><small class="text-muted">{{ $account->seller->email }}</small>
                                </div>
                            @else
                                <span class="text-muted">Chưa gán</span>
                            @endif
                        </td>
                        <td>
                            @if($account->store_name)
                                <div>
                                    <strong>{{ $account->store_name }}</strong>
                                    @if($account->store_url)
                                        <br><a href="{{ $account->store_url }}" target="_blank" class="small">
                                            <i class="fas fa-external-link-alt"></i> Xem store
                                        </a>
                                    @endif
                                </div>
                            @else
                                <span class="text-muted">Chưa có store</span>
                            @endif
                        </td>
                        <td>
                            <div>
                                <strong>{{ number_format($account->total_payouts) }}đ</strong>
                                <br><small class="text-muted">{{ $account->payouts_count ?? 0 }} payouts</small>
                                @if($account->last_payout_date)
                                    <br><small class="text-success">Gần nhất: {{ $account->last_payout_date->format('d/m/Y') }}</small>
                                @endif
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-{{ 
                                $account->status == 'active' ? 'success' : 
                                ($account->status == 'suspended' ? 'danger' : 
                                ($account->status == 'limited' ? 'warning' : 'secondary')) 
                            }}">
                                {{ ucfirst($account->status) }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('finance.accounts.show', $account) }}" class="btn btn-info btn-sm" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('finance.accounts.edit', $account) }}" class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('finance.accounts.destroy', $account) }}" method="POST" class="d-inline" 
                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa tài khoản này?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center">Không có tài khoản eBay nào</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if(isset($accounts) && $accounts->hasPages())
        <div class="d-flex justify-content-center">
            {{ $accounts->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Summary Cards -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Tổng tài khoản
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $accounts->total() ?? 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fab fa-ebay fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Hoạt động
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $accounts->where('status', 'active')->count() ?? 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Tổng payouts
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ number_format($accounts->sum('total_payouts') ?? 0) }}đ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Có vấn đề
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ $accounts->whereIn('status', ['suspended', 'limited'])->count() ?? 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
