<?php

namespace App\Services;

use App\Models\MonthlyExpense;
use App\Models\User;
use App\Models\Team;
use App\Services\ExpenseValidationEngine;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;

class SmartApprovalWorkflow
{
    private $validationEngine;

    public function __construct(ExpenseValidationEngine $validationEngine)
    {
        $this->validationEngine = $validationEngine;
    }

    public function determineApprovalPath($expense)
    {
        try {
            $approvalPath = [];
            $validationResults = $this->validationEngine->validateExpense($expense);
            
            // Get base approval rules
            $amount = $expense->amount ?? $expense['amount'];
            $category = $expense->category ?? $expense['category'];
            $teamId = $expense->team_id ?? $expense['team_id'];

            // Risk-based modifications
            if ($validationResults['fraud_detection']['risk_level'] === 'high') {
                $approvalPath[] = [
                    'level' => 'fraud_review',
                    'approver_role' => 'finance_manager',
                    'required_actions' => ['verify_receipt', 'contact_vendor', 'user_interview'],
                    'timeout_hours' => 24,
                    'can_auto_approve' => false,
                    'priority' => 'urgent'
                ];
            }

            // Amount-based approval levels
            if ($amount >= 50000000) { // 50M VND
                $approvalPath[] = [
                    'level' => 'director_approval',
                    'approver_role' => 'director',
                    'required_actions' => ['business_justification', 'budget_impact_analysis'],
                    'timeout_hours' => 72,
                    'can_auto_approve' => false,
                    'priority' => 'high'
                ];
            } elseif ($amount >= 20000000) { // 20M VND
                $approvalPath[] = [
                    'level' => 'senior_manager_approval',
                    'approver_role' => 'senior_manager',
                    'required_actions' => ['business_justification'],
                    'timeout_hours' => 48,
                    'can_auto_approve' => false,
                    'priority' => 'high'
                ];
            } elseif ($amount >= 5000000) { // 5M VND
                $approvalPath[] = [
                    'level' => 'manager_approval',
                    'approver_role' => 'finance_manager',
                    'required_actions' => ['receipt_verification'],
                    'timeout_hours' => 24,
                    'can_auto_approve' => false,
                    'priority' => 'medium'
                ];
            } elseif ($amount >= 1000000) { // 1M VND
                $approvalPath[] = [
                    'level' => 'team_leader_approval',
                    'approver_role' => 'team_leader',
                    'required_actions' => [],
                    'timeout_hours' => 12,
                    'can_auto_approve' => true,
                    'priority' => 'normal'
                ];
            }

            // Category-specific rules
            $categoryRules = $this->getCategorySpecificRules($category, $amount);
            if ($categoryRules) {
                $approvalPath = array_merge($approvalPath, $categoryRules);
            }

            // Validation-based modifications
            if ($validationResults['overall_status'] === 'failed') {
                // Add additional approval step for failed validation
                array_unshift($approvalPath, [
                    'level' => 'validation_review',
                    'approver_role' => 'finance_manager',
                    'required_actions' => ['resolve_validation_issues'],
                    'timeout_hours' => 24,
                    'can_auto_approve' => false,
                    'priority' => 'urgent'
                ]);
            }

            // Auto-approval for low-risk, small amounts
            if (empty($approvalPath) && 
                $validationResults['fraud_detection']['risk_level'] === 'low' &&
                $amount < 500000 &&
                $validationResults['overall_status'] === 'passed') {
                
                return [
                    'auto_approved' => true,
                    'approval_reason' => 'low_risk_small_amount',
                    'approved_at' => now(),
                    'approved_by' => 'system',
                    'approval_path' => [],
                    'estimated_completion' => now()
                ];
            }

            // Remove duplicates and sort by priority
            $approvalPath = $this->optimizeApprovalPath($approvalPath);

            return [
                'auto_approved' => false,
                'approval_path' => $approvalPath,
                'estimated_completion' => $this->calculateEstimatedCompletion($approvalPath),
                'total_steps' => count($approvalPath),
                'validation_results' => $validationResults
            ];

        } catch (\Exception $e) {
            Log::error('Error determining approval path: ' . $e->getMessage());
            
            // Fallback to manual approval
            return [
                'auto_approved' => false,
                'approval_path' => [[
                    'level' => 'manual_review',
                    'approver_role' => 'team_leader',
                    'required_actions' => ['manual_verification'],
                    'timeout_hours' => 24,
                    'can_auto_approve' => false,
                    'priority' => 'normal'
                ]],
                'estimated_completion' => now()->addHours(24),
                'error' => 'Approval path determination failed'
            ];
        }
    }

    public function processApproval($expenseId, $approverId, $decision, $comments = null)
    {
        try {
            $expense = MonthlyExpense::findOrFail($expenseId);
            $currentStep = $this->getCurrentApprovalStep($expense);
            
            if (!$currentStep) {
                throw new \Exception('No current approval step found');
            }

            // Verify approver has permission
            if (!$this->canUserApprove($approverId, $currentStep)) {
                throw new \Exception('User does not have permission to approve this step');
            }

            $approvalHistory = $expense->approval_history ?? [];
            
            if ($decision === 'approved') {
                // Mark current step as approved
                $approvalHistory[] = [
                    'step' => $currentStep,
                    'decision' => 'approved',
                    'approver_id' => $approverId,
                    'approved_at' => now(),
                    'comments' => $comments,
                    'processing_time' => $this->calculateProcessingTime($expense, $currentStep)
                ];

                $expense->update([
                    'approval_history' => $approvalHistory
                ]);

                // Check if all approvals are complete
                if ($this->isFullyApproved($expense)) {
                    $this->finalizeApproval($expense, $approverId);
                } else {
                    $this->moveToNextApprovalStep($expense);
                }

            } elseif ($decision === 'rejected') {
                $this->rejectExpense($expense, $approverId, $comments);
            } elseif ($decision === 'request_changes') {
                $this->requestChanges($expense, $approverId, $comments);
            }

            return $this->getApprovalStatus($expense->fresh());

        } catch (\Exception $e) {
            Log::error('Error processing approval: ' . $e->getMessage());
            throw $e;
        }
    }

    public function autoProcessLowRiskExpenses()
    {
        try {
            $pendingExpenses = MonthlyExpense::where('approval_status', 'pending')
                ->where('risk_level', 'low')
                ->where('amount', '<', 500000)
                ->where('created_at', '>=', now()->subHours(1)) // Only recent expenses
                ->get();

            $processed = 0;

            foreach ($pendingExpenses as $expense) {
                $validationResults = $this->validationEngine->validateExpense($expense);
                
                if ($validationResults['overall_status'] === 'passed' && 
                    $validationResults['fraud_detection']['risk_level'] === 'low') {
                    
                    $this->autoApproveExpense($expense);
                    $processed++;
                }
            }

            Log::info("Auto-processed {$processed} low-risk expenses");
            return $processed;

        } catch (\Exception $e) {
            Log::error('Error in auto-processing: ' . $e->getMessage());
            return 0;
        }
    }

    private function getCategorySpecificRules($category, $amount)
    {
        $rules = [];

        switch ($category) {
            case 'advertising':
                if ($amount > 10000000) { // 10M VND
                    $rules[] = [
                        'level' => 'marketing_review',
                        'approver_role' => 'marketing_manager',
                        'required_actions' => ['roi_justification', 'campaign_details'],
                        'timeout_hours' => 24,
                        'can_auto_approve' => false,
                        'priority' => 'medium'
                    ];
                }
                break;

            case 'tools_software':
                $rules[] = [
                    'level' => 'it_review',
                    'approver_role' => 'it_manager',
                    'required_actions' => ['check_existing_licenses', 'security_review'],
                    'timeout_hours' => 48,
                    'can_auto_approve' => false,
                    'priority' => 'low'
                ];
                break;

            case 'refunds':
                $rules[] = [
                    'level' => 'customer_service_review',
                    'approver_role' => 'cs_manager',
                    'required_actions' => ['verify_customer_complaint', 'check_refund_policy'],
                    'timeout_hours' => 12,
                    'can_auto_approve' => false,
                    'priority' => 'high'
                ];
                break;

            case 'travel':
                if ($amount > 5000000) { // 5M VND
                    $rules[] = [
                        'level' => 'travel_approval',
                        'approver_role' => 'operations_manager',
                        'required_actions' => ['verify_business_purpose', 'check_travel_policy'],
                        'timeout_hours' => 48,
                        'can_auto_approve' => false,
                        'priority' => 'medium'
                    ];
                }
                break;
        }

        return $rules;
    }

    private function optimizeApprovalPath($approvalPath)
    {
        // Remove duplicates based on approver_role
        $uniquePath = [];
        $seenRoles = [];

        foreach ($approvalPath as $step) {
            if (!in_array($step['approver_role'], $seenRoles)) {
                $uniquePath[] = $step;
                $seenRoles[] = $step['approver_role'];
            }
        }

        // Sort by priority (urgent > high > medium > normal > low)
        $priorityOrder = ['urgent' => 1, 'high' => 2, 'medium' => 3, 'normal' => 4, 'low' => 5];
        
        usort($uniquePath, function($a, $b) use ($priorityOrder) {
            return $priorityOrder[$a['priority']] <=> $priorityOrder[$b['priority']];
        });

        return $uniquePath;
    }

    private function calculateEstimatedCompletion($approvalPath)
    {
        $totalHours = 0;
        foreach ($approvalPath as $step) {
            $totalHours += $step['timeout_hours'];
        }
        
        // Account for business hours (8 AM - 6 PM, Mon-Fri)
        $businessHoursPerDay = 10;
        $businessDays = ceil($totalHours / $businessHoursPerDay);
        
        return now()->addWeekdays($businessDays);
    }

    private function getCurrentApprovalStep($expense)
    {
        $approvalPath = $expense->approval_path ?? [];
        $approvalHistory = $expense->approval_history ?? [];
        
        $completedSteps = count($approvalHistory);
        
        if ($completedSteps < count($approvalPath)) {
            return $approvalPath[$completedSteps];
        }
        
        return null;
    }

    private function canUserApprove($userId, $step)
    {
        $user = User::find($userId);
        if (!$user) return false;

        $requiredRole = $step['approver_role'];
        
        // Check if user has the required role
        return $user->hasRole($requiredRole) || $user->hasRole('admin');
    }

    private function isFullyApproved($expense)
    {
        $approvalPath = $expense->approval_path ?? [];
        $approvalHistory = $expense->approval_history ?? [];
        
        return count($approvalHistory) >= count($approvalPath);
    }

    private function finalizeApproval($expense, $approverId)
    {
        $expense->update([
            'approval_status' => 'approved',
            'status' => 'approved',
            'approved_by' => $approverId,
            'approved_at' => now(),
            'processed_at' => now()
        ]);

        $this->triggerPostApprovalActions($expense);
        $this->sendApprovalNotifications($expense, 'approved');
        
        Log::info("Expense {$expense->id} fully approved");
    }

    private function rejectExpense($expense, $approverId, $comments)
    {
        $approvalHistory = $expense->approval_history ?? [];
        $currentStep = $this->getCurrentApprovalStep($expense);
        
        $approvalHistory[] = [
            'step' => $currentStep,
            'decision' => 'rejected',
            'approver_id' => $approverId,
            'rejected_at' => now(),
            'comments' => $comments,
            'processing_time' => $this->calculateProcessingTime($expense, $currentStep)
        ];

        $expense->update([
            'approval_status' => 'rejected',
            'status' => 'rejected',
            'approval_history' => $approvalHistory,
            'approval_comments' => $comments,
            'processed_at' => now()
        ]);

        $this->sendApprovalNotifications($expense, 'rejected');
        
        Log::info("Expense {$expense->id} rejected by user {$approverId}");
    }

    private function requestChanges($expense, $approverId, $comments)
    {
        $expense->update([
            'approval_status' => 'changes_requested',
            'status' => 'pending',
            'approval_comments' => $comments
        ]);

        $this->sendApprovalNotifications($expense, 'changes_requested');
        
        Log::info("Changes requested for expense {$expense->id} by user {$approverId}");
    }

    private function moveToNextApprovalStep($expense)
    {
        $this->sendApprovalNotifications($expense, 'pending_next_approval');
        
        Log::info("Expense {$expense->id} moved to next approval step");
    }

    private function autoApproveExpense($expense)
    {
        $expense->update([
            'approval_status' => 'auto_approved',
            'status' => 'approved',
            'approved_by' => null, // System approval
            'approved_at' => now(),
            'processed_at' => now(),
            'approval_comments' => 'Auto-approved: Low risk, small amount'
        ]);

        $this->triggerPostApprovalActions($expense);
        
        Log::info("Expense {$expense->id} auto-approved");
    }

    private function triggerPostApprovalActions($expense)
    {
        // Update financial records
        $this->updateFinancialRecords($expense);
        
        // Generate accounting entries
        $this->generateAccountingEntries($expense);
        
        // Update budget tracking
        $this->updateBudgetTracking($expense);
        
        // Update analytics
        $this->updateExpenseAnalytics($expense);
    }

    private function calculateProcessingTime($expense, $step)
    {
        $stepStartTime = $expense->created_at;
        $approvalHistory = $expense->approval_history ?? [];
        
        if (!empty($approvalHistory)) {
            $lastApproval = end($approvalHistory);
            $stepStartTime = Carbon::parse($lastApproval['approved_at'] ?? $lastApproval['rejected_at']);
        }
        
        return now()->diffInMinutes($stepStartTime);
    }

    private function getApprovalStatus($expense)
    {
        $approvalPath = $expense->approval_path ?? [];
        $approvalHistory = $expense->approval_history ?? [];
        $currentStep = $this->getCurrentApprovalStep($expense);
        
        return [
            'expense_id' => $expense->id,
            'approval_status' => $expense->approval_status,
            'current_step' => $currentStep,
            'completed_steps' => count($approvalHistory),
            'total_steps' => count($approvalPath),
            'progress_percentage' => count($approvalPath) > 0 ? 
                round((count($approvalHistory) / count($approvalPath)) * 100, 2) : 0,
            'estimated_completion' => $this->calculateEstimatedCompletion(
                array_slice($approvalPath, count($approvalHistory))
            ),
            'approval_history' => $approvalHistory
        ];
    }

    // Placeholder methods for integration
    private function updateFinancialRecords($expense) { /* Implementation needed */ }
    private function generateAccountingEntries($expense) { /* Implementation needed */ }
    private function updateBudgetTracking($expense) { /* Implementation needed */ }
    private function updateExpenseAnalytics($expense) { /* Implementation needed */ }
    private function sendApprovalNotifications($expense, $type) { /* Implementation needed */ }
}
