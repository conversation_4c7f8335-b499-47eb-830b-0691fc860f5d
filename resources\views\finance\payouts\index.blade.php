@extends('layouts.app')

@section('title', 'Quản lý Payouts - Dropship Manager')
@section('page-title', 'Quản lý eBay Payouts')

@section('content')
<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('finance.payouts.index') }}" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">T<PERSON><PERSON> kiếm</label>
                <input type="text" name="search" class="form-control" placeholder="Mã payout, tài khoản..." value="{{ request('search') }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ xử lý</option>
                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}><PERSON><PERSON><PERSON> thành</option>
                    <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>Thất bại</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Team</label>
                <select name="team_id" class="form-select">
                    <option value="">Tất cả teams</option>
                    @foreach($teams ?? [] as $team)
                        <option value="{{ $team->id }}" {{ request('team_id') == $team->id ? 'selected' : '' }}>
                            {{ $team->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Sắp xếp</label>
                <select name="sort_by" class="form-select">
                    <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Ngày tạo</option>
                    <option value="amount_vnd" {{ request('sort_by') == 'amount_vnd' ? 'selected' : '' }}>Số tiền</option>
                    <option value="payout_date" {{ request('sort_by') == 'payout_date' ? 'selected' : '' }}>Ngày payout</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Payouts Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách Payouts</h6>
        <div class="btn-group">
            <a href="{{ route('finance.payouts.quick-create') }}" class="btn btn-success btn-sm">
                <i class="fas fa-plus"></i> Nhập nhanh
            </a>
            <a href="{{ route('finance.payouts.create') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Thêm mới
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Mã Payout</th>
                        <th>Tài khoản eBay</th>
                        <th>Team</th>
                        <th>Số tiền</th>
                        <th>Ngày payout</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($payouts ?? [] as $payout)
                    <tr>
                        <td>
                            <strong>{{ $payout->payout_number }}</strong>
                        </td>
                        <td>
                            {{ $payout->ebayAccount->account_name ?? 'N/A' }}
                            <br><small class="text-muted">{{ $payout->ebayAccount->email ?? '' }}</small>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ $payout->team->name ?? 'N/A' }}</span>
                        </td>
                        <td>
                            <div>
                                <strong>{{ number_format($payout->amount_vnd) }}đ</strong><br>
                                <small class="text-muted">{{ $payout->amount }} {{ $payout->currency }}</small>
                            </div>
                        </td>
                        <td>{{ $payout->payout_date->format('d/m/Y') }}</td>
                        <td>
                            <span class="badge bg-{{ 
                                $payout->status == 'completed' ? 'success' : 
                                ($payout->status == 'failed' ? 'danger' : 'warning') 
                            }}">
                                {{ ucfirst($payout->status) }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('finance.payouts.show', $payout) }}" class="btn btn-info btn-sm" title="Xem chi tiết">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('finance.payouts.edit', $payout) }}" class="btn btn-warning btn-sm" title="Chỉnh sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="{{ route('finance.payouts.destroy', $payout) }}" method="POST" class="d-inline" 
                                      onsubmit="return confirm('Bạn có chắc chắn muốn xóa payout này?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger btn-sm" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center">Không có payout nào</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if(isset($payouts) && $payouts->hasPages())
        <div class="d-flex justify-content-center">
            {{ $payouts->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>
@endsection
