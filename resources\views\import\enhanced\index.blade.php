@extends('layouts.app')

@section('title', 'Import Đơn hàng từ CSV')
@section('page-title', 'Import Đơn hàng từ CSV')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('orders.enhanced.index') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        Quay lại đơn hàng
    </a>
    <a href="{{ route('import.enhanced.template') }}" class="btn btn-success">
        <i class="fas fa-download me-1"></i>
        Tải template CSV
    </a>
</div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Hướng dẫn Import
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📋 Cấu trúc file CSV cần có:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-1"></i> <strong>Date</strong>: Ngày đặt hàng (dd/mm/yyyy)</li>
                                <li><i class="fas fa-check text-success me-1"></i> <strong>Buyer ID</strong>: ID người mua (bắt buộc)</li>
                                <li><i class="fas fa-check text-success me-1"></i> <strong>SKU</strong>: Mã sản phẩm</li>
                                <li><i class="fas fa-check text-success me-1"></i> <strong>Giá Gốc</strong>: Giá cost từ supplier</li>
                                <li><i class="fas fa-check text-success me-1"></i> <strong>SL</strong>: Số lượng (bắt buộc)</li>
                                <li><i class="fas fa-check text-success me-1"></i> <strong>Giá Ebay</strong>: Giá bán (bắt buộc)</li>
                                <li><i class="fas fa-check text-success me-1"></i> <strong>Earnings</strong>: Thu nhập thực tế</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>⚠️ Lưu ý quan trọng:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-exclamation-triangle text-warning me-1"></i> File CSV phải có encoding UTF-8</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-1"></i> Dòng đầu tiên là header</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-1"></i> Giá tiền có thể có dấu phẩy/chấm</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-1"></i> Sản phẩm chưa có sẽ được tạo tự động</li>
                                <li><i class="fas fa-exclamation-triangle text-warning me-1"></i> Supplier chưa có sẽ được tạo tự động</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Import Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        Import Đơn hàng
                    </h5>
                </div>
                <div class="card-body">
                    <form id="importForm" enctype="multipart/form-data">
                        @csrf
                        
                        <!-- Step 1: File Upload -->
                        <div class="import-step" id="step1">
                            <h6 class="mb-3">Bước 1: Chọn file CSV</h6>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label">File CSV <span class="text-danger">*</span></label>
                                        <input type="file" class="form-control" id="csvFile" name="csv_file" 
                                               accept=".csv,.txt" required>
                                        <div class="form-text">
                                            Chọn file CSV từ Google Sheets hoặc Excel. Tối đa 10MB.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Xem trước</label>
                                        <button type="button" class="btn btn-outline-info w-100" id="previewBtn" disabled>
                                            <i class="fas fa-eye me-1"></i>
                                            Xem trước dữ liệu
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-primary" id="nextStep1" disabled>
                                    Tiếp theo
                                    <i class="fas fa-arrow-right ms-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 2: Configuration -->
                        <div class="import-step d-none" id="step2">
                            <h6 class="mb-3">Bước 2: Cấu hình import</h6>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Platform <span class="text-danger">*</span></label>
                                        <select class="form-select" name="platform" required>
                                            <option value="">-- Chọn Platform --</option>
                                            <option value="ebay">eBay</option>
                                            <option value="amazon">Amazon</option>
                                            <option value="shopify">Shopify</option>
                                            <option value="facebook">Facebook</option>
                                            <option value="other">Khác</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Tài khoản Platform <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="platform_account" 
                                               placeholder="Tên tài khoản" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Team <span class="text-danger">*</span></label>
                                        <select class="form-select" name="team_id" required>
                                            @if(auth()->user()->isAdmin())
                                                @foreach(\App\Models\Team::all() as $team)
                                                    <option value="{{ $team->id }}" 
                                                            {{ auth()->user()->team_id == $team->id ? 'selected' : '' }}>
                                                        {{ $team->name }}
                                                    </option>
                                                @endforeach
                                            @else
                                                <option value="{{ auth()->user()->team_id }}" selected>
                                                    {{ auth()->user()->team->name }}
                                                </option>
                                            @endif
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" id="prevStep2">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Quay lại
                                </button>
                                <button type="button" class="btn btn-primary" id="nextStep2">
                                    Tiếp theo
                                    <i class="fas fa-arrow-right ms-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Step 3: Preview & Confirm -->
                        <div class="import-step d-none" id="step3">
                            <h6 class="mb-3">Bước 3: Xác nhận và Import</h6>
                            
                            <div id="previewData" class="mb-4">
                                <!-- Preview data will be loaded here -->
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" id="prevStep3">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    Quay lại
                                </button>
                                <button type="submit" class="btn btn-success" id="importBtn">
                                    <i class="fas fa-upload me-1"></i>
                                    Bắt đầu Import
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Progress -->
            <div class="card mt-4 d-none" id="progressCard">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        Đang xử lý...
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="progressText">Đang chuẩn bị...</div>
                </div>
            </div>

            <!-- Results -->
            <div class="card mt-4 d-none" id="resultsCard">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Kết quả Import
                    </h5>
                </div>
                <div class="card-body" id="resultsContent">
                    <!-- Results will be displayed here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xem trước dữ liệu CSV</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Đang tải...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.import-step {
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.progress {
    height: 25px;
}

.table-preview {
    font-size: 0.875rem;
}

.table-preview th,
.table-preview td {
    padding: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.step-indicator .step {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.step-indicator .step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

.step-indicator .step.active .step-number {
    background-color: #007bff;
    color: white;
}

.step-indicator .step.completed .step-number {
    background-color: #28a745;
    color: white;
}
</style>
@endpush

@push('scripts')
<script>
let currentStep = 1;
let csvData = null;

// File input change handler
document.getElementById('csvFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        document.getElementById('previewBtn').disabled = false;
        document.getElementById('nextStep1').disabled = false;
    } else {
        document.getElementById('previewBtn').disabled = true;
        document.getElementById('nextStep1').disabled = true;
    }
});

// Preview button handler
document.getElementById('previewBtn').addEventListener('click', function() {
    const fileInput = document.getElementById('csvFile');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('Vui lòng chọn file CSV trước!');
        return;
    }
    
    const formData = new FormData();
    formData.append('csv_file', file);
    
    fetch('{{ route("import.enhanced.validate") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showPreview(data.data);
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi xem trước file');
    });
});

// Step navigation
document.getElementById('nextStep1').addEventListener('click', () => showStep(2));
document.getElementById('prevStep2').addEventListener('click', () => showStep(1));
document.getElementById('nextStep2').addEventListener('click', () => showStep(3));
document.getElementById('prevStep3').addEventListener('click', () => showStep(2));

// Form submission
document.getElementById('importForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Show progress
    document.getElementById('progressCard').classList.remove('d-none');
    document.getElementById('resultsCard').classList.add('d-none');
    
    // Simulate progress
    let progress = 0;
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');
    
    const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 90) progress = 90;
        
        progressBar.style.width = progress + '%';
        progressText.textContent = `Đang xử lý... ${Math.round(progress)}%`;
    }, 500);
    
    // Submit form
    fetch('{{ route("import.enhanced.orders") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressText.textContent = 'Hoàn thành!';
        
        setTimeout(() => {
            document.getElementById('progressCard').classList.add('d-none');
            showResults(data);
        }, 1000);
    })
    .catch(error => {
        clearInterval(progressInterval);
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi import dữ liệu');
        document.getElementById('progressCard').classList.add('d-none');
    });
});

function showStep(step) {
    // Hide all steps
    document.querySelectorAll('.import-step').forEach(el => {
        el.classList.add('d-none');
    });
    
    // Show current step
    document.getElementById(`step${step}`).classList.remove('d-none');
    currentStep = step;
}

function showPreview(data) {
    let html = `
        <div class="alert alert-info">
            <strong>Thông tin file:</strong> ${data.total_rows} dòng dữ liệu
        </div>
    `;
    
    if (data.issues.length > 0) {
        html += `
            <div class="alert alert-warning">
                <strong>Cảnh báo:</strong>
                <ul class="mb-0">
                    ${data.issues.map(issue => `<li>${issue}</li>`).join('')}
                </ul>
            </div>
        `;
    }
    
    html += `
        <h6>Headers:</h6>
        <div class="mb-3">
            ${data.headers.map(header => `<span class="badge bg-secondary me-1">${header}</span>`).join('')}
        </div>
        
        <h6>Dữ liệu mẫu (5 dòng đầu):</h6>
        <div class="table-responsive">
            <table class="table table-sm table-preview">
                <thead>
                    <tr>
                        ${data.headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${data.sample_data.map(row => `
                        <tr>
                            ${row.map(cell => `<td title="${cell}">${cell}</td>`).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = html;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

function showResults(data) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    
    let html = '';
    
    if (data.success) {
        html = `
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle me-2"></i>Import thành công!</h5>
                <p class="mb-0">${data.message}</p>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>${data.data.total_rows}</h3>
                            <p class="mb-0">Tổng dòng</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>${data.data.imported}</h3>
                            <p class="mb-0">Đã import</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3>${data.data.skipped}</h3>
                            <p class="mb-0">Bỏ qua</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        if (data.data.errors.length > 0) {
            html += `
                <div class="alert alert-warning mt-3">
                    <h6>Các lỗi gặp phải:</h6>
                    <ul class="mb-0">
                        ${data.data.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            `;
        }
        
        html += `
            <div class="mt-3">
                <a href="{{ route('orders.enhanced.index') }}" class="btn btn-primary">
                    <i class="fas fa-list me-1"></i>
                    Xem danh sách đơn hàng
                </a>
            </div>
        `;
    } else {
        html = `
            <div class="alert alert-danger">
                <h5><i class="fas fa-exclamation-circle me-2"></i>Import thất bại!</h5>
                <p class="mb-0">${data.message}</p>
            </div>
        `;
    }
    
    resultsContent.innerHTML = html;
    resultsCard.classList.remove('d-none');
}
</script>
@endpush
@endsection
