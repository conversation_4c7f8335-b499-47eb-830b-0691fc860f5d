<?php

namespace Database\Seeders;

use App\Services\SettingsService;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settingsService = new SettingsService();
        $settingsService->initializeDefaults();

        $this->command->info('Default settings have been initialized successfully!');
    }
}
