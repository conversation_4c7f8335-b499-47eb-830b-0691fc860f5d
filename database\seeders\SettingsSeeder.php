<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'app_name',
                'value' => 'Dropship Manager',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Tên ứng dụng',
                'description' => 'Tên hiển thị của ứng dụng',
                'validation_rules' => ['required', 'string', 'max:255'],
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'app_description',
                'value' => '<PERSON>ệ thống quản lý dropshipping chuyên nghiệ<PERSON>',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Mô tả ứng dụng',
                'description' => '<PERSON>ô tả ngắn về ứng dụng',
                'validation_rules' => ['string', 'max:500'],
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'app_timezone',
                'value' => 'Asia/Ho_Chi_Minh',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Múi giờ',
                'description' => 'Múi giờ mặc định của hệ thống',
                'validation_rules' => ['required', 'string'],
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'maintenance_mode',
                'value' => false,
                'type' => 'boolean',
                'category' => 'general',
                'label' => 'Chế độ bảo trì',
                'description' => 'Bật/tắt chế độ bảo trì hệ thống',
                'validation_rules' => ['boolean'],
                'is_public' => false,
                'sort_order' => 4,
            ],

            // Financial Settings
            [
                'key' => 'default_currency',
                'value' => 'VND',
                'type' => 'string',
                'category' => 'financial',
                'label' => 'Đơn vị tiền tệ mặc định',
                'description' => 'Đơn vị tiền tệ sử dụng trong hệ thống',
                'validation_rules' => ['required', 'string', 'in:VND,USD,EUR'],
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'exchange_rate_usd_vnd',
                'value' => 24000,
                'type' => 'integer',
                'category' => 'financial',
                'label' => 'Tỷ giá USD/VND',
                'description' => 'Tỷ giá chuyển đổi từ USD sang VND',
                'validation_rules' => ['required', 'numeric', 'min:1'],
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'auto_approve_expense_limit',
                'value' => 1000000,
                'type' => 'integer',
                'category' => 'financial',
                'label' => 'Hạn mức tự động duyệt chi phí',
                'description' => 'Chi phí dưới mức này sẽ được tự động duyệt (VND)',
                'validation_rules' => ['numeric', 'min:0'],
                'is_public' => false,
                'sort_order' => 3,
            ],

            // Notification Settings
            [
                'key' => 'notification_email_enabled',
                'value' => true,
                'type' => 'boolean',
                'category' => 'notification',
                'label' => 'Bật thông báo email',
                'description' => 'Cho phép gửi thông báo qua email',
                'validation_rules' => ['boolean'],
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'notification_admin_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'category' => 'notification',
                'label' => 'Email admin',
                'description' => 'Email nhận thông báo quan trọng',
                'validation_rules' => ['required', 'email'],
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'smtp_host',
                'value' => '',
                'type' => 'string',
                'category' => 'notification',
                'label' => 'SMTP Host',
                'description' => 'Địa chỉ SMTP server (VD: smtp.gmail.com)',
                'validation_rules' => ['nullable', 'string', 'max:255'],
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'type' => 'integer',
                'category' => 'notification',
                'label' => 'SMTP Port',
                'description' => 'Port kết nối SMTP (thường là 587 cho TLS)',
                'validation_rules' => ['nullable', 'integer', 'min:1', 'max:65535'],
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'smtp_username',
                'value' => '',
                'type' => 'string',
                'category' => 'notification',
                'label' => 'SMTP Username',
                'description' => 'Email đăng nhập SMTP',
                'validation_rules' => ['nullable', 'email'],
                'is_encrypted' => true,
                'is_public' => false,
                'sort_order' => 5,
            ],
            [
                'key' => 'smtp_password',
                'value' => '',
                'type' => 'string',
                'category' => 'notification',
                'label' => 'SMTP Password',
                'description' => 'Mật khẩu hoặc App Password cho SMTP',
                'validation_rules' => ['nullable', 'string'],
                'is_encrypted' => true,
                'is_public' => false,
                'sort_order' => 6,
            ],
            [
                'key' => 'notification_frequency',
                'value' => 'immediate',
                'type' => 'select',
                'category' => 'notification',
                'label' => 'Tần suất thông báo',
                'description' => 'Tần suất gửi email notifications',
                'validation_rules' => ['required', 'in:immediate,hourly,daily,weekly'],
                'is_public' => false,
                'sort_order' => 7,
            ],

            // API Settings
            [
                'key' => 'api_rate_limit',
                'value' => 1000,
                'type' => 'integer',
                'category' => 'api',
                'label' => 'Giới hạn API requests',
                'description' => 'Số lượng requests tối đa mỗi giờ',
                'validation_rules' => ['required', 'integer', 'min:1'],
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'google_sheets_api_key',
                'value' => '',
                'type' => 'string',
                'category' => 'api',
                'label' => 'Google Sheets API Key',
                'description' => 'API key để tích hợp với Google Sheets',
                'validation_rules' => ['string'],
                'is_encrypted' => true,
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'ebay_api_key',
                'value' => '',
                'type' => 'string',
                'category' => 'api',
                'label' => 'eBay API Key',
                'description' => 'API key để tự động import payouts từ eBay',
                'validation_rules' => ['nullable', 'string'],
                'is_encrypted' => true,
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'exchange_rate_api_key',
                'value' => '',
                'type' => 'string',
                'category' => 'api',
                'label' => 'Exchange Rate API Key',
                'description' => 'API key để lấy tỷ giá thời gian thực',
                'validation_rules' => ['nullable', 'string'],
                'is_encrypted' => true,
                'is_public' => false,
                'sort_order' => 4,
            ],

            // Checkout Settings
            [
                'key' => 'checkout_default_rate',
                'value' => 15.0,
                'type' => 'float',
                'category' => 'checkout',
                'label' => 'Tỷ lệ checkout mặc định (%)',
                'description' => 'Tỷ lệ checkout mặc định cho đơn vị mới',
                'validation_rules' => ['required', 'numeric', 'min:0', 'max:100'],
                'is_public' => false,
                'sort_order' => 1,
            ],

            // Team Settings
            [
                'key' => 'team_max_members',
                'value' => 50,
                'type' => 'integer',
                'category' => 'team',
                'label' => 'Số thành viên tối đa mỗi team',
                'description' => 'Giới hạn số lượng thành viên trong một team',
                'validation_rules' => ['required', 'integer', 'min:1'],
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'max_members_per_team',
                'value' => 50,
                'type' => 'integer',
                'category' => 'team',
                'label' => 'Số lượng members tối đa per team',
                'description' => 'Giới hạn số lượng thành viên tối đa trong một team',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:1000'],
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'default_user_role',
                'value' => 'user',
                'type' => 'select',
                'category' => 'team',
                'label' => 'Role mặc định cho user mới',
                'description' => 'Role được gán tự động cho user mới khi đăng ký',
                'validation_rules' => ['required', 'in:user,seller'],
                'is_public' => false,
                'sort_order' => 3,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        $this->command->info('Default settings have been initialized successfully!');
    }
}
