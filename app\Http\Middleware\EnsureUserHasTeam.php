<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserHasTeam
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        // If user is not authenticated, let auth middleware handle it
        if (!$user) {
            return $next($request);
        }
        
        // Check if user has team_id
        if (!$user->team_id) {
            // If it's an AJAX request, return JSON error
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'error' => 'User must be assigned to a team to access this resource.',
                    'redirect' => route('profile.edit')
                ], 403);
            }
            
            // For regular requests, redirect to profile with error message
            return redirect()->route('profile.edit')
                ->with('error', 'You must be assigned to a team to access this feature. Please contact your administrator.');
        }
        
        return $next($request);
    }
}
