<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use App\Models\Team;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Tạo notification cho user cụ thể
     */
    public function createForUser($userId, $type, $title, $message, $data = null, $priority = 'medium', $actionUrl = null)
    {
        try {
            $user = User::find($userId);
            if (!$user) {
                Log::warning("User not found for notification: {$userId}");
                return null;
            }

            return Notification::create([
                'type' => $type,
                'user_id' => $userId,
                'team_id' => $user->team_id,
                'title' => $title,
                'message' => $message,
                'data' => $data,
                'action_url' => $actionUrl,
                'priority' => $priority
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to create notification: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Tạo notification cho tất cả members của team
     */
    public function createForTeam($teamId, $type, $title, $message, $data = null, $priority = 'medium', $actionUrl = null, $excludeUserId = null)
    {
        try {
            $team = Team::with('members')->find($teamId);
            if (!$team) {
                Log::warning("Team not found for notification: {$teamId}");
                return [];
            }

            $notifications = [];
            foreach ($team->members as $member) {
                if ($excludeUserId && $member->id == $excludeUserId) {
                    continue;
                }

                $notification = $this->createForUser(
                    $member->id,
                    $type,
                    $title,
                    $message,
                    $data,
                    $priority,
                    $actionUrl
                );

                if ($notification) {
                    $notifications[] = $notification;
                }
            }

            return $notifications;
        } catch (\Exception $e) {
            Log::error("Failed to create team notifications: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Tạo notification cho team leaders
     */
    public function createForTeamLeaders($type, $title, $message, $data = null, $priority = 'medium', $actionUrl = null)
    {
        try {
            $teamLeaders = User::where('role', 'team_leader')->get();
            $notifications = [];

            foreach ($teamLeaders as $leader) {
                $notification = $this->createForUser(
                    $leader->id,
                    $type,
                    $title,
                    $message,
                    $data,
                    $priority,
                    $actionUrl
                );

                if ($notification) {
                    $notifications[] = $notification;
                }
            }

            return $notifications;
        } catch (\Exception $e) {
            Log::error("Failed to create team leader notifications: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Tạo notification cho admins
     */
    public function createForAdmins($type, $title, $message, $data = null, $priority = 'medium', $actionUrl = null)
    {
        try {
            $admins = User::where('role', 'admin')->get();
            $notifications = [];

            foreach ($admins as $admin) {
                $notification = $this->createForUser(
                    $admin->id,
                    $type,
                    $title,
                    $message,
                    $data,
                    $priority,
                    $actionUrl
                );

                if ($notification) {
                    $notifications[] = $notification;
                }
            }

            return $notifications;
        } catch (\Exception $e) {
            Log::error("Failed to create admin notifications: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Notification cho payout mới
     */
    public function payoutCreated($payout, $createdBy)
    {
        $title = "Payout mới được tạo";
        $message = "Payout {$payout->formatted_amount_vnd} cho account {$payout->ebayAccount->account_name} đã được tạo bởi {$createdBy->name}";
        $actionUrl = route('finance.payouts.show', $payout);
        
        $data = [
            'payout_id' => $payout->id,
            'amount' => $payout->amount_vnd,
            'account_name' => $payout->ebayAccount->account_name,
            'created_by' => $createdBy->name
        ];

        // Notify team leader
        if ($payout->team->leader) {
            $this->createForUser(
                $payout->team->leader->id,
                'payout_created',
                $title,
                $message,
                $data,
                'medium',
                $actionUrl
            );
        }

        // Notify admins
        $this->createForAdmins(
            'payout_created',
            $title,
            $message,
            $data,
            'low',
            $actionUrl
        );
    }

    /**
     * Notification cho expense cần duyệt
     */
    public function expenseNeedsApproval($expense, $createdBy)
    {
        $title = "Chi phí cần duyệt";
        $message = "Chi phí {$expense->formatted_amount} ({$expense->title}) cần được duyệt từ {$createdBy->name}";
        $actionUrl = route('finance.expenses.show', $expense);
        
        $data = [
            'expense_id' => $expense->id,
            'amount' => $expense->amount,
            'title' => $expense->title,
            'created_by' => $createdBy->name
        ];

        // Notify team leader
        if ($expense->team->leader && $expense->team->leader->id != $createdBy->id) {
            $this->createForUser(
                $expense->team->leader->id,
                'expense_approval_needed',
                $title,
                $message,
                $data,
                'high',
                $actionUrl
            );
        }

        // Notify admins
        $this->createForAdmins(
            'expense_approval_needed',
            $title,
            $message,
            $data,
            'medium',
            $actionUrl
        );
    }

    /**
     * Notification cho expense đã được duyệt
     */
    public function expenseApproved($expense, $approvedBy)
    {
        $title = "Chi phí đã được duyệt";
        $message = "Chi phí {$expense->formatted_amount} ({$expense->title}) đã được duyệt bởi {$approvedBy->name}";
        $actionUrl = route('finance.expenses.show', $expense);
        
        $data = [
            'expense_id' => $expense->id,
            'amount' => $expense->amount,
            'title' => $expense->title,
            'approved_by' => $approvedBy->name
        ];

        // Notify creator
        if ($expense->createdBy && $expense->createdBy->id != $approvedBy->id) {
            $this->createForUser(
                $expense->createdBy->id,
                'expense_approved',
                $title,
                $message,
                $data,
                'medium',
                $actionUrl
            );
        }
    }

    /**
     * Notification cho checkout order status change
     */
    public function checkoutOrderStatusChanged($order, $oldStatus, $newStatus, $updatedBy)
    {
        $title = "Trạng thái đơn hàng checkout thay đổi";
        $message = "Đơn hàng {$order->checkout_order_number} đã chuyển từ {$oldStatus} sang {$newStatus} bởi {$updatedBy->name}";
        $actionUrl = route('checkout.orders.show', $order);
        
        $data = [
            'order_id' => $order->id,
            'order_number' => $order->checkout_order_number,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'updated_by' => $updatedBy->name
        ];

        // Notify checkout unit contact person if exists
        if ($order->checkoutUnit && $order->checkoutUnit->contact_email) {
            // This would need email notification implementation
        }

        // Notify team members
        $this->createForTeam(
            $order->checkoutUnit->team_id ?? 1, // Default team if no team assigned
            'checkout_order_status_changed',
            $title,
            $message,
            $data,
            'low',
            $actionUrl,
            $updatedBy->id
        );
    }

    /**
     * Notification cho team member mới
     */
    public function teamMemberAdded($team, $newMember, $addedBy)
    {
        $title = "Thành viên mới được thêm vào team";
        $message = "{$newMember->name} đã được thêm vào team {$team->name} bởi {$addedBy->name}";
        $actionUrl = route('finance.teams.show', $team);
        
        $data = [
            'team_id' => $team->id,
            'team_name' => $team->name,
            'new_member_name' => $newMember->name,
            'added_by' => $addedBy->name
        ];

        // Notify all team members except the new member and the one who added
        $this->createForTeam(
            $team->id,
            'team_member_added',
            $title,
            $message,
            $data,
            'low',
            $actionUrl,
            $newMember->id
        );
    }

    /**
     * Mark notification as read
     */
    public function markAsRead($notificationId, $userId)
    {
        try {
            $notification = Notification::where('id', $notificationId)
                                      ->where('user_id', $userId)
                                      ->first();
            
            if ($notification) {
                $notification->markAsRead();
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error("Failed to mark notification as read: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Mark all notifications as read for user
     */
    public function markAllAsRead($userId)
    {
        try {
            Notification::where('user_id', $userId)
                       ->whereNull('read_at')
                       ->update(['read_at' => now()]);
            
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to mark all notifications as read: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get unread count for user
     */
    public function getUnreadCount($userId)
    {
        return Notification::where('user_id', $userId)->unread()->count();
    }

    /**
     * Get recent notifications for user
     */
    public function getRecentNotifications($userId, $limit = 10)
    {
        return Notification::where('user_id', $userId)
                          ->orderBy('created_at', 'desc')
                          ->limit($limit)
                          ->get();
    }
}
