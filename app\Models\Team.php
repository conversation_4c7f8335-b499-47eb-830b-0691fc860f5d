<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Team extends Model
{
    protected $fillable = [
        'name',
        'code',
        'description',
        'leader_id',
        'status',
        'settings'
    ];

    protected $casts = [
        'settings' => 'array'
    ];

    // Tự động tạo code khi tạo team mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($team) {
            if (empty($team->code)) {
                $team->code = strtoupper(Str::slug($team->name, '_'));
            }
        });
    }

    // Relationship với User (leader)
    public function leader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'leader_id');
    }

    // Relationship với Users (members)
    public function members(): HasMany
    {
        return $this->hasMany(User::class);
    }

    // Relationship với EbayAccounts
    public function ebayAccounts(): Has<PERSON>any
    {
        return $this->hasMany(EbayAccount::class);
    }

    // Relationship với EbayPayouts
    public function ebayPayouts(): HasMany
    {
        return $this->hasMany(EbayPayout::class);
    }

    // Relationship với MonthlyExpenses
    public function monthlyExpenses(): HasMany
    {
        return $this->hasMany(MonthlyExpense::class);
    }

    // Scope để lấy các team đang active
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Tính tổng payout của team
    public function getTotalPayoutsAttribute()
    {
        return $this->ebayPayouts()->where('status', 'completed')->sum('amount_vnd');
    }

    // Tính tổng chi phí của team
    public function getTotalExpensesAttribute()
    {
        return $this->monthlyExpenses()->where('status', 'paid')->sum('amount');
    }

    // Tính lợi nhuận của team
    public function getProfitAttribute()
    {
        return $this->total_payouts - $this->total_expenses;
    }

    // Tính tổng payout theo tháng
    public function getMonthlyPayouts($year, $month)
    {
        return $this->ebayPayouts()
            ->whereYear('payout_date', $year)
            ->whereMonth('payout_date', $month)
            ->where('status', 'completed')
            ->sum('amount_vnd');
    }

    // Tính tổng chi phí theo tháng
    public function getMonthlyExpenses($year, $month)
    {
        return $this->monthlyExpenses()
            ->where('expense_month', $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT))
            ->where('status', 'paid')
            ->sum('amount');
    }

    // Tính lợi nhuận theo tháng
    public function getMonthlyProfit($year, $month)
    {
        $payouts = $this->getMonthlyPayouts($year, $month);
        $expenses = $this->getMonthlyExpenses($year, $month);
        return $payouts - $expenses;
    }
}
