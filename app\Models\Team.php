<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Team extends Model
{
    protected $fillable = [
        'name',
        'code',
        'description',
        'leader_id',
        'status',
        'settings'
    ];

    protected $casts = [
        'settings' => 'array'
    ];

    // Tự động tạo code khi tạo team mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($team) {
            if (empty($team->code)) {
                $team->code = strtoupper(Str::slug($team->name, '_'));
            }
        });

        // Validation khi cập nhật leader
        static::updating(function ($team) {
            if ($team->leader_id && $team->isDirty('leader_id')) {
                $leader = User::find($team->leader_id);
                if ($leader && $leader->team_id !== $team->id) {
                    throw new \Exception('Leader ph<PERSON><PERSON> là thành viên của team này.');
                }
            }
        });
    }

    // Relationship với User (leader)
    public function leader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'leader_id');
    }

    // Relationship với Users (members)
    public function members(): HasMany
    {
        return $this->hasMany(User::class);
    }

    // Relationship với EbayAccounts
    public function ebayAccounts(): HasMany
    {
        return $this->hasMany(EbayAccount::class);
    }

    // Relationship với EbayPayouts
    public function ebayPayouts(): HasMany
    {
        return $this->hasMany(EbayPayout::class);
    }

    // Relationship với MonthlyExpenses
    public function monthlyExpenses(): HasMany
    {
        return $this->hasMany(MonthlyExpense::class);
    }

    // Scope để lấy các team đang active
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Tính tổng payout của team
    public function getTotalPayoutsAttribute()
    {
        return $this->ebayPayouts()->where('status', 'completed')->sum('amount_vnd');
    }

    // Tính tổng chi phí của team
    public function getTotalExpensesAttribute()
    {
        return $this->monthlyExpenses()->where('status', 'paid')->sum('amount');
    }

    // Tính lợi nhuận của team
    public function getProfitAttribute()
    {
        return $this->total_payouts - $this->total_expenses;
    }

    // Tính tổng payout theo tháng
    public function getMonthlyPayouts($year, $month)
    {
        return $this->ebayPayouts()
            ->whereYear('payout_date', $year)
            ->whereMonth('payout_date', $month)
            ->where('status', 'completed')
            ->sum('amount_vnd');
    }

    // Tính tổng chi phí theo tháng
    public function getMonthlyExpenses($year, $month)
    {
        return $this->monthlyExpenses()
            ->where('expense_month', $year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT))
            ->where('status', 'paid')
            ->sum('amount');
    }

    // Tính lợi nhuận theo tháng
    public function getMonthlyProfit($year, $month)
    {
        $payouts = $this->getMonthlyPayouts($year, $month);
        $expenses = $this->getMonthlyExpenses($year, $month);
        return $payouts - $expenses;
    }

    // Lấy các members theo role
    public function getTeamLeaders()
    {
        return $this->members()->where('role', 'team_leader')->get();
    }

    public function getSellers()
    {
        return $this->members()->where('role', 'seller')->get();
    }

    public function getUsers()
    {
        return $this->members()->where('role', 'user')->get();
    }

    // Kiểm tra user có phải leader của team này không
    public function isLeader($userId)
    {
        return $this->leader_id == $userId;
    }

    // Kiểm tra user có phải member của team này không
    public function isMember($userId)
    {
        return $this->members()->where('id', $userId)->exists();
    }

    // Thêm member vào team
    public function addMember($userId, $role = 'user')
    {
        $user = User::find($userId);
        if ($user) {
            $user->update([
                'team_id' => $this->id,
                'role' => $role
            ]);
            return true;
        }
        return false;
    }

    // Xóa member khỏi team (không thể xóa leader)
    public function removeMember($userId)
    {
        if ($this->leader_id == $userId) {
            throw new \Exception('Không thể xóa leader khỏi team. Vui lòng chỉ định leader mới trước.');
        }

        $user = User::find($userId);
        if ($user && $user->team_id == $this->id) {
            // Cần gán user vào team khác hoặc tạo team mặc định
            $defaultTeam = Team::where('code', 'DEFAULT')->first();
            if (!$defaultTeam) {
                $defaultTeam = Team::create([
                    'name' => 'Unassigned Team',
                    'code' => 'UNASSIGNED',
                    'description' => 'Team cho users chưa được phân team',
                    'status' => 'active'
                ]);
            }

            $user->update([
                'team_id' => $defaultTeam->id,
                'role' => 'user'
            ]);
            return true;
        }
        return false;
    }

    // Thay đổi leader
    public function changeLeader($newLeaderId)
    {
        $newLeader = User::find($newLeaderId);

        if (!$newLeader) {
            throw new \Exception('User không tồn tại.');
        }

        if ($newLeader->team_id !== $this->id) {
            throw new \Exception('Leader mới phải là thành viên của team này.');
        }

        // Cập nhật role của leader cũ
        if ($this->leader_id) {
            $oldLeader = User::find($this->leader_id);
            if ($oldLeader) {
                $oldLeader->update(['role' => 'user']);
            }
        }

        // Cập nhật leader mới
        $newLeader->update(['role' => 'team_leader']);
        $this->update(['leader_id' => $newLeaderId]);

        return true;
    }

    // Lấy thống kê members
    public function getMemberStats()
    {
        $members = $this->members();

        return [
            'total' => $members->count(),
            'team_leaders' => $members->where('role', 'team_leader')->count(),
            'sellers' => $members->where('role', 'seller')->count(),
            'users' => $members->where('role', 'user')->count(),
            'active_accounts' => $this->ebayAccounts()->where('status', 'active')->count(),
            'total_accounts' => $this->ebayAccounts()->count(),
        ];
    }
}
