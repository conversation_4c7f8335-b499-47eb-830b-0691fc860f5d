@extends('layouts.app')

@section('title', 'Chi tiết Team - ' . $team->name)
@section('page-title', 'Team: ' . $team->name)

@section('page-actions')
<div class="btn-group" role="group">
    @if(auth()->user()->canManageTeam($team->id))
        <a href="{{ route('finance.teams.edit', $team) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>
            Chỉnh sửa Team
        </a>
        <a href="{{ route('finance.teams.members', $team) }}" class="btn btn-success">
            <i class="fas fa-users me-1"></i>
            Quản lý Members
        </a>
    @endif
    @if(auth()->user()->isAdmin())
        <a href="{{ route('finance.teams.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Quay lại
        </a>
    @endif
</div>
@endsection

@section('content')
<!-- Team Info -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thông tin Team</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Tên Team:</strong></td>
                                <td>{{ $team->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Mã Team:</strong></td>
                                <td><code>{{ $team->code }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>Mô tả:</strong></td>
                                <td>{{ $team->description ?: 'Chưa có mô tả' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Trạng thái:</strong></td>
                                <td>
                                    @if($team->status == 'active')
                                        <span class="badge bg-success">Hoạt động</span>
                                    @else
                                        <span class="badge bg-secondary">Không hoạt động</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Team Leader:</strong></td>
                                <td>
                                    @if($team->leader)
                                        <div>
                                            {{ $team->leader->name }}
                                            <br><small class="text-muted">{{ $team->leader->email }}</small>
                                        </div>
                                    @else
                                        <span class="text-muted">Chưa có leader</span>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Ngày tạo:</strong></td>
                                <td>{{ $team->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Cập nhật:</strong></td>
                                <td>{{ $team->updated_at->format('d/m/Y H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thống kê Members</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ $stats['members']['total'] }}</h4>
                        <small>Tổng Members</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ $stats['members']['sellers'] }}</h4>
                        <small>Sellers</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ $stats['members']['active_accounts'] }}</h4>
                        <small>eBay Accounts</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ $stats['members']['users'] }}</h4>
                        <small>Users</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Tổng Payout
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['finance']['total_payouts']) }}đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Tổng Chi phí
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['finance']['total_expenses']) }}đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-receipt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-left-{{ $stats['finance']['profit'] >= 0 ? 'primary' : 'warning' }} shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-{{ $stats['finance']['profit'] >= 0 ? 'primary' : 'warning' }} text-uppercase mb-1">
                            Lợi nhuận
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['finance']['profit']) }}đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Lợi nhuận tháng này
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($stats['finance']['monthly_payouts'] - $stats['finance']['monthly_expenses']) }}đ</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Members List -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Team Leaders & Sellers</h5>
            </div>
            <div class="card-body">
                @if($members['leaders']->count() > 0)
                    <h6 class="text-primary">Team Leaders</h6>
                    @foreach($members['leaders'] as $leader)
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ $leader->name }}</strong>
                            <br><small class="text-muted">{{ $leader->email }}</small>
                        </div>
                        <span class="badge bg-primary">Leader</span>
                    </div>
                    @endforeach
                    <hr>
                @endif

                @if($members['sellers']->count() > 0)
                    <h6 class="text-success">Sellers</h6>
                    @foreach($members['sellers'] as $seller)
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ $seller->name }}</strong>
                            <br><small class="text-muted">{{ $seller->email }}</small>
                            <br><small class="text-info">{{ $seller->ebayAccounts->count() }} eBay accounts</small>
                        </div>
                        <span class="badge bg-success">Seller</span>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted">Chưa có sellers nào</p>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Users khác</h5>
            </div>
            <div class="card-body">
                @if($members['users']->count() > 0)
                    @foreach($members['users'] as $user)
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>{{ $user->name }}</strong>
                            <br><small class="text-muted">{{ $user->email }}</small>
                        </div>
                        <span class="badge bg-secondary">User</span>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted">Chưa có users nào</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Payouts gần đây</h5>
            </div>
            <div class="card-body">
                @if($recentPayouts->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Account</th>
                                    <th>Số tiền</th>
                                    <th>Ngày</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentPayouts as $payout)
                                <tr>
                                    <td>
                                        <small>{{ $payout->ebayAccount->account_name }}</small>
                                        <br><small class="text-muted">{{ $payout->enteredBy->name }}</small>
                                    </td>
                                    <td class="text-success">{{ $payout->formatted_amount_vnd }}</td>
                                    <td><small>{{ $payout->payout_date->format('d/m/Y') }}</small></td>
                                    <td>
                                        <span class="badge bg-{{ $payout->status == 'completed' ? 'success' : 'warning' }} badge-sm">
                                            {{ $payout->status_label }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">Chưa có payouts nào</p>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Chi phí gần đây</h5>
            </div>
            <div class="card-body">
                @if($recentExpenses->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Loại</th>
                                    <th>Số tiền</th>
                                    <th>Ngày</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentExpenses as $expense)
                                <tr>
                                    <td>
                                        <small>{{ $expense->title }}</small>
                                        <br><small class="text-muted">{{ $expense->createdBy->name }}</small>
                                    </td>
                                    <td class="text-danger">{{ $expense->formatted_amount }}</td>
                                    <td><small>{{ $expense->expense_date->format('d/m/Y') }}</small></td>
                                    <td>
                                        <span class="badge bg-{{ $expense->status == 'paid' ? 'success' : 'warning' }} badge-sm">
                                            {{ $expense->status_label }}
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-muted">Chưa có chi phí nào</p>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
