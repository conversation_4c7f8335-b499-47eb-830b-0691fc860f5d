<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('checkout_orders', function (Blueprint $table) {
            $table->id();
            $table->string('checkout_order_number')->unique(); // Mã đơn checkout
            $table->foreignId('checkout_unit_id')->constrained()->onDelete('cascade'); // Đơn vị checkout
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null'); // Đơn hàng gốc (nếu có)
            $table->string('external_order_id')->nullable(); // ID đơn hàng từ hệ thống bên ngoài
            $table->string('customer_name'); // Tên khách hàng
            $table->string('customer_phone')->nullable(); // SĐT khách hàng
            $table->string('customer_email')->nullable(); // Email khách hàng
            $table->text('shipping_address'); // Địa chỉ giao hàng
            $table->decimal('order_value', 12, 2); // Giá trị đơn hàng
            $table->decimal('checkout_fee', 12, 2)->default(0); // Phí checkout
            $table->decimal('shipping_fee', 12, 2)->default(0); // Phí vận chuyển
            $table->decimal('total_amount', 12, 2); // Tổng tiền
            $table->enum('status', [
                'pending',      // Chờ xử lý
                'confirmed',    // Đã xác nhận
                'processing',   // Đang xử lý
                'shipped',      // Đã gửi hàng
                'delivered',    // Đã giao hàng
                'completed',    // Hoàn thành
                'cancelled',    // Đã hủy
                'returned',     // Trả hàng
                'refunded'      // Hoàn tiền
            ])->default('pending');
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'refunded'])->default('pending');
            $table->text('notes')->nullable(); // Ghi chú
            $table->json('tracking_info')->nullable(); // Thông tin tracking (JSON)
            $table->timestamp('confirmed_at')->nullable(); // Thời gian xác nhận
            $table->timestamp('shipped_at')->nullable(); // Thời gian gửi hàng
            $table->timestamp('delivered_at')->nullable(); // Thời gian giao hàng
            $table->timestamp('completed_at')->nullable(); // Thời gian hoàn thành
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checkout_orders');
    }
};
