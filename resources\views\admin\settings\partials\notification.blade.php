<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-envelope me-1"></i>
        <PERSON><PERSON><PERSON> <PERSON><PERSON>nh <PERSON>TP
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_host" class="form-label">SMTP Host</label>
                <input type="text" 
                       class="form-control @error('smtp_host') is-invalid @enderror" 
                       id="smtp_host" 
                       name="smtp_host" 
                       value="{{ old('smtp_host', $values['smtp_host'] ?? '') }}"
                       placeholder="smtp.gmail.com">
                <div class="form-text">Địa chỉ SMTP server (VD: smtp.gmail.com, smtp.outlook.com)</div>
                @error('smtp_host')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_port" class="form-label">SMTP Port</label>
                <select class="form-select @error('smtp_port') is-invalid @enderror" 
                        id="smtp_port" 
                        name="smtp_port">
                    @php
                        $ports = [
                            '587' => '587 (TLS - Recommended)',
                            '465' => '465 (SSL)',
                            '25' => '25 (Non-encrypted)',
                            '2525' => '2525 (Alternative)'
                        ];
                        $currentPort = old('smtp_port', $values['smtp_port'] ?? '587');
                    @endphp
                    @foreach($ports as $value => $label)
                        <option value="{{ $value }}" {{ $currentPort == $value ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
                <div class="form-text">Port kết nối SMTP (thường là 587 cho TLS)</div>
                @error('smtp_port')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_username" class="form-label">
                    SMTP Username
                    <span class="badge bg-warning text-dark ms-1">Encrypted</span>
                </label>
                <div class="input-group">
                    <input type="email" 
                           class="form-control @error('smtp_username') is-invalid @enderror" 
                           id="smtp_username" 
                           name="smtp_username" 
                           value="{{ old('smtp_username', $values['smtp_username'] ? '••••••••••••••••' : '') }}"
                           placeholder="<EMAIL>">
                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('smtp_username')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-text">Email đăng nhập SMTP</div>
                @error('smtp_username')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label for="smtp_password" class="form-label">
                    SMTP Password
                    <span class="badge bg-warning text-dark ms-1">Encrypted</span>
                </label>
                <div class="input-group">
                    <input type="password" 
                           class="form-control @error('smtp_password') is-invalid @enderror" 
                           id="smtp_password" 
                           name="smtp_password" 
                           value="{{ old('smtp_password', $values['smtp_password'] ? '••••••••••••••••' : '') }}"
                           placeholder="App Password hoặc mật khẩu email">
                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('smtp_password')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-text">Mật khẩu hoặc App Password cho SMTP</div>
                @error('smtp_password')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="setting-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">Test kết nối SMTP</h6>
                        <small class="text-muted">Kiểm tra cấu hình SMTP có hoạt động không</small>
                    </div>
                    <button type="button" class="btn btn-outline-primary" onclick="testApi('smtp', this)">
                        <i class="fas fa-paper-plane me-1"></i>
                        Test SMTP
                    </button>
                </div>
                <div class="test-result mt-2" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-clock me-1"></i>
        Tần suất thông báo
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="notification_frequency" class="form-label">
                    Tần suất gửi thông báo <span class="text-danger">*</span>
                </label>
                <select class="form-select @error('notification_frequency') is-invalid @enderror" 
                        id="notification_frequency" 
                        name="notification_frequency" 
                        required>
                    @php
                        $frequencies = [
                            'immediate' => 'Ngay lập tức',
                            'hourly' => 'Mỗi giờ',
                            'daily' => 'Hàng ngày (9:00 AM)',
                            'weekly' => 'Hàng tuần (Thứ 2, 9:00 AM)'
                        ];
                        $currentFrequency = old('notification_frequency', $values['notification_frequency'] ?? 'immediate');
                    @endphp
                    @foreach($frequencies as $value => $label)
                        <option value="{{ $value }}" {{ $currentFrequency === $value ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
                <div class="form-text">Tần suất gửi email notifications cho users</div>
                @error('notification_frequency')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label class="form-label">Mô tả tần suất</label>
                <div class="card bg-light">
                    <div class="card-body">
                        <div id="frequencyDescription">
                            @php
                                $descriptions = [
                                    'immediate' => 'Gửi email ngay khi có thông báo mới. Phù hợp cho môi trường cần phản hồi nhanh.',
                                    'hourly' => 'Gửi email tổng hợp mỗi giờ. Giảm spam email nhưng vẫn kịp thời.',
                                    'daily' => 'Gửi email tổng hợp hàng ngày lúc 9:00 AM. Phù hợp cho thông báo không khẩn cấp.',
                                    'weekly' => 'Gửi email tổng hợp hàng tuần vào thứ 2. Phù hợp cho báo cáo định kỳ.'
                                ];
                                $currentDesc = $descriptions[$currentFrequency] ?? $descriptions['immediate'];
                            @endphp
                            <small>{{ $currentDesc }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-cog me-1"></i>
        Cấu hình nâng cao
    </h6>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Hướng dẫn cấu hình SMTP</h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="smtpGuideAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gmailGuide">
                                    Gmail SMTP
                                </button>
                            </h2>
                            <div id="gmailGuide" class="accordion-collapse collapse" data-bs-parent="#smtpGuideAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Cấu hình:</h6>
                                            <ul class="small">
                                                <li><strong>Host:</strong> smtp.gmail.com</li>
                                                <li><strong>Port:</strong> 587 (TLS)</li>
                                                <li><strong>Username:</strong> <EMAIL></li>
                                                <li><strong>Password:</strong> App Password</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Cách tạo App Password:</h6>
                                            <ol class="small">
                                                <li>Vào Google Account settings</li>
                                                <li>Bật 2-Step Verification</li>
                                                <li>Tạo App Password cho "Mail"</li>
                                                <li>Sử dụng App Password thay vì mật khẩu thường</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#outlookGuide">
                                    Outlook/Hotmail SMTP
                                </button>
                            </h2>
                            <div id="outlookGuide" class="accordion-collapse collapse" data-bs-parent="#smtpGuideAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Cấu hình:</h6>
                                            <ul class="small">
                                                <li><strong>Host:</strong> smtp-mail.outlook.com</li>
                                                <li><strong>Port:</strong> 587 (TLS)</li>
                                                <li><strong>Username:</strong> <EMAIL></li>
                                                <li><strong>Password:</strong> Mật khẩu email</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Lưu ý:</h6>
                                            <ul class="small">
                                                <li>Có thể cần bật "Less secure app access"</li>
                                                <li>Hoặc sử dụng App Password nếu có 2FA</li>
                                                <li>Kiểm tra spam folder nếu không nhận được email</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#customGuide">
                                    Custom SMTP Server
                                </button>
                            </h2>
                            <div id="customGuide" class="accordion-collapse collapse" data-bs-parent="#smtpGuideAccordion">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Thông tin cần thiết:</h6>
                                            <ul class="small">
                                                <li>SMTP server hostname</li>
                                                <li>Port number (thường 587, 465, hoặc 25)</li>
                                                <li>Authentication method</li>
                                                <li>Username và password</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Kiểm tra:</h6>
                                            <ul class="small">
                                                <li>Firewall có block port không</li>
                                                <li>SSL/TLS certificate hợp lệ</li>
                                                <li>Authentication credentials đúng</li>
                                                <li>Rate limiting của provider</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-chart-pie me-1"></i>
        Thống kê thông báo
    </h6>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h6 class="card-title text-primary">SMTP Status</h6>
                    <p class="card-text">
                        @if(!empty($values['smtp_host']) && !empty($values['smtp_username']))
                            <span class="badge bg-success">Đã cấu hình</span>
                        @else
                            <span class="badge bg-secondary">Chưa cấu hình</span>
                        @endif
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h6 class="card-title text-info">Tần suất gửi</h6>
                    <p class="card-text">
                        <strong>{{ $frequencies[$values['notification_frequency'] ?? 'immediate'] ?? 'Ngay lập tức' }}</strong>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h6 class="card-title text-success">Email hôm nay</h6>
                    <p class="card-text">
                        <strong>0</strong><br>
                        <small class="text-muted">Emails đã gửi</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update frequency description when selection changes
document.getElementById('notification_frequency').addEventListener('change', function() {
    const descriptions = {
        'immediate': 'Gửi email ngay khi có thông báo mới. Phù hợp cho môi trường cần phản hồi nhanh.',
        'hourly': 'Gửi email tổng hợp mỗi giờ. Giảm spam email nhưng vẫn kịp thời.',
        'daily': 'Gửi email tổng hợp hàng ngày lúc 9:00 AM. Phù hợp cho thông báo không khẩn cấp.',
        'weekly': 'Gửi email tổng hợp hàng tuần vào thứ 2. Phù hợp cho báo cáo định kỳ.'
    };
    
    const selectedValue = this.value;
    const description = descriptions[selectedValue] || descriptions['immediate'];
    
    document.getElementById('frequencyDescription').innerHTML = `<small>${description}</small>`;
});

// Clear encrypted fields when focused
document.querySelectorAll('#smtp_username, #smtp_password').forEach(function(input) {
    input.addEventListener('focus', function() {
        if (this.value === '••••••••••••••••') {
            this.value = '';
            this.type = 'text';
        }
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
