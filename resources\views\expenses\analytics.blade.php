@extends('layouts.app')

@section('title', 'Expense Analytics Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Expense Analytics Dashboard
                    </h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm text-dark" id="periodSelector" style="width: auto;">
                            <option value="current_month" {{ $period == 'current_month' ? 'selected' : '' }}>Tháng này</option>
                            <option value="last_month" {{ $period == 'last_month' ? 'selected' : '' }}>Tháng trước</option>
                            <option value="current_quarter" {{ $period == 'current_quarter' ? 'selected' : '' }}>Quý này</option>
                            <option value="current_year" {{ $period == 'current_year' ? 'selected' : '' }}>Năm này</option>
                            <option value="last_30_days" {{ $period == 'last_30_days' ? 'selected' : '' }}>30 ngày qua</option>
                            <option value="last_7_days" {{ $period == 'last_7_days' ? 'selected' : '' }}>7 ngày qua</option>
                        </select>
                        <a href="{{ route('finance.expenses.index') }}" class="btn btn-outline-light btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>
                            Quay lại
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-money-bill-wave fa-2x text-primary mb-2"></i>
                    <h3 class="text-primary">{{ number_format($analytics['spending_overview']['total_spent'], 0) }}</h3>
                    <p class="text-muted mb-1">Tổng Chi phí (VND)</p>
                    <small class="text-{{ $analytics['spending_overview']['vs_previous_period']['percentage_change'] >= 0 ? 'danger' : 'success' }}">
                        {{ $analytics['spending_overview']['vs_previous_period']['percentage_change'] >= 0 ? '+' : '' }}{{ $analytics['spending_overview']['vs_previous_period']['percentage_change'] }}%
                    </small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-receipt fa-2x text-info mb-2"></i>
                    <h3 class="text-info">{{ $analytics['spending_overview']['total_transactions'] }}</h3>
                    <p class="text-muted mb-1">Tổng Giao dịch</p>
                    <small class="text-muted">{{ number_format($analytics['spending_overview']['average_transaction'], 0) }} VND/giao dịch</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning">{{ $analytics['approval_metrics']['total_pending'] }}</h3>
                    <p class="text-muted mb-1">Chờ Duyệt</p>
                    <small class="text-muted">{{ number_format($analytics['spending_overview']['pending_approvals'], 0) }} VND</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                    <h3 class="text-success">{{ $analytics['risk_analysis']['low_risk_expenses'] }}</h3>
                    <p class="text-muted mb-1">Rủi ro Thấp</p>
                    <small class="text-muted">{{ $analytics['efficiency_metrics']['auto_approval_rate'] }}% tự động duyệt</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Category Breakdown -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Phân bố theo Danh mục
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="categoryChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- Trend Analysis -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Xu hướng Chi phí
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="trendChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Analytics -->
    <div class="row mb-4">
        <!-- Category Details -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Chi tiết theo Danh mục
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Danh mục</th>
                                    <th>Số tiền</th>
                                    <th>Giao dịch</th>
                                    <th>% Tổng</th>
                                    <th>Trung bình</th>
                                    <th>Xu hướng</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($analytics['category_breakdown'] as $category => $data)
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">{{ ucfirst($category) }}</span>
                                    </td>
                                    <td class="fw-bold">{{ number_format($data['total_amount'], 0) }} VND</td>
                                    <td>{{ $data['transaction_count'] }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar" style="width: {{ $data['percentage_of_total'] }}%">
                                                {{ number_format($data['percentage_of_total'], 1) }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ number_format($data['average_amount'], 0) }} VND</td>
                                    <td>
                                        @if($data['vs_previous_period']['percentage_change'] > 0)
                                            <span class="text-danger">
                                                <i class="fas fa-arrow-up"></i>
                                                {{ $data['vs_previous_period']['percentage_change'] }}%
                                            </span>
                                        @elseif($data['vs_previous_period']['percentage_change'] < 0)
                                            <span class="text-success">
                                                <i class="fas fa-arrow-down"></i>
                                                {{ abs($data['vs_previous_period']['percentage_change']) }}%
                                            </span>
                                        @else
                                            <span class="text-muted">
                                                <i class="fas fa-minus"></i>
                                                0%
                                            </span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk & Efficiency Metrics -->
        <div class="col-md-4">
            <!-- Risk Analysis -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Phân tích Rủi ro
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-success">
                                <i class="fas fa-shield-alt fa-2x"></i>
                                <h4>{{ $analytics['risk_analysis']['low_risk_expenses'] }}</h4>
                                <small>Thấp</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-warning">
                                <i class="fas fa-shield-alt fa-2x"></i>
                                <h4>{{ $analytics['risk_analysis']['medium_risk_expenses'] }}</h4>
                                <small>Trung bình</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-danger">
                                <i class="fas fa-shield-alt fa-2x"></i>
                                <h4>{{ $analytics['risk_analysis']['high_risk_expenses'] }}</h4>
                                <small>Cao</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <p class="mb-1"><strong>Fraud Score Trung bình:</strong></p>
                        <h4 class="text-primary">{{ $analytics['risk_analysis']['average_fraud_score'] }}/100</h4>
                    </div>
                </div>
            </div>

            <!-- Efficiency Metrics -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Hiệu suất
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Tỷ lệ Tự động Duyệt:</label>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: {{ $analytics['efficiency_metrics']['auto_approval_rate'] }}%">
                                {{ $analytics['efficiency_metrics']['auto_approval_rate'] }}%
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tỷ lệ Duyệt Lần đầu:</label>
                        <div class="progress">
                            <div class="progress-bar bg-primary" style="width: {{ $analytics['efficiency_metrics']['first_time_approval_rate'] }}%">
                                {{ $analytics['efficiency_metrics']['first_time_approval_rate'] }}%
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Độ chính xác Phân loại:</label>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: {{ $analytics['efficiency_metrics']['categorization_accuracy'] }}%">
                                {{ $analytics['efficiency_metrics']['categorization_accuracy'] }}%
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Chất lượng Dữ liệu:</label>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: {{ $analytics['efficiency_metrics']['data_quality_score'] }}%">
                                {{ $analytics['efficiency_metrics']['data_quality_score'] }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alerts & Recommendations -->
    @if(count($analytics['alerts_and_recommendations']) > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        Cảnh báo & Khuyến nghị
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($analytics['alerts_and_recommendations'] as $alert)
                        <div class="col-md-6 mb-3">
                            <div class="alert alert-{{ $alert['priority'] == 'critical' ? 'danger' : ($alert['priority'] == 'high' ? 'warning' : 'info') }}">
                                <h6 class="alert-heading">
                                    <i class="fas fa-{{ $alert['priority'] == 'critical' ? 'exclamation-triangle' : ($alert['priority'] == 'high' ? 'exclamation-circle' : 'info-circle') }} me-2"></i>
                                    {{ $alert['title'] ?? 'Alert' }}
                                </h6>
                                <p class="mb-0">{{ $alert['message'] ?? 'No message' }}</p>
                                @if(isset($alert['action']))
                                    <hr>
                                    <p class="mb-0"><strong>Hành động:</strong> {{ $alert['action'] }}</p>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Budget Tracking -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Theo dõi Ngân sách
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ number_format($analytics['budget_tracking']['estimated_budget'], 0) }}</h4>
                                <p class="text-muted">Ngân sách Ước tính (VND)</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-danger">{{ number_format($analytics['budget_tracking']['total_spent'], 0) }}</h4>
                                <p class="text-muted">Đã Chi tiêu (VND)</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ number_format($analytics['budget_tracking']['remaining_budget'], 0) }}</h4>
                                <p class="text-muted">Còn lại (VND)</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-{{ $analytics['budget_tracking']['utilization_percentage'] > 90 ? 'danger' : ($analytics['budget_tracking']['utilization_percentage'] > 75 ? 'warning' : 'success') }}">
                                    {{ number_format($analytics['budget_tracking']['utilization_percentage'], 1) }}%
                                </h4>
                                <p class="text-muted">Sử dụng Ngân sách</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="progress" style="height: 25px;">
                            <div class="progress-bar bg-{{ $analytics['budget_tracking']['utilization_percentage'] > 90 ? 'danger' : ($analytics['budget_tracking']['utilization_percentage'] > 75 ? 'warning' : 'success') }}" 
                                 style="width: {{ min($analytics['budget_tracking']['utilization_percentage'], 100) }}%">
                                {{ number_format($analytics['budget_tracking']['utilization_percentage'], 1) }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Period selector change
    $('#periodSelector').on('change', function() {
        const period = $(this).val();
        window.location.href = `{{ route('finance.expenses.analytics') }}?period=${period}&team_id={{ $teamId }}`;
    });

    // Category Pie Chart
    const categoryData = @json($analytics['category_breakdown']);
    const categoryLabels = Object.keys(categoryData);
    const categoryAmounts = Object.values(categoryData).map(item => item.total_amount);
    const categoryColors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
        '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
    ];

    new Chart(document.getElementById('categoryChart'), {
        type: 'doughnut',
        data: {
            labels: categoryLabels.map(label => label.charAt(0).toUpperCase() + label.slice(1)),
            datasets: [{
                data: categoryAmounts,
                backgroundColor: categoryColors.slice(0, categoryLabels.length),
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${context.label}: ${value.toLocaleString()} VND (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });

    // Trend Line Chart (placeholder data)
    const trendData = {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [{
            label: 'Chi phí (VND)',
            data: [
                {{ $analytics['spending_overview']['total_spent'] * 0.2 }},
                {{ $analytics['spending_overview']['total_spent'] * 0.3 }},
                {{ $analytics['spending_overview']['total_spent'] * 0.25 }},
                {{ $analytics['spending_overview']['total_spent'] * 0.25 }}
            ],
            borderColor: '#36A2EB',
            backgroundColor: 'rgba(54, 162, 235, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };

    new Chart(document.getElementById('trendChart'), {
        type: 'line',
        data: trendData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' VND';
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8
                }
            }
        }
    });

    // Auto-refresh every 5 minutes
    setInterval(function() {
        window.location.reload();
    }, 300000);
});
</script>
@endpush
@endsection
