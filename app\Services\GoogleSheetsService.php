<?php

namespace App\Services;

use Google\Client;
use Google\Service\Sheets;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class GoogleSheetsService
{
    private $client;
    private $service;

    public function __construct()
    {
        $this->initializeClient();
    }

    private function initializeClient()
    {
        try {
            $this->client = new Client();
            $this->client->setApplicationName('Dropship Manager');
            $this->client->setScopes([Sheets::SPREADSHEETS]);
            $this->client->setAccessType('offline');
            
            // Set credentials from environment or service account file
            if (env('GOOGLE_SERVICE_ACCOUNT_JSON')) {
                $this->client->setAuthConfig(json_decode(env('GOOGLE_SERVICE_ACCOUNT_JSON'), true));
            } elseif (Storage::exists('google-service-account.json')) {
                $this->client->setAuthConfig(Storage::path('google-service-account.json'));
            } else {
                // For demo purposes, we'll use a mock implementation
                Log::warning('Google Sheets credentials not configured. Using mock implementation.');
                return;
            }

            $this->service = new Sheets($this->client);
        } catch (\Exception $e) {
            Log::error('Failed to initialize Google Sheets client: ' . $e->getMessage());
        }
    }

    /**
     * Extract spreadsheet ID from Google Sheets URL
     */
    private function extractSpreadsheetId($url)
    {
        if (preg_match('/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/', $url, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * Sync checkout data to Google Sheets
     */
    public function syncCheckoutData($checkoutUnit)
    {
        try {
            if (!$this->service) {
                return $this->mockSyncCheckoutData($checkoutUnit);
            }

            $spreadsheetId = $this->extractSpreadsheetId($checkoutUnit->google_sheet_url);
            if (!$spreadsheetId) {
                throw new \Exception('Invalid Google Sheets URL');
            }

            // Get checkout orders for this unit
            $orders = $checkoutUnit->checkoutOrders()->with(['checkoutPayments'])->get();

            // Prepare data for sheets
            $values = [
                ['Mã đơn hàng', 'Khách hàng', 'SĐT', 'Địa chỉ', 'Giá trị đơn', 'Phí checkout', 'Tổng tiền', 'Trạng thái', 'Ngày tạo', 'Ghi chú']
            ];

            foreach ($orders as $order) {
                $values[] = [
                    $order->checkout_order_number,
                    $order->customer_name,
                    $order->customer_phone,
                    $order->shipping_address,
                    number_format($order->order_value),
                    number_format($order->checkout_fee),
                    number_format($order->total_amount),
                    $order->status,
                    $order->created_at->format('d/m/Y H:i'),
                    $order->notes ?? ''
                ];
            }

            // Update the sheet
            $range = 'A1:J' . (count($values));
            $body = new \Google\Service\Sheets\ValueRange([
                'values' => $values
            ]);

            $params = [
                'valueInputOption' => 'RAW'
            ];

            $result = $this->service->spreadsheets_values->update(
                $spreadsheetId,
                $range,
                $body,
                $params
            );

            Log::info("Google Sheets sync successful for checkout unit {$checkoutUnit->id}. Updated {$result->getUpdatedCells()} cells.");

            return [
                'success' => true,
                'message' => "Đã đồng bộ {$result->getUpdatedCells()} ô dữ liệu",
                'updated_rows' => count($values) - 1
            ];

        } catch (\Exception $e) {
            Log::error("Google Sheets sync failed for checkout unit {$checkoutUnit->id}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Lỗi đồng bộ: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Import checkout data from Google Sheets
     */
    public function importCheckoutData($checkoutUnit)
    {
        try {
            if (!$this->service) {
                return $this->mockImportCheckoutData($checkoutUnit);
            }

            $spreadsheetId = $this->extractSpreadsheetId($checkoutUnit->google_sheet_url);
            if (!$spreadsheetId) {
                throw new \Exception('Invalid Google Sheets URL');
            }

            // Read data from sheets
            $range = 'A2:J1000'; // Skip header row, read up to 1000 rows
            $response = $this->service->spreadsheets_values->get($spreadsheetId, $range);
            $values = $response->getValues();

            if (empty($values)) {
                return [
                    'success' => true,
                    'message' => 'Không có dữ liệu để import',
                    'imported_count' => 0
                ];
            }

            $importedCount = 0;
            $errors = [];

            foreach ($values as $index => $row) {
                try {
                    // Skip empty rows
                    if (empty(array_filter($row))) {
                        continue;
                    }

                    // Map columns to order data
                    $orderData = [
                        'checkout_unit_id' => $checkoutUnit->id,
                        'external_order_id' => $row[0] ?? null,
                        'customer_name' => $row[1] ?? null,
                        'customer_phone' => $row[2] ?? null,
                        'shipping_address' => $row[3] ?? null,
                        'order_value' => $this->parseNumber($row[4] ?? 0),
                        'checkout_fee' => $this->parseNumber($row[5] ?? 0),
                        'total_amount' => $this->parseNumber($row[6] ?? 0),
                        'status' => $this->mapStatus($row[7] ?? 'pending'),
                        'notes' => $row[9] ?? null
                    ];

                    // Validate required fields
                    if (empty($orderData['customer_name']) || empty($orderData['customer_phone'])) {
                        $errors[] = "Dòng " . ($index + 2) . ": Thiếu thông tin khách hàng";
                        continue;
                    }

                    // Check if order already exists
                    $existingOrder = \App\Models\CheckoutOrder::where('external_order_id', $orderData['external_order_id'])
                                                            ->where('checkout_unit_id', $checkoutUnit->id)
                                                            ->first();

                    if ($existingOrder) {
                        // Update existing order
                        $existingOrder->update($orderData);
                    } else {
                        // Create new order
                        \App\Models\CheckoutOrder::create($orderData);
                    }

                    $importedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Dòng " . ($index + 2) . ": " . $e->getMessage();
                }
            }

            $message = "Đã import {$importedCount} đơn hàng";
            if (!empty($errors)) {
                $message .= ". Có " . count($errors) . " lỗi.";
            }

            Log::info("Google Sheets import completed for checkout unit {$checkoutUnit->id}. Imported: {$importedCount}, Errors: " . count($errors));

            return [
                'success' => true,
                'message' => $message,
                'imported_count' => $importedCount,
                'errors' => $errors
            ];

        } catch (\Exception $e) {
            Log::error("Google Sheets import failed for checkout unit {$checkoutUnit->id}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Lỗi import: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Export financial report to Google Sheets
     */
    public function exportFinancialReport($team, $period)
    {
        try {
            // This would create a new spreadsheet with financial data
            // For now, return mock response
            return $this->mockExportFinancialReport($team, $period);

        } catch (\Exception $e) {
            Log::error("Financial report export failed for team {$team->id}: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Lỗi export báo cáo: ' . $e->getMessage()
            ];
        }
    }

    // Helper methods
    private function parseNumber($value)
    {
        // Remove formatting and convert to number
        $cleaned = preg_replace('/[^\d.]/', '', $value);
        return floatval($cleaned);
    }

    private function mapStatus($status)
    {
        $statusMap = [
            'pending' => 'pending',
            'confirmed' => 'confirmed',
            'processing' => 'processing',
            'shipped' => 'shipped',
            'delivered' => 'delivered',
            'completed' => 'completed',
            'cancelled' => 'cancelled',
            'returned' => 'returned',
            'refunded' => 'refunded'
        ];

        return $statusMap[strtolower($status)] ?? 'pending';
    }

    // Mock implementations for demo
    private function mockSyncCheckoutData($checkoutUnit)
    {
        sleep(1); // Simulate API call delay
        
        $orderCount = $checkoutUnit->checkoutOrders()->count();
        
        return [
            'success' => true,
            'message' => "Đã đồng bộ {$orderCount} đơn hàng (Demo mode)",
            'updated_rows' => $orderCount
        ];
    }

    private function mockImportCheckoutData($checkoutUnit)
    {
        sleep(1); // Simulate API call delay
        
        return [
            'success' => true,
            'message' => 'Import thành công 5 đơn hàng mới (Demo mode)',
            'imported_count' => 5,
            'errors' => []
        ];
    }

    private function mockExportFinancialReport($team, $period)
    {
        sleep(1); // Simulate API call delay
        
        return [
            'success' => true,
            'message' => "Đã export báo cáo tài chính {$period} cho team {$team->name} (Demo mode)",
            'spreadsheet_url' => 'https://docs.google.com/spreadsheets/d/demo-export-' . time()
        ];
    }
}
