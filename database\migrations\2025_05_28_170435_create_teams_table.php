<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teams', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên team
            $table->string('code')->unique(); // Mã team (VD: TEAM_A, TEAM_B)
            $table->text('description')->nullable(); // Mô tả team
            $table->foreignId('leader_id')->nullable()->constrained('users')->onDelete('set null'); // Team leader
            $table->enum('status', ['active', 'inactive'])->default('active'); // Trạng thái
            $table->json('settings')->nullable(); // Cài đặt team (JSON)
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teams');
    }
};
