@extends('layouts.app')

@section('title', 'Chỉnh sửa Chi phí - ' . $expense->expense_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-edit text-primary me-2"></i>
                        Chỉnh sửa Chi phí - {{ $expense->expense_number }}
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="{{ route('finance.expenses.show', $expense) }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-eye me-1"></i>
                            Xem chi tiết
                        </a>
                        <a href="{{ route('finance.expenses.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>
                            Quay lại
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Current Status Alert -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Trạng thái hiện tại:</strong>
                                <span class="badge bg-{{ $expense->status == 'pending' ? 'warning' : ($expense->status == 'approved' ? 'success' : 'secondary') }}">
                                    {{ $expense->status_label }}
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>Duyệt:</strong>
                                <span class="badge bg-{{ $expense->approval_status == 'pending' ? 'warning' : ($expense->approval_status == 'approved' ? 'success' : 'secondary') }}">
                                    {{ ucfirst($expense->approval_status) }}
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>Rủi ro:</strong>
                                <span class="badge bg-{{ $expense->risk_level == 'low' ? 'success' : ($expense->risk_level == 'medium' ? 'warning' : 'danger') }}">
                                    {{ ucfirst($expense->risk_level) }}
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>Phân loại AI:</strong>
                                @if($expense->category_manually_corrected)
                                    <span class="badge bg-warning">Đã sửa thủ công</span>
                                @else
                                    <span class="badge bg-info">{{ $expense->categorization_confidence }}% tin cậy</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <form id="expenseForm" action="{{ route('finance.expenses.update', $expense) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thông tin cơ bản</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="expense_type" class="form-label">Loại chi phí <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('expense_type') is-invalid @enderror" id="expense_type" name="expense_type" required>
                                                        <option value="">Chọn loại chi phí</option>
                                                        <option value="checkout_fee" {{ old('expense_type', $expense->expense_type) == 'checkout_fee' ? 'selected' : '' }}>Tiền checkout</option>
                                                        <option value="advertising" {{ old('expense_type', $expense->expense_type) == 'advertising' ? 'selected' : '' }}>Quảng cáo</option>
                                                        <option value="shipping" {{ old('expense_type', $expense->expense_type) == 'shipping' ? 'selected' : '' }}>Vận chuyển</option>
                                                        <option value="refunds" {{ old('expense_type', $expense->expense_type) == 'refunds' ? 'selected' : '' }}>Hoàn tiền</option>
                                                        <option value="tools_software" {{ old('expense_type', $expense->expense_type) == 'tools_software' ? 'selected' : '' }}>Công cụ/Phần mềm</option>
                                                        <option value="office_supplies" {{ old('expense_type', $expense->expense_type) == 'office_supplies' ? 'selected' : '' }}>Văn phòng phẩm</option>
                                                        <option value="utilities" {{ old('expense_type', $expense->expense_type) == 'utilities' ? 'selected' : '' }}>Tiện ích</option>
                                                        <option value="travel" {{ old('expense_type', $expense->expense_type) == 'travel' ? 'selected' : '' }}>Du lịch</option>
                                                        <option value="meals" {{ old('expense_type', $expense->expense_type) == 'meals' ? 'selected' : '' }}>Ăn uống</option>
                                                        <option value="other" {{ old('expense_type', $expense->expense_type) == 'other' ? 'selected' : '' }}>Khác</option>
                                                    </select>
                                                    @error('expense_type')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="category" class="form-label">Phân loại <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('category') is-invalid @enderror" id="category" name="category" required>
                                                        <option value="">Chọn phân loại</option>
                                                        <option value="advertising" {{ old('category', $expense->category) == 'advertising' ? 'selected' : '' }}>Quảng cáo</option>
                                                        <option value="shipping" {{ old('category', $expense->category) == 'shipping' ? 'selected' : '' }}>Vận chuyển</option>
                                                        <option value="refunds" {{ old('category', $expense->category) == 'refunds' ? 'selected' : '' }}>Hoàn tiền</option>
                                                        <option value="tools_software" {{ old('category', $expense->category) == 'tools_software' ? 'selected' : '' }}>Công cụ/Phần mềm</option>
                                                        <option value="office_supplies" {{ old('category', $expense->category) == 'office_supplies' ? 'selected' : '' }}>Văn phòng phẩm</option>
                                                        <option value="utilities" {{ old('category', $expense->category) == 'utilities' ? 'selected' : '' }}>Tiện ích</option>
                                                        <option value="travel" {{ old('category', $expense->category) == 'travel' ? 'selected' : '' }}>Du lịch</option>
                                                        <option value="meals" {{ old('category', $expense->category) == 'meals' ? 'selected' : '' }}>Ăn uống</option>
                                                        <option value="other" {{ old('category', $expense->category) == 'other' ? 'selected' : '' }}>Khác</option>
                                                    </select>
                                                    @error('category')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    @if($expense->suggested_category && $expense->suggested_category != $expense->category)
                                                        <div class="form-text">
                                                            <small class="text-info">
                                                                <i class="fas fa-robot me-1"></i>
                                                                AI gợi ý: <strong>{{ ucfirst($expense->suggested_category) }}</strong> ({{ $expense->categorization_confidence }}% tin cậy)
                                                                <button type="button" class="btn btn-link btn-sm p-0 ms-1" onclick="selectSuggestedCategory()">
                                                                    Sử dụng gợi ý
                                                                </button>
                                                            </small>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="title" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title', $expense->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">Mô tả <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="3" required>{{ old('description', $expense->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="vendor" class="form-label">Nhà cung cấp/Vendor</label>
                                            <input type="text" class="form-control @error('vendor') is-invalid @enderror" 
                                                   id="vendor" name="vendor" value="{{ old('vendor', $expense->vendor) }}">
                                            @error('vendor')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Amount & Date Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thông tin số tiền & thời gian</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="amount" class="form-label">Số tiền <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                                               id="amount" name="amount" value="{{ old('amount', $expense->amount) }}" 
                                                               step="0.01" min="0" required>
                                                        <select class="form-select @error('currency') is-invalid @enderror" 
                                                                id="currency" name="currency" style="max-width: 100px;">
                                                            <option value="VND" {{ old('currency', $expense->currency) == 'VND' ? 'selected' : '' }}>VND</option>
                                                            <option value="USD" {{ old('currency', $expense->currency) == 'USD' ? 'selected' : '' }}>USD</option>
                                                        </select>
                                                    </div>
                                                    @error('amount')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    @error('currency')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="expense_date" class="form-label">Ngày chi phí <span class="text-danger">*</span></label>
                                                    <input type="date" class="form-control @error('expense_date') is-invalid @enderror" 
                                                           id="expense_date" name="expense_date" value="{{ old('expense_date', $expense->expense_date->format('Y-m-d')) }}" required>
                                                    @error('expense_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="frequency" class="form-label">Tần suất <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('frequency') is-invalid @enderror" id="frequency" name="frequency" required>
                                                        <option value="">Chọn tần suất</option>
                                                        <option value="one_time" {{ old('frequency', $expense->frequency) == 'one_time' ? 'selected' : '' }}>Một lần</option>
                                                        <option value="monthly" {{ old('frequency', $expense->frequency) == 'monthly' ? 'selected' : '' }}>Hàng tháng</option>
                                                        <option value="quarterly" {{ old('frequency', $expense->frequency) == 'quarterly' ? 'selected' : '' }}>Hàng quý</option>
                                                        <option value="yearly" {{ old('frequency', $expense->frequency) == 'yearly' ? 'selected' : '' }}>Hàng năm</option>
                                                    </select>
                                                    @error('frequency')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="checkout_unit_id" class="form-label">Checkout Unit</label>
                                                    <select class="form-select @error('checkout_unit_id') is-invalid @enderror" id="checkout_unit_id" name="checkout_unit_id">
                                                        <option value="">Không liên kết</option>
                                                        @foreach($checkoutUnits as $unit)
                                                            <option value="{{ $unit->id }}" {{ old('checkout_unit_id', $expense->checkout_unit_id) == $unit->id ? 'selected' : '' }}>
                                                                {{ $unit->name }} ({{ $unit->rate }}%)
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('checkout_unit_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thông tin bổ sung</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">Ghi chú</label>
                                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                                      id="notes" name="notes" rows="3">{{ old('notes', $expense->notes) }}</textarea>
                                            @error('notes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="attachments" class="form-label">Đính kèm mới</label>
                                            <input type="file" class="form-control @error('attachments') is-invalid @enderror" 
                                                   id="attachments" name="attachments[]" multiple accept="image/*,.pdf,.doc,.docx,.xls,.xlsx">
                                            @error('attachments')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Hỗ trợ: hình ảnh, PDF, Word, Excel. Tối đa 10MB mỗi file.</div>
                                        </div>

                                        @if($expense->attachments && count($expense->attachments) > 0)
                                        <div class="mb-3">
                                            <label class="form-label">Đính kèm hiện tại:</label>
                                            <div class="row">
                                                @foreach($expense->attachments as $attachment)
                                                <div class="col-md-4 mb-2">
                                                    <div class="card">
                                                        <div class="card-body text-center py-2">
                                                            <i class="fas fa-file fa-lg text-muted"></i>
                                                            <p class="card-text small mb-1">{{ basename($attachment) }}</p>
                                                            <a href="{{ Storage::url($attachment) }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                @endforeach
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Smart Features & Status -->
                            <div class="col-md-4">
                                <!-- Current Analysis -->
                                <div class="card mb-4">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-robot me-2"></i>
                                            Phân tích Hiện tại
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">Phân loại AI:</label>
                                            <div>
                                                <span class="badge bg-primary">{{ ucfirst($expense->suggested_category) }}</span>
                                                <small class="text-muted d-block">Độ tin cậy: {{ $expense->categorization_confidence }}%</small>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Mức độ rủi ro:</label>
                                            <div>
                                                <span class="badge bg-{{ $expense->risk_level == 'low' ? 'success' : ($expense->risk_level == 'medium' ? 'warning' : 'danger') }}">
                                                    {{ ucfirst($expense->risk_level) }}
                                                </span>
                                                @if($expense->fraud_score > 0)
                                                    <small class="text-muted d-block">Fraud Score: {{ $expense->fraud_score }}/100</small>
                                                @endif
                                            </div>
                                        </div>

                                        <button type="button" id="reAnalyzeBtn" class="btn btn-outline-primary btn-sm w-100">
                                            <i class="fas fa-sync me-1"></i>
                                            Phân tích lại
                                        </button>
                                    </div>
                                </div>

                                <!-- Validation Alerts -->
                                <div class="card mb-4">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Kiểm tra Validation
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="validationResults">
                                            @if($expense->validation_results)
                                                @php $validation = $expense->validation_results; @endphp
                                                <div class="alert alert-{{ $validation['overall_status'] == 'passed' ? 'success' : ($validation['overall_status'] == 'warning' ? 'warning' : 'danger') }} py-2">
                                                    <strong>Status:</strong> {{ ucfirst($validation['overall_status']) }}
                                                </div>
                                                
                                                @if(count($validation['flags'] ?? []) > 0)
                                                    @foreach($validation['flags'] as $flag)
                                                        <div class="alert alert-danger py-1 mb-1">
                                                            <small><strong>{{ $flag['type'] }}:</strong> {{ $flag['message'] }}</small>
                                                        </div>
                                                    @endforeach
                                                @endif
                                                
                                                @if(count($validation['warnings'] ?? []) > 0)
                                                    @foreach($validation['warnings'] as $warning)
                                                        <div class="alert alert-warning py-1 mb-1">
                                                            <small><strong>{{ $warning['type'] }}:</strong> {{ $warning['message'] }}</small>
                                                        </div>
                                                    @endforeach
                                                @endif
                                            @else
                                                <div class="text-muted text-center">
                                                    <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                                    <p class="mb-0">Chưa có kết quả validation</p>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thao tác</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-1"></i>
                                                Cập nhật chi phí
                                            </button>
                                            <a href="{{ route('finance.expenses.show', $expense) }}" class="btn btn-outline-info">
                                                <i class="fas fa-eye me-1"></i>
                                                Xem chi tiết
                                            </a>
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                                <i class="fas fa-times me-1"></i>
                                                Hủy bỏ
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Re-analyze when significant fields change
    $('#description, #amount, #vendor, #category').on('change', function() {
        $('#reAnalyzeBtn').removeClass('btn-outline-primary').addClass('btn-warning').html('<i class="fas fa-exclamation-triangle me-1"></i>Cần phân tích lại');
    });
    
    // Re-analyze button
    $('#reAnalyzeBtn').on('click', function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Đang phân tích...');
        
        // This would trigger a re-analysis via AJAX
        // For now, just show a message
        setTimeout(function() {
            btn.prop('disabled', false).removeClass('btn-warning').addClass('btn-outline-primary').html('<i class="fas fa-sync me-1"></i>Phân tích lại');
            showAlert('Tính năng phân tích lại sẽ được triển khai trong phiên bản tiếp theo.', 'info');
        }, 2000);
    });
});

function selectSuggestedCategory() {
    $('#category').val('{{ $expense->suggested_category }}');
    $('#category').trigger('change');
    showAlert('Đã chọn phân loại được AI gợi ý.', 'success');
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
@endpush
@endsection
