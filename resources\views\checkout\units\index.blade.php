@extends('layouts.app')

@section('title', 'Quản lý đơn vị Checkout - Dropship Manager')
@section('page-title', 'Quản lý đơn vị Checkout')

@section('page-actions')
<a href="{{ route('checkout.units.create') }}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    Thêm đơn vị Checkout
</a>
@endsection

@section('content')
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Danh sách đơn vị Checkout</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="{{ route('checkout.units.index') }}" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control form-control-sm" name="search" 
                               placeholder="Tìm kiếm..." value="{{ request('search') }}">
                    </div>
                    <div class="col-auto">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Hoạt động</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Không hoạt động</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="{{ route('checkout.units.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Tên đơn vị</th>
                        <th>Mã đơn vị</th>
                        <th>Tỷ lệ Checkout</th>
                        <th>Tổng đơn hàng</th>
                        <th>Hoàn thành</th>
                        <th>Sự cố</th>
                        <th>Tổng tiền</th>
                        <th>Đã thanh toán</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($checkoutUnits as $unit)
                    <tr>
                        <td>
                            <div>
                                <strong>{{ $unit->name }}</strong>
                                @if($unit->contact_person)
                                    <br><small class="text-muted">{{ $unit->contact_person }}</small>
                                @endif
                            </div>
                        </td>
                        <td><code>{{ $unit->code }}</code></td>
                        <td>
                            <span class="badge bg-info">{{ $unit->checkout_rate }}%</span>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ $unit->checkout_orders_count }}</span>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ $unit->completed_orders }}</span>
                        </td>
                        <td>
                            <span class="badge bg-warning">{{ $unit->problematic_orders }}</span>
                        </td>
                        <td>
                            <strong>{{ number_format($unit->total_checkout_amount) }}đ</strong>
                        </td>
                        <td>
                            <span class="text-success">{{ number_format($unit->total_paid_amount) }}đ</span>
                            @if($unit->pending_payment_amount > 0)
                                <br><small class="text-danger">Còn nợ: {{ number_format($unit->pending_payment_amount) }}đ</small>
                            @endif
                        </td>
                        <td>
                            @if($unit->status == 'active')
                                <span class="badge bg-success">Hoạt động</span>
                            @else
                                <span class="badge bg-secondary">Không hoạt động</span>
                            @endif
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ route('checkout.units.show', $unit) }}" class="btn btn-outline-info" title="Xem">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('checkout.units.edit', $unit) }}" class="btn btn-outline-primary" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @if($unit->google_sheet_url)
                                    <button type="button" class="btn btn-outline-success" title="Đồng bộ Google Sheets"
                                            onclick="syncGoogleSheet({{ $unit->id }})">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                @endif
                                <a href="{{ route('checkout.units.export', $unit) }}" class="btn btn-outline-warning" title="Xuất dữ liệu">
                                    <i class="fas fa-download"></i>
                                </a>
                                <form action="{{ route('checkout.units.destroy', $unit) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger" title="Xóa"
                                            onclick="return confirm('Bạn có chắc chắn muốn xóa đơn vị checkout này?\n\nLưu ý: Chỉ có thể xóa đơn vị không có đơn hàng nào.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>Chưa có đơn vị checkout nào</p>
                                <a href="{{ route('checkout.units.create') }}" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Thêm đơn vị checkout đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    @if($checkoutUnits->hasPages())
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị {{ $checkoutUnits->firstItem() }} - {{ $checkoutUnits->lastItem() }} 
                trong tổng số {{ $checkoutUnits->total() }} đơn vị
            </div>
            <div>
                {{ $checkoutUnits->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ $checkoutUnits->total() }}</h5>
                <p class="card-text">Tổng đơn vị</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ $checkoutUnits->where('status', 'active')->count() }}</h5>
                <p class="card-text">Đang hoạt động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ $checkoutUnits->sum('checkout_orders_count') }}</h5>
                <p class="card-text">Tổng đơn hàng</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ $checkoutUnits->where('checkout_orders_count', 0)->count() }}</h5>
                <p class="card-text">Chưa có đơn hàng</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function syncGoogleSheet(unitId) {
    if (!confirm('Bạn có muốn đồng bộ dữ liệu từ Google Sheets?')) {
        return;
    }

    // Show loading
    const button = event.target.closest('button');
    const originalHtml = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    fetch(`/checkout/units/${unitId}/sync-google-sheet`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Đồng bộ dữ liệu thành công!');
            location.reload();
        } else {
            alert('Lỗi: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi đồng bộ dữ liệu!');
    })
    .finally(() => {
        button.innerHTML = originalHtml;
        button.disabled = false;
    });
}
</script>
@endpush
