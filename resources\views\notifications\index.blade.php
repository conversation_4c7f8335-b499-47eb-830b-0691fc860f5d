@extends('layouts.app')

@section('title', 'Thông báo - Dropship Manager')
@section('page-title', 'Thông báo')

@section('page-actions')
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="markAllAsRead()">
        <i class="fas fa-check-double me-1"></i>
        Đ<PERSON>h dấu tất cả đã đọc
    </button>
    <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
        <i class="fas fa-sync me-1"></i>
        Làm mới
    </button>
</div>
@endsection

@section('content')
<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Tổng thông báo
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-bell fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Chưa đọc
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['unread'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Ưu tiên cao
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['high_priority'] }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('notifications.index') }}" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Trạng thái</label>
                <select name="status" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="unread" {{ request('status') == 'unread' ? 'selected' : '' }}>Chưa đọc</option>
                    <option value="read" {{ request('status') == 'read' ? 'selected' : '' }}>Đã đọc</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Loại</label>
                <select name="type" class="form-select">
                    <option value="">Tất cả loại</option>
                    <option value="payout_created" {{ request('type') == 'payout_created' ? 'selected' : '' }}>Payout mới</option>
                    <option value="expense_approval_needed" {{ request('type') == 'expense_approval_needed' ? 'selected' : '' }}>Cần duyệt chi phí</option>
                    <option value="expense_approved" {{ request('type') == 'expense_approved' ? 'selected' : '' }}>Chi phí đã duyệt</option>
                    <option value="checkout_order_status_changed" {{ request('type') == 'checkout_order_status_changed' ? 'selected' : '' }}>Đơn hàng checkout</option>
                    <option value="team_member_added" {{ request('type') == 'team_member_added' ? 'selected' : '' }}>Thành viên mới</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Độ ưu tiên</label>
                <select name="priority" class="form-select">
                    <option value="">Tất cả</option>
                    <option value="urgent" {{ request('priority') == 'urgent' ? 'selected' : '' }}>Khẩn cấp</option>
                    <option value="high" {{ request('priority') == 'high' ? 'selected' : '' }}>Cao</option>
                    <option value="medium" {{ request('priority') == 'medium' ? 'selected' : '' }}>Trung bình</option>
                    <option value="low" {{ request('priority') == 'low' ? 'selected' : '' }}>Thấp</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i>
                    Lọc
                </button>
                <a href="{{ route('notifications.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    Reset
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Notifications List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">Danh sách thông báo</h5>
    </div>
    <div class="card-body p-0">
        @forelse($notifications as $notification)
        <div class="notification-item border-bottom p-3 {{ $notification->isRead() ? '' : 'bg-light' }}" data-notification-id="{{ $notification->id }}">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-2">
                        <h6 class="mb-0 me-2">{{ $notification->title }}</h6>
                        <span class="badge bg-{{ $notification->priority_color }}">{{ $notification->priority_label }}</span>
                        @if(!$notification->isRead())
                            <span class="badge bg-primary ms-2">Mới</span>
                        @endif
                    </div>
                    <p class="mb-2 text-muted">{{ $notification->message }}</p>
                    <div class="d-flex align-items-center text-muted small">
                        <i class="fas fa-clock me-1"></i>
                        <span>{{ $notification->time_ago }}</span>
                        @if($notification->team)
                            <span class="ms-3">
                                <i class="fas fa-users me-1"></i>
                                {{ $notification->team->name }}
                            </span>
                        @endif
                    </div>
                </div>
                <div class="ms-3">
                    <div class="btn-group btn-group-sm" role="group">
                        @if($notification->action_url)
                            <a href="{{ $notification->action_url }}" class="btn btn-outline-primary" title="Xem chi tiết">
                                <i class="fas fa-eye"></i>
                            </a>
                        @endif
                        @if(!$notification->isRead())
                            <button type="button" class="btn btn-outline-success" onclick="markAsRead({{ $notification->id }})" title="Đánh dấu đã đọc">
                                <i class="fas fa-check"></i>
                            </button>
                        @endif
                        <form action="{{ route('notifications.destroy', $notification) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger" title="Xóa" onclick="return confirm('Bạn có chắc chắn muốn xóa thông báo này?')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Không có thông báo nào</h5>
            <p class="text-muted">Bạn sẽ nhận được thông báo khi có hoạt động mới trong hệ thống.</p>
        </div>
        @endforelse
    </div>
    
    @if($notifications->hasPages())
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị {{ $notifications->firstItem() }} - {{ $notifications->lastItem() }} 
                trong tổng số {{ $notifications->total() }} thông báo
            </div>
            <div>
                {{ $notifications->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('styles')
<style>
.notification-item {
    transition: all 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa !important;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
</style>
@endpush

@push('scripts')
<script>
function markAsRead(notificationId) {
    $.post(`/notifications/${notificationId}/mark-as-read`, function(data) {
        if (data.success) {
            // Update UI
            const notificationItem = $(`[data-notification-id="${notificationId}"]`);
            notificationItem.removeClass('bg-light');
            notificationItem.find('.badge:contains("Mới")').remove();
            notificationItem.find('.btn-outline-success').remove();
            
            // Update notification count in header
            updateNotificationCount(data.unread_count);
            
            // Show success message
            showAlert('success', 'Thông báo đã được đánh dấu là đã đọc.');
        } else {
            showAlert('error', 'Có lỗi xảy ra khi đánh dấu thông báo.');
        }
    }).fail(function() {
        showAlert('error', 'Có lỗi xảy ra khi đánh dấu thông báo.');
    });
}

function markAllAsRead() {
    if (!confirm('Bạn có chắc chắn muốn đánh dấu tất cả thông báo là đã đọc?')) {
        return;
    }
    
    $.post('/notifications/mark-all-as-read', function(data) {
        if (data.success) {
            // Reload page to update UI
            location.reload();
        } else {
            showAlert('error', 'Có lỗi xảy ra khi đánh dấu thông báo.');
        }
    }).fail(function() {
        showAlert('error', 'Có lỗi xảy ra khi đánh dấu thông báo.');
    });
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert alert at the top of the content
    $('.container-fluid').prepend(alertHtml);
    
    // Auto hide after 3 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}

function updateNotificationCount(count) {
    const badge = $('#notification-count');
    if (count > 0) {
        badge.text(count).show();
    } else {
        badge.hide();
    }
}
</script>
@endpush
