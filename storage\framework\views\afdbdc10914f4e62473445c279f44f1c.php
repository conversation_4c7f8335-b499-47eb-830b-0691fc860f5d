<?php $__env->startSection('title', 'Quản lý Teams - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Quản lý Teams'); ?>

<?php $__env->startSection('page-actions'); ?>
<a href="<?php echo e(route('finance.teams.create')); ?>" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>
    Tạo Team mới
</a>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">Danh sách Teams</h5>
            </div>
            <div class="col-auto">
                <!-- Search and Filter Form -->
                <form method="GET" action="<?php echo e(route('finance.teams.index')); ?>" class="row g-3">
                    <div class="col-auto">
                        <input type="text" class="form-control form-control-sm" name="search" 
                               placeholder="Tìm kiếm..." value="<?php echo e(request('search')); ?>">
                    </div>
                    <div class="col-auto">
                        <select name="status" class="form-select form-select-sm">
                            <option value="">Tất cả trạng thái</option>
                            <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Hoạt động</option>
                            <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Không hoạt động</option>
                        </select>
                    </div>
                    <div class="col-auto">
                        <button type="submit" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-search"></i>
                        </button>
                        <a href="<?php echo e(route('finance.teams.index')); ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Leader</th>
                        <th>Members</th>
                        <th>eBay Accounts</th>
                        <th>Tài chính</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $teams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td>
                            <div>
                                <strong><?php echo e($team->name); ?></strong>
                                <br><code class="small"><?php echo e($team->code); ?></code>
                                <?php if($team->description): ?>
                                    <br><small class="text-muted"><?php echo e(Str::limit($team->description, 50)); ?></small>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php if($team->leader): ?>
                                <div>
                                    <strong><?php echo e($team->leader->name); ?></strong>
                                    <br><small class="text-muted"><?php echo e($team->leader->email); ?></small>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">Chưa có leader</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <span class="badge bg-primary mb-1"><?php echo e($team->member_stats['total']); ?> tổng</span>
                                <small class="text-muted">
                                    <?php echo e($team->member_stats['sellers']); ?> sellers, 
                                    <?php echo e($team->member_stats['users']); ?> users
                                </small>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <span class="badge bg-success mb-1"><?php echo e($team->member_stats['active_accounts']); ?> active</span>
                                <small class="text-muted"><?php echo e($team->member_stats['total_accounts']); ?> tổng</small>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-column">
                                <small class="text-success">Payout: <?php echo e(number_format($team->total_payouts)); ?>đ</small>
                                <small class="text-danger">Chi phí: <?php echo e(number_format($team->total_expenses)); ?>đ</small>
                                <small class="text-<?php echo e($team->profit >= 0 ? 'primary' : 'warning'); ?>">
                                    <strong>Lợi nhuận: <?php echo e(number_format($team->profit)); ?>đ</strong>
                                </small>
                            </div>
                        </td>
                        <td>
                            <?php if($team->status == 'active'): ?>
                                <span class="badge bg-success">Hoạt động</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Không hoạt động</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?php echo e(route('finance.teams.show', $team)); ?>" class="btn btn-outline-info" title="Xem">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('finance.teams.edit', $team)); ?>" class="btn btn-outline-primary" title="Sửa">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="<?php echo e(route('finance.teams.members', $team)); ?>" class="btn btn-outline-success" title="Quản lý Members">
                                    <i class="fas fa-users"></i>
                                </a>
                                <form action="<?php echo e(route('finance.teams.destroy', $team)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-outline-danger" title="Xóa"
                                            onclick="return confirm('Bạn có chắc chắn muốn xóa team này?\n\nLưu ý: Chỉ có thể xóa team không có members và dữ liệu tài chính.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p>Chưa có team nào</p>
                                <a href="<?php echo e(route('finance.teams.create')); ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-1"></i>
                                    Tạo team đầu tiên
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <?php if($teams->hasPages()): ?>
    <div class="card-footer">
        <div class="d-flex justify-content-between align-items-center">
            <div class="text-muted">
                Hiển thị <?php echo e($teams->firstItem()); ?> - <?php echo e($teams->lastItem()); ?> 
                trong tổng số <?php echo e($teams->total()); ?> teams
            </div>
            <div>
                <?php echo e($teams->appends(request()->query())->links()); ?>

            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary"><?php echo e($teams->total()); ?></h5>
                <p class="card-text">Tổng Teams</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success"><?php echo e($teams->where('status', 'active')->count()); ?></h5>
                <p class="card-text">Đang hoạt động</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info"><?php echo e($teams->sum(function($team) { return $team->member_stats['total']; })); ?></h5>
                <p class="card-text">Tổng Members</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning"><?php echo e($teams->sum(function($team) { return $team->member_stats['total_accounts']; })); ?></h5>
                <p class="card-text">Tổng eBay Accounts</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/finance/teams/index.blade.php ENDPATH**/ ?>