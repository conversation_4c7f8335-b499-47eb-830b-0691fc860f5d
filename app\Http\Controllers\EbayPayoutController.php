<?php

namespace App\Http\Controllers;

use App\Models\EbayAccount;
use App\Models\EbayPayout;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EbayPayoutController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $query = EbayPayout::with(['ebayAccount', 'team', 'enteredBy']);

        // Phân quyền: Seller chỉ thấy payout của account mình
        if ($user->isSeller()) {
            $query->whereHas('ebayAccount', function($q) use ($user) {
                $q->where('seller_id', $user->id);
            });
        } elseif ($user->isTeamLeader()) {
            $query->where('team_id', $user->team_id);
        } elseif (!$user->isAdmin()) {
            $query->where('team_id', $user->team_id);
        }

        // Tìm kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('payout_number', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%")
                  ->orWhereHas('ebayAccount', function($subQ) use ($search) {
                      $subQ->where('account_name', 'like', "%{$search}%");
                  });
            });
        }

        // Lọc theo team (chỉ admin)
        if ($user->isAdmin() && $request->filled('team_id')) {
            $query->where('team_id', $request->team_id);
        }

        // Lọc theo account
        if ($request->filled('account_id')) {
            $query->where('ebay_account_id', $request->account_id);
        }

        // Lọc theo status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Lọc theo tháng
        if ($request->filled('month')) {
            $query->whereMonth('payout_date', date('m', strtotime($request->month)))
                  ->whereYear('payout_date', date('Y', strtotime($request->month)));
        }

        // Sắp xếp
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $payouts = $query->paginate(20);

        // Danh sách accounts để filter
        $accountsQuery = EbayAccount::query();
        if ($user->isSeller()) {
            $accountsQuery->where('seller_id', $user->id);
        } elseif (!$user->isAdmin()) {
            $accountsQuery->where('team_id', $user->team_id);
        }
        $accounts = $accountsQuery->get();

        return view('finance.payouts.index', compact('payouts', 'accounts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = Auth::user();

        // Lấy danh sách accounts theo quyền
        $accountsQuery = EbayAccount::active();
        if ($user->isSeller()) {
            $accountsQuery->where('seller_id', $user->id);
        } elseif (!$user->isAdmin()) {
            $accountsQuery->where('team_id', $user->team_id);
        }

        $accounts = $accountsQuery->get();

        return view('finance.payouts.create', compact('accounts'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'ebay_account_id' => 'required|exists:ebay_accounts,id',
            'amount' => 'required|numeric|min:0',
            'payout_date' => 'required|date',
            'received_date' => 'nullable|date',
            'currency' => 'required|string|max:3',
            'exchange_rate' => 'nullable|numeric|min:0',
            'status' => 'required|in:pending,received,processing,completed',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        // Kiểm tra quyền tạo payout cho account này
        $account = EbayAccount::findOrFail($validated['ebay_account_id']);

        if ($user->isSeller() && $account->seller_id !== $user->id) {
            abort(403, 'Bạn không có quyền tạo payout cho account này.');
        } elseif (!$user->isAdmin() && $account->team_id !== $user->team_id) {
            abort(403, 'Bạn không có quyền tạo payout cho account này.');
        }

        $validated['team_id'] = $account->team_id;
        $validated['entered_by'] = $user->id;

        $payout = EbayPayout::create($validated);

        // Cập nhật last_payout_date và total_payouts của account
        $account->update([
            'last_payout_date' => $validated['payout_date'],
            'total_payouts' => $account->total_payouts + $validated['amount']
        ]);

        return redirect()->route('finance.payouts.index')
            ->with('success', 'Payout đã được tạo thành công!');
    }

    /**
     * Display the specified resource.
     */
    public function show(EbayPayout $payout)
    {
        $user = Auth::user();

        // Kiểm tra quyền xem
        if ($user->isSeller() && $payout->ebayAccount->seller_id !== $user->id) {
            abort(403, 'Bạn không có quyền xem payout này.');
        } elseif (!$user->isAdmin() && $payout->team_id !== $user->team_id) {
            abort(403, 'Bạn không có quyền xem payout này.');
        }

        $payout->load(['ebayAccount', 'team', 'enteredBy']);

        return view('finance.payouts.show', compact('payout'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EbayPayout $payout)
    {
        $user = Auth::user();

        // Kiểm tra quyền chỉnh sửa
        if (!$payout->can_edit) {
            abort(403, 'Payout này không thể chỉnh sửa.');
        }

        if ($user->isSeller() && $payout->ebayAccount->seller_id !== $user->id) {
            abort(403, 'Bạn không có quyền chỉnh sửa payout này.');
        } elseif (!$user->isAdmin() && $payout->team_id !== $user->team_id) {
            abort(403, 'Bạn không có quyền chỉnh sửa payout này.');
        }

        $accountsQuery = EbayAccount::active();
        if ($user->isSeller()) {
            $accountsQuery->where('seller_id', $user->id);
        } elseif (!$user->isAdmin()) {
            $accountsQuery->where('team_id', $user->team_id);
        }

        $accounts = $accountsQuery->get();

        return view('finance.payouts.edit', compact('payout', 'accounts'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EbayPayout $payout)
    {
        $user = Auth::user();

        // Kiểm tra quyền cập nhật
        if (!$payout->can_edit) {
            abort(403, 'Payout này không thể chỉnh sửa.');
        }

        if ($user->isSeller() && $payout->ebayAccount->seller_id !== $user->id) {
            abort(403, 'Bạn không có quyền cập nhật payout này.');
        } elseif (!$user->isAdmin() && $payout->team_id !== $user->team_id) {
            abort(403, 'Bạn không có quyền cập nhật payout này.');
        }

        $validated = $request->validate([
            'ebay_account_id' => 'required|exists:ebay_accounts,id',
            'amount' => 'required|numeric|min:0',
            'payout_date' => 'required|date',
            'received_date' => 'nullable|date',
            'currency' => 'required|string|max:3',
            'exchange_rate' => 'nullable|numeric|min:0',
            'status' => 'required|in:pending,received,processing,completed',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
        ]);

        $payout->update($validated);

        return redirect()->route('finance.payouts.index')
            ->with('success', 'Payout đã được cập nhật thành công!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EbayPayout $payout)
    {
        $user = Auth::user();

        // Kiểm tra quyền xóa
        if (!$payout->can_delete) {
            abort(403, 'Payout này không thể xóa.');
        }

        if ($user->isSeller() && $payout->ebayAccount->seller_id !== $user->id) {
            abort(403, 'Bạn không có quyền xóa payout này.');
        } elseif (!$user->isAdmin() && $payout->team_id !== $user->team_id) {
            abort(403, 'Bạn không có quyền xóa payout này.');
        }

        $payout->delete();

        return redirect()->route('finance.payouts.index')
            ->with('success', 'Payout đã được xóa thành công!');
    }

    /**
     * Quick payout form for sellers
     */
    public function quickCreate()
    {
        $user = Auth::user();

        if (!$user->isSeller()) {
            abort(403, 'Chỉ seller mới có thể sử dụng form nhập nhanh.');
        }

        $accounts = EbayAccount::where('seller_id', $user->id)->active()->get();

        return view('finance.payouts.quick-create', compact('accounts'));
    }

    /**
     * Store quick payout
     */
    public function quickStore(Request $request)
    {
        $user = Auth::user();

        if (!$user->isSeller()) {
            abort(403, 'Chỉ seller mới có thể sử dụng form nhập nhanh.');
        }

        $validated = $request->validate([
            'ebay_account_id' => 'required|exists:ebay_accounts,id',
            'amount' => 'required|numeric|min:0',
            'payout_date' => 'required|date',
            'currency' => 'required|string|max:3',
            'exchange_rate' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
        ]);

        // Kiểm tra account thuộc về seller
        $account = EbayAccount::findOrFail($validated['ebay_account_id']);
        if ($account->seller_id !== $user->id) {
            abort(403, 'Bạn không có quyền tạo payout cho account này.');
        }

        $validated['team_id'] = $account->team_id;
        $validated['entered_by'] = $user->id;
        $validated['status'] = 'pending'; // Mặc định là pending

        EbayPayout::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Payout đã được tạo thành công!'
        ]);
    }
}
