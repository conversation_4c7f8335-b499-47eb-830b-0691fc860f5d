@extends('layouts.app')

@section('title', 'Quản lý Chi phí')

@section('content')
<div class="container-fluid">
    <!-- Analytics Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Tổng quan Chi phí - {{ $analytics['spending_overview']['vs_previous_period']['trend'] == 'increasing' ? '📈' : ($analytics['spending_overview']['vs_previous_period']['trend'] == 'decreasing' ? '📉' : '➡️') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-primary mb-1">{{ number_format($analytics['spending_overview']['total_spent'], 0) }} VND</h3>
                                <p class="text-muted mb-0">Tổng chi phí tháng này</p>
                                <small class="text-{{ $analytics['spending_overview']['vs_previous_period']['percentage_change'] >= 0 ? 'danger' : 'success' }}">
                                    {{ $analytics['spending_overview']['vs_previous_period']['percentage_change'] >= 0 ? '+' : '' }}{{ $analytics['spending_overview']['vs_previous_period']['percentage_change'] }}% so với tháng trước
                                </small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-info mb-1">{{ $analytics['spending_overview']['total_transactions'] }}</h3>
                                <p class="text-muted mb-0">Tổng giao dịch</p>
                                <small class="text-muted">{{ number_format($analytics['spending_overview']['average_transaction'], 0) }} VND/giao dịch</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-warning mb-1">{{ number_format($analytics['spending_overview']['pending_approvals'], 0) }} VND</h3>
                                <p class="text-muted mb-0">Chờ duyệt</p>
                                <small class="text-muted">{{ $analytics['approval_metrics']['total_pending'] }} giao dịch</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h3 class="text-success mb-1">{{ $analytics['approval_metrics']['approval_rate'] }}%</h3>
                                <p class="text-muted mb-0">Tỷ lệ duyệt</p>
                                <small class="text-muted">{{ $analytics['approval_metrics']['auto_approved'] }} tự động duyệt</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt text-primary me-2"></i>
                        Danh sách Chi phí
                    </h5>
                    <div class="d-flex gap-2">
                        <a href="{{ route('finance.expenses.analytics') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-chart-bar me-1"></i>
                            Analytics
                        </a>
                        <a href="{{ route('finance.expenses.export') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </a>
                        <a href="{{ route('finance.expenses.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            Tạo mới
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="GET" action="{{ route('finance.expenses.index') }}" class="row g-3">
                                <div class="col-md-2">
                                    <label for="status" class="form-label">Trạng thái</label>
                                    <select class="form-select form-select-sm" id="status" name="status">
                                        <option value="">Tất cả</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ duyệt</option>
                                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Đã duyệt</option>
                                        <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Đã thanh toán</option>
                                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Đã hủy</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="approval_status" class="form-label">Duyệt</label>
                                    <select class="form-select form-select-sm" id="approval_status" name="approval_status">
                                        <option value="">Tất cả</option>
                                        <option value="pending" {{ request('approval_status') == 'pending' ? 'selected' : '' }}>Chờ duyệt</option>
                                        <option value="approved" {{ request('approval_status') == 'approved' ? 'selected' : '' }}>Đã duyệt</option>
                                        <option value="auto_approved" {{ request('approval_status') == 'auto_approved' ? 'selected' : '' }}>Tự động duyệt</option>
                                        <option value="rejected" {{ request('approval_status') == 'rejected' ? 'selected' : '' }}>Từ chối</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="category" class="form-label">Phân loại</label>
                                    <select class="form-select form-select-sm" id="category" name="category">
                                        <option value="">Tất cả</option>
                                        <option value="advertising" {{ request('category') == 'advertising' ? 'selected' : '' }}>Quảng cáo</option>
                                        <option value="shipping" {{ request('category') == 'shipping' ? 'selected' : '' }}>Vận chuyển</option>
                                        <option value="refunds" {{ request('category') == 'refunds' ? 'selected' : '' }}>Hoàn tiền</option>
                                        <option value="tools_software" {{ request('category') == 'tools_software' ? 'selected' : '' }}>Công cụ/Phần mềm</option>
                                        <option value="office_supplies" {{ request('category') == 'office_supplies' ? 'selected' : '' }}>Văn phòng phẩm</option>
                                        <option value="utilities" {{ request('category') == 'utilities' ? 'selected' : '' }}>Tiện ích</option>
                                        <option value="travel" {{ request('category') == 'travel' ? 'selected' : '' }}>Du lịch</option>
                                        <option value="meals" {{ request('category') == 'meals' ? 'selected' : '' }}>Ăn uống</option>
                                        <option value="other" {{ request('category') == 'other' ? 'selected' : '' }}>Khác</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="risk_level" class="form-label">Mức độ rủi ro</label>
                                    <select class="form-select form-select-sm" id="risk_level" name="risk_level">
                                        <option value="">Tất cả</option>
                                        <option value="low" {{ request('risk_level') == 'low' ? 'selected' : '' }}>Thấp</option>
                                        <option value="medium" {{ request('risk_level') == 'medium' ? 'selected' : '' }}>Trung bình</option>
                                        <option value="high" {{ request('risk_level') == 'high' ? 'selected' : '' }}>Cao</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="month" class="form-label">Tháng</label>
                                    <input type="month" class="form-control form-control-sm" id="month" name="month" value="{{ request('month', date('Y-m')) }}">
                                </div>
                                <div class="col-md-2">
                                    <label for="search" class="form-label">Tìm kiếm</label>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control" id="search" name="search" value="{{ request('search') }}" placeholder="Tìm kiếm...">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    @if(auth()->user()->hasRole(['admin', 'finance_manager', 'team_leader']))
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="bulkApproveBtn">
                                    <i class="fas fa-check-double me-1"></i>
                                    Duyệt hàng loạt (Rủi ro thấp)
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="window.location.reload()">
                                    <i class="fas fa-sync me-1"></i>
                                    Làm mới
                                </button>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Expenses Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Mã số</th>
                                    <th>Tiêu đề</th>
                                    <th>Phân loại</th>
                                    <th>Số tiền</th>
                                    <th>Trạng thái</th>
                                    <th>Duyệt</th>
                                    <th>Rủi ro</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($expenses as $expense)
                                <tr>
                                    <td>
                                        <a href="{{ route('finance.expenses.show', $expense) }}" class="text-decoration-none">
                                            {{ $expense->expense_number }}
                                        </a>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $expense->title }}</strong>
                                            @if($expense->vendor)
                                                <br><small class="text-muted">{{ $expense->vendor }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ ucfirst($expense->category) }}</span>
                                        @if($expense->category_manually_corrected)
                                            <br><small class="text-warning"><i class="fas fa-edit"></i> Đã sửa</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ number_format($expense->amount, 0) }} {{ $expense->currency }}</strong>
                                    </td>
                                    <td>
                                        @php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'paid' => 'primary',
                                                'cancelled' => 'danger'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $statusColors[$expense->status] ?? 'secondary' }}">
                                            {{ $expense->status_label }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $approvalColors = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'auto_approved' => 'info',
                                                'rejected' => 'danger'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $approvalColors[$expense->approval_status] ?? 'secondary' }}">
                                            {{ ucfirst($expense->approval_status) }}
                                        </span>
                                    </td>
                                    <td>
                                        @php
                                            $riskColors = [
                                                'low' => 'success',
                                                'medium' => 'warning',
                                                'high' => 'danger'
                                            ];
                                        @endphp
                                        <span class="badge bg-{{ $riskColors[$expense->risk_level] ?? 'secondary' }}">
                                            {{ ucfirst($expense->risk_level) }}
                                        </span>
                                        @if($expense->fraud_score > 0)
                                            <br><small class="text-muted">Score: {{ $expense->fraud_score }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $expense->created_at->format('d/m/Y') }}</div>
                                        <small class="text-muted">{{ $expense->created_at->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ route('finance.expenses.show', $expense) }}" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($expense->can_edit)
                                                <a href="{{ route('finance.expenses.edit', $expense) }}" class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif
                                            @if($expense->can_approve && auth()->user()->hasRole(['admin', 'finance_manager', 'team_leader']))
                                                <button type="button" class="btn btn-outline-success btn-sm" onclick="showApprovalModal({{ $expense->id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-inbox fa-3x mb-3"></i>
                                            <p>Không có chi phí nào được tìm thấy.</p>
                                            <a href="{{ route('finance.expenses.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i>
                                                Tạo chi phí đầu tiên
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($expenses->hasPages())
                        <div class="d-flex justify-content-center">
                            {{ $expenses->appends(request()->query())->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xử lý duyệt chi phí</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="approvalForm" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="decision" class="form-label">Quyết định</label>
                        <select class="form-select" id="decision" name="decision" required>
                            <option value="">Chọn quyết định</option>
                            <option value="approved">Duyệt</option>
                            <option value="rejected">Từ chối</option>
                            <option value="request_changes">Yêu cầu chỉnh sửa</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="comments" class="form-label">Ghi chú</label>
                        <textarea class="form-control" id="comments" name="comments" rows="3" placeholder="Nhập ghi chú (tùy chọn)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Xác nhận</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Bulk approve functionality
    $('#bulkApproveBtn').on('click', function() {
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Đang xử lý...');
        
        $.ajax({
            url: '{{ route("finance.expenses.bulk-approve") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    showAlert(`Đã xử lý ${response.processed} chi phí rủi ro thấp`, 'success');
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    showAlert('Có lỗi xảy ra khi xử lý', 'danger');
                }
            },
            error: function() {
                showAlert('Có lỗi xảy ra khi xử lý', 'danger');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check-double me-1"></i>Duyệt hàng loạt (Rủi ro thấp)');
            }
        });
    });
});

function showApprovalModal(expenseId) {
    $('#approvalForm').attr('action', `/finance/expenses/${expenseId}/process-approval`);
    $('#approvalModal').modal('show');
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
@endpush
@endsection
