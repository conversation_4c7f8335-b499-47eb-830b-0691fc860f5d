@extends('layouts.app')

@section('title', 'Tạo Chi phí mới')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle text-primary me-2"></i>
                        Tạo Chi phí mới
                    </h5>
                    <a href="{{ route('finance.expenses.index') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>
                        Quay lại
                    </a>
                </div>

                <div class="card-body">
                    <form id="expenseForm" action="{{ route('finance.expenses.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thông tin cơ bản</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="team_id" class="form-label">Team <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('team_id') is-invalid @enderror" id="team_id" name="team_id" required>
                                                        <option value="">Chọn team</option>
                                                        @foreach($teams as $team)
                                                            <option value="{{ $team->id }}" {{ old('team_id', auth()->user()->team_id) == $team->id ? 'selected' : '' }}>
                                                                {{ $team->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('team_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="expense_type" class="form-label">Loại chi phí <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('expense_type') is-invalid @enderror" id="expense_type" name="expense_type" required>
                                                        <option value="">Chọn loại chi phí</option>
                                                        <option value="checkout_fee" {{ old('expense_type') == 'checkout_fee' ? 'selected' : '' }}>Tiền checkout</option>
                                                        <option value="advertising" {{ old('expense_type') == 'advertising' ? 'selected' : '' }}>Quảng cáo</option>
                                                        <option value="shipping" {{ old('expense_type') == 'shipping' ? 'selected' : '' }}>Vận chuyển</option>
                                                        <option value="refunds" {{ old('expense_type') == 'refunds' ? 'selected' : '' }}>Hoàn tiền</option>
                                                        <option value="tools_software" {{ old('expense_type') == 'tools_software' ? 'selected' : '' }}>Công cụ/Phần mềm</option>
                                                        <option value="office_supplies" {{ old('expense_type') == 'office_supplies' ? 'selected' : '' }}>Văn phòng phẩm</option>
                                                        <option value="utilities" {{ old('expense_type') == 'utilities' ? 'selected' : '' }}>Tiện ích</option>
                                                        <option value="travel" {{ old('expense_type') == 'travel' ? 'selected' : '' }}>Du lịch</option>
                                                        <option value="meals" {{ old('expense_type') == 'meals' ? 'selected' : '' }}>Ăn uống</option>
                                                        <option value="other" {{ old('expense_type') == 'other' ? 'selected' : '' }}>Khác</option>
                                                    </select>
                                                    @error('expense_type')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="title" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                                   id="title" name="title" value="{{ old('title') }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">Mô tả <span class="text-danger">*</span></label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Mô tả chi tiết về chi phí này</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="vendor" class="form-label">Nhà cung cấp/Vendor</label>
                                            <input type="text" class="form-control @error('vendor') is-invalid @enderror" 
                                                   id="vendor" name="vendor" value="{{ old('vendor') }}">
                                            @error('vendor')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Tên công ty hoặc người bán</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Amount & Date Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thông tin số tiền & thời gian</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="amount" class="form-label">Số tiền <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control @error('amount') is-invalid @enderror" 
                                                               id="amount" name="amount" value="{{ old('amount') }}" 
                                                               step="0.01" min="0" required>
                                                        <select class="form-select @error('currency') is-invalid @enderror" 
                                                                id="currency" name="currency" style="max-width: 100px;">
                                                            <option value="VND" {{ old('currency', 'VND') == 'VND' ? 'selected' : '' }}>VND</option>
                                                            <option value="USD" {{ old('currency') == 'USD' ? 'selected' : '' }}>USD</option>
                                                        </select>
                                                    </div>
                                                    @error('amount')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                    @error('currency')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="expense_date" class="form-label">Ngày chi phí <span class="text-danger">*</span></label>
                                                    <input type="date" class="form-control @error('expense_date') is-invalid @enderror" 
                                                           id="expense_date" name="expense_date" value="{{ old('expense_date', date('Y-m-d')) }}" required>
                                                    @error('expense_date')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="frequency" class="form-label">Tần suất <span class="text-danger">*</span></label>
                                                    <select class="form-select @error('frequency') is-invalid @enderror" id="frequency" name="frequency" required>
                                                        <option value="">Chọn tần suất</option>
                                                        <option value="one_time" {{ old('frequency', 'one_time') == 'one_time' ? 'selected' : '' }}>Một lần</option>
                                                        <option value="monthly" {{ old('frequency') == 'monthly' ? 'selected' : '' }}>Hàng tháng</option>
                                                        <option value="quarterly" {{ old('frequency') == 'quarterly' ? 'selected' : '' }}>Hàng quý</option>
                                                        <option value="yearly" {{ old('frequency') == 'yearly' ? 'selected' : '' }}>Hàng năm</option>
                                                    </select>
                                                    @error('frequency')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="checkout_unit_id" class="form-label">Checkout Unit</label>
                                                    <select class="form-select @error('checkout_unit_id') is-invalid @enderror" id="checkout_unit_id" name="checkout_unit_id">
                                                        <option value="">Không liên kết</option>
                                                        @foreach($checkoutUnits as $unit)
                                                            <option value="{{ $unit->id }}" {{ old('checkout_unit_id') == $unit->id ? 'selected' : '' }}>
                                                                {{ $unit->name }} ({{ $unit->rate }}%)
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    @error('checkout_unit_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Information -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thông tin bổ sung</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">Ghi chú</label>
                                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                                      id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                            @error('notes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mb-3">
                                            <label for="attachments" class="form-label">Đính kèm</label>
                                            <input type="file" class="form-control @error('attachments') is-invalid @enderror" 
                                                   id="attachments" name="attachments[]" multiple accept="image/*,.pdf,.doc,.docx,.xls,.xlsx">
                                            @error('attachments')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Hỗ trợ: hình ảnh, PDF, Word, Excel. Tối đa 10MB mỗi file.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Smart Features -->
                            <div class="col-md-4">
                                <!-- Smart Categorization -->
                                <div class="card mb-4">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-robot me-2"></i>
                                            Smart Categorization
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="categoryPrediction" class="d-none">
                                            <div class="alert alert-info mb-3">
                                                <strong>Gợi ý phân loại:</strong>
                                                <div id="suggestedCategory" class="mt-2"></div>
                                                <div id="confidenceScore" class="mt-1"></div>
                                            </div>
                                            <div id="alternativeCategories" class="mb-3"></div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="category" class="form-label">Phân loại <span class="text-danger">*</span></label>
                                            <select class="form-select @error('category') is-invalid @enderror" id="category" name="category" required>
                                                <option value="">Chọn phân loại</option>
                                                <option value="advertising">Quảng cáo</option>
                                                <option value="shipping">Vận chuyển</option>
                                                <option value="refunds">Hoàn tiền</option>
                                                <option value="tools_software">Công cụ/Phần mềm</option>
                                                <option value="office_supplies">Văn phòng phẩm</option>
                                                <option value="utilities">Tiện ích</option>
                                                <option value="travel">Du lịch</option>
                                                <option value="meals">Ăn uống</option>
                                                <option value="other">Khác</option>
                                            </select>
                                            @error('category')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <button type="button" id="suggestCategoryBtn" class="btn btn-outline-primary btn-sm w-100">
                                            <i class="fas fa-magic me-1"></i>
                                            Gợi ý phân loại
                                        </button>
                                    </div>
                                </div>

                                <!-- Validation Alerts -->
                                <div class="card mb-4">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Kiểm tra tự động
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="validationAlerts">
                                            <div class="text-muted text-center">
                                                <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                                <p class="mb-0">Nhập thông tin để kiểm tra</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">Thao tác nhanh</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-1"></i>
                                                Tạo chi phí
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                                <i class="fas fa-times me-1"></i>
                                                Hủy bỏ
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    let debounceTimer;
    
    // Auto-suggest category when description, amount, or vendor changes
    $('#description, #amount, #vendor').on('input', function() {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(function() {
            suggestCategory();
        }, 1000); // Wait 1 second after user stops typing
    });
    
    // Manual category suggestion
    $('#suggestCategoryBtn').on('click', function() {
        suggestCategory();
    });
    
    function suggestCategory() {
        const description = $('#description').val();
        const amount = $('#amount').val();
        const vendor = $('#vendor').val();
        const teamId = $('#team_id').val();
        
        if (!description || !amount || !teamId) {
            return;
        }
        
        $('#suggestCategoryBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Đang phân tích...');
        
        $.ajax({
            url: '{{ route("finance.expenses.suggest-category") }}',
            method: 'POST',
            data: {
                description: description,
                amount: amount,
                vendor: vendor,
                team_id: teamId,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    displayCategorySuggestion(response.categorization);
                }
            },
            error: function() {
                showAlert('Không thể gợi ý phân loại. Vui lòng thử lại.', 'danger');
            },
            complete: function() {
                $('#suggestCategoryBtn').prop('disabled', false).html('<i class="fas fa-magic me-1"></i>Gợi ý phân loại');
            }
        });
    }
    
    function displayCategorySuggestion(categorization) {
        const categoryNames = {
            'advertising': 'Quảng cáo',
            'shipping': 'Vận chuyển',
            'refunds': 'Hoàn tiền',
            'tools_software': 'Công cụ/Phần mềm',
            'office_supplies': 'Văn phòng phẩm',
            'utilities': 'Tiện ích',
            'travel': 'Du lịch',
            'meals': 'Ăn uống',
            'other': 'Khác'
        };
        
        $('#suggestedCategory').html(`
            <span class="badge bg-primary">${categoryNames[categorization.suggested_category] || categorization.suggested_category}</span>
        `);
        
        $('#confidenceScore').html(`
            <small class="text-muted">Độ tin cậy: ${categorization.confidence}%</small>
        `);
        
        // Show alternatives if available
        if (categorization.alternatives && Object.keys(categorization.alternatives).length > 0) {
            let alternativesHtml = '<small class="text-muted">Lựa chọn khác:</small><br>';
            Object.entries(categorization.alternatives).forEach(([category, score]) => {
                alternativesHtml += `<span class="badge bg-secondary me-1 mb-1" style="cursor: pointer;" onclick="selectCategory('${category}')">${categoryNames[category] || category} (${score.toFixed(1)}%)</span>`;
            });
            $('#alternativeCategories').html(alternativesHtml);
        }
        
        // Auto-select if confidence is high
        if (categorization.confidence >= 80) {
            $('#category').val(categorization.suggested_category);
        }
        
        $('#categoryPrediction').removeClass('d-none');
    }
    
    // Form validation
    $('#expenseForm').on('submit', function(e) {
        // Add any additional client-side validation here
    });
});

function selectCategory(category) {
    $('#category').val(category);
    $('#category').trigger('change');
}

function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert at top of form
    $('#expenseForm').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
@endpush
@endsection
