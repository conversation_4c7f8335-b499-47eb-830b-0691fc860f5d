<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Product extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'sku',
        'description',
        'short_description',
        'images',
        'cost_price',
        'selling_price',
        'compare_price',
        'stock_quantity',
        'min_stock_level',
        'weight',
        'dimensions',
        'category_id',
        'supplier_id',
        'status',
        'is_featured',
        'attributes',
        'seo_title',
        'seo_description'
    ];

    protected $casts = [
        'images' => 'array',
        'attributes' => 'array',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'compare_price' => 'decimal:2',
        'stock_quantity' => 'integer',
        'min_stock_level' => 'integer',
        'is_featured' => 'boolean'
    ];

    // Tự động tạo slug và SKU khi tạo product mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
            if (empty($product->sku)) {
                $product->sku = 'SKU-' . strtoupper(Str::random(8));
            }
        });
    }

    // Relationship với Category
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    // Relationship với Supplier
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    // Relationship với OrderItems
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    // Scope để lấy các product đang active
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Scope để lấy các product nổi bật
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    // Scope để lấy các product còn hàng
    public function scopeInStock($query)
    {
        return $query->where('stock_quantity', '>', 0);
    }

    // Tính profit margin
    public function getProfitMarginAttribute()
    {
        if ($this->cost_price > 0) {
            return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
        }
        return 0;
    }

    // Kiểm tra sản phẩm có còn hàng không
    public function getIsInStockAttribute()
    {
        return $this->stock_quantity > 0;
    }

    // Kiểm tra sản phẩm có dưới mức tồn kho tối thiểu không
    public function getIsLowStockAttribute()
    {
        return $this->stock_quantity <= $this->min_stock_level;
    }

    // Lấy hình ảnh đầu tiên
    public function getFirstImageAttribute()
    {
        return $this->images && count($this->images) > 0 ? $this->images[0] : null;
    }
}
