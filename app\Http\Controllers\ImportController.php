<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ImportController extends Controller
{
    public function index()
    {
        return view('import.index');
    }

    public function importProducts(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240', // 10MB max
        ]);

        try {
            $file = $request->file('csv_file');
            $csvData = array_map('str_getcsv', file($file->getRealPath()));

            // Lấy header từ dòng đầu tiên
            $header = array_shift($csvData);

            // Kiểm tra các cột bắt buộc
            $requiredColumns = ['name', 'cost_price', 'selling_price', 'category_name', 'supplier_name'];
            $missingColumns = array_diff($requiredColumns, $header);

            if (!empty($missingColumns)) {
                return back()->withErrors([
                    'csv_file' => 'File CSV thiếu các cột bắt buộc: ' . implode(', ', $missingColumns)
                ]);
            }

            $imported = 0;
            $errors = [];

            DB::beginTransaction();

            foreach ($csvData as $rowIndex => $row) {
                try {
                    // Tạo array associative từ header và row data
                    $data = array_combine($header, $row);

                    // Bỏ qua dòng trống
                    if (empty(trim($data['name']))) {
                        continue;
                    }

                    // Tìm hoặc tạo category
                    $category = Category::firstOrCreate(
                        ['name' => trim($data['category_name'])],
                        [
                            'slug' => Str::slug(trim($data['category_name'])),
                            'is_active' => true,
                            'sort_order' => 0
                        ]
                    );

                    // Tìm hoặc tạo supplier
                    $supplier = Supplier::firstOrCreate(
                        ['name' => trim($data['supplier_name'])],
                        [
                            'email' => isset($data['supplier_email']) ? trim($data['supplier_email']) : '<EMAIL>',
                            'status' => 'active',
                            'commission_rate' => 0
                        ]
                    );

                    // Chuẩn bị dữ liệu product
                    $productData = [
                        'name' => trim($data['name']),
                        'slug' => Str::slug(trim($data['name'])),
                        'sku' => isset($data['sku']) && !empty(trim($data['sku']))
                            ? trim($data['sku'])
                            : 'SKU-' . strtoupper(Str::random(8)),
                        'description' => isset($data['description']) ? trim($data['description']) : null,
                        'short_description' => isset($data['short_description']) ? trim($data['short_description']) : null,
                        'cost_price' => (float) str_replace(',', '', $data['cost_price']),
                        'selling_price' => (float) str_replace(',', '', $data['selling_price']),
                        'compare_price' => isset($data['compare_price']) && !empty($data['compare_price'])
                            ? (float) str_replace(',', '', $data['compare_price'])
                            : null,
                        'stock_quantity' => isset($data['stock_quantity']) ? (int) $data['stock_quantity'] : 0,
                        'min_stock_level' => isset($data['min_stock_level']) ? (int) $data['min_stock_level'] : 0,
                        'weight' => isset($data['weight']) ? trim($data['weight']) : null,
                        'dimensions' => isset($data['dimensions']) ? trim($data['dimensions']) : null,
                        'category_id' => $category->id,
                        'supplier_id' => $supplier->id,
                        'status' => isset($data['status']) && in_array($data['status'], ['active', 'inactive', 'out_of_stock'])
                            ? $data['status']
                            : 'active',
                        'is_featured' => isset($data['is_featured']) ? (bool) $data['is_featured'] : false,
                        'seo_title' => isset($data['seo_title']) ? trim($data['seo_title']) : null,
                        'seo_description' => isset($data['seo_description']) ? trim($data['seo_description']) : null,
                    ];

                    // Kiểm tra sản phẩm đã tồn tại chưa (theo SKU)
                    $existingProduct = Product::where('sku', $productData['sku'])->first();

                    if ($existingProduct) {
                        // Cập nhật sản phẩm hiện có
                        $existingProduct->update($productData);
                    } else {
                        // Tạo sản phẩm mới
                        Product::create($productData);
                    }

                    $imported++;

                } catch (\Exception $e) {
                    $errors[] = "Dòng " . ($rowIndex + 2) . ": " . $e->getMessage();
                    Log::error("Import error at row " . ($rowIndex + 2), [
                        'error' => $e->getMessage(),
                        'data' => $data ?? null
                    ]);
                }
            }

            DB::commit();

            $message = "Đã import thành công {$imported} sản phẩm.";
            if (!empty($errors)) {
                $message .= " Có " . count($errors) . " lỗi xảy ra.";
            }

            return back()->with('success', $message)->with('import_errors', $errors);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error("Import failed", ['error' => $e->getMessage()]);

            return back()->withErrors([
                'csv_file' => 'Có lỗi xảy ra khi import: ' . $e->getMessage()
            ]);
        }
    }

    public function downloadTemplate()
    {
        $headers = [
            'name',
            'sku',
            'description',
            'short_description',
            'cost_price',
            'selling_price',
            'compare_price',
            'stock_quantity',
            'min_stock_level',
            'weight',
            'dimensions',
            'category_name',
            'supplier_name',
            'supplier_email',
            'status',
            'is_featured',
            'seo_title',
            'seo_description'
        ];

        $sampleData = [
            'Áo thun nam',
            'SKU-001',
            'Áo thun nam chất liệu cotton 100%',
            'Áo thun nam cotton',
            '50000',
            '100000',
            '120000',
            '100',
            '10',
            '200g',
            '30x40x2cm',
            'Thời trang nam',
            'Công ty ABC',
            '<EMAIL>',
            'active',
            '0',
            'Áo thun nam chất lượng cao',
            'Áo thun nam cotton 100% chất lượng cao, giá tốt'
        ];

        $filename = 'product_import_template.csv';

        $handle = fopen('php://output', 'w');

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Thêm BOM để Excel hiển thị đúng tiếng Việt
        fprintf($handle, chr(0xEF).chr(0xBB).chr(0xBF));

        fputcsv($handle, $headers);
        fputcsv($handle, $sampleData);

        fclose($handle);
        exit;
    }
}
