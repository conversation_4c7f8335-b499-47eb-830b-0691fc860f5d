<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('team_id')->nullable()->constrained()->onDelete('set null');

            // Customer Information
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone');
            $table->text('shipping_address');
            $table->text('billing_address')->nullable();

            // Dropshipping specific fields
            $table->string('buyer_id')->nullable(); // ID người mua từ platform
            $table->string('external_order_id')->nullable(); // ID đơn hàng từ eBay/Amazon
            $table->string('platform')->nullable(); // ebay, amazon, shopify, etc.
            $table->string('platform_account')->nullable(); // Tài khoản platform
            $table->text('product_link')->nullable(); // Link sản phẩm gốc
            $table->string('assigned_to')->nullable(); // Người được giao xử lý

            // Financial Information
            $table->decimal('subtotal', 10, 2);
            $table->decimal('shipping_fee', 10, 2)->default(0);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->decimal('cost_amount', 10, 2)->nullable(); // Tổng giá gốc
            $table->decimal('profit_amount', 10, 2)->nullable(); // Lợi nhuận
            $table->decimal('platform_fee', 10, 2)->default(0); // Phí platform

            // Status Management
            $table->enum('status', ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'])->default('pending');
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded', 'partial'])->default('pending');
            $table->string('payment_method')->nullable();
            $table->enum('fulfillment_status', ['pending', 'sourced', 'ordered', 'shipped', 'delivered'])->default('pending');

            // Additional Information
            $table->text('notes')->nullable();
            $table->json('tracking_info')->nullable(); // Thông tin tracking
            $table->json('images')->nullable(); // Hình ảnh đơn hàng
            $table->boolean('is_profitable')->default(true); // Đơn hàng có lợi nhuận
            $table->decimal('profit_margin', 5, 2)->nullable(); // % lợi nhuận

            // Timestamps
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('shipped_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['platform', 'platform_account']);
            $table->index(['assigned_to', 'status']);
            $table->index(['team_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
