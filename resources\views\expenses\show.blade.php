@extends('layouts.app')

@section('title', 'Chi tiết Chi phí - ' . $expense->expense_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt text-primary me-2"></i>
                        Chi tiết Chi phí - {{ $expense->expense_number }}
                    </h5>
                    <div class="d-flex gap-2">
                        @if($expense->can_edit)
                            <a href="{{ route('finance.expenses.edit', $expense) }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit me-1"></i>
                                Chỉnh sửa
                            </a>
                        @endif
                        <a href="{{ route('finance.expenses.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>
                            Quay lại
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- Left Column - Main Information -->
                        <div class="col-md-8">
                            <!-- Basic Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Thông tin cơ bản</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td class="fw-bold">Mã số:</td>
                                                    <td>{{ $expense->expense_number }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Tiêu đề:</td>
                                                    <td>{{ $expense->title }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Team:</td>
                                                    <td>{{ $expense->team->name }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Loại chi phí:</td>
                                                    <td>{{ $expense->expense_type_label }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Phân loại:</td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ ucfirst($expense->category) }}</span>
                                                        @if($expense->category_manually_corrected)
                                                            <span class="badge bg-warning ms-1">Đã sửa thủ công</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless table-sm">
                                                <tr>
                                                    <td class="fw-bold">Số tiền:</td>
                                                    <td class="fs-5 fw-bold text-primary">{{ number_format($expense->amount, 0) }} {{ $expense->currency }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Ngày chi phí:</td>
                                                    <td>{{ $expense->expense_date->format('d/m/Y') }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Tần suất:</td>
                                                    <td>{{ $expense->frequency_label }}</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Vendor:</td>
                                                    <td>{{ $expense->vendor ?: 'Không có' }}</td>
                                                </tr>
                                                @if($expense->checkoutUnit)
                                                <tr>
                                                    <td class="fw-bold">Checkout Unit:</td>
                                                    <td>{{ $expense->checkoutUnit->name }} ({{ $expense->checkoutUnit->rate }}%)</td>
                                                </tr>
                                                @endif
                                            </table>
                                        </div>
                                    </div>

                                    @if($expense->description)
                                    <div class="mt-3">
                                        <h6>Mô tả:</h6>
                                        <p class="text-muted">{{ $expense->description }}</p>
                                    </div>
                                    @endif

                                    @if($expense->notes)
                                    <div class="mt-3">
                                        <h6>Ghi chú:</h6>
                                        <p class="text-muted">{{ $expense->notes }}</p>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Smart Analysis Results -->
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-robot me-2"></i>
                                        Kết quả Phân tích Thông minh
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <h6>Phân loại AI:</h6>
                                            @if($expense->suggested_category)
                                                <p>
                                                    <span class="badge bg-primary">{{ ucfirst($expense->suggested_category) }}</span>
                                                    <br><small class="text-muted">Độ tin cậy: {{ $expense->categorization_confidence }}%</small>
                                                </p>
                                            @else
                                                <p class="text-muted">Chưa có phân tích</p>
                                            @endif
                                        </div>
                                        <div class="col-md-4">
                                            <h6>Mức độ rủi ro:</h6>
                                            @php
                                                $riskColors = [
                                                    'low' => 'success',
                                                    'medium' => 'warning',
                                                    'high' => 'danger'
                                                ];
                                            @endphp
                                            <p>
                                                <span class="badge bg-{{ $riskColors[$expense->risk_level] ?? 'secondary' }}">
                                                    {{ ucfirst($expense->risk_level) }}
                                                </span>
                                                @if($expense->fraud_score > 0)
                                                    <br><small class="text-muted">Fraud Score: {{ $expense->fraud_score }}/100</small>
                                                @endif
                                            </p>
                                        </div>
                                        <div class="col-md-4">
                                            <h6>Validation:</h6>
                                            @if($expense->validation_results)
                                                @php $validation = $expense->validation_results; @endphp
                                                <p>
                                                    @if($validation['overall_status'] == 'passed')
                                                        <span class="badge bg-success">Passed</span>
                                                    @elseif($validation['overall_status'] == 'warning')
                                                        <span class="badge bg-warning">Warning</span>
                                                    @else
                                                        <span class="badge bg-danger">Failed</span>
                                                    @endif
                                                </p>
                                            @else
                                                <p class="text-muted">Chưa kiểm tra</p>
                                            @endif
                                        </div>
                                    </div>

                                    @if($expense->fraud_indicators && count($expense->fraud_indicators) > 0)
                                    <div class="mt-3">
                                        <h6>Fraud Indicators:</h6>
                                        <div class="d-flex flex-wrap gap-1">
                                            @foreach($expense->fraud_indicators as $indicator)
                                                <span class="badge bg-warning">{{ str_replace('_', ' ', $indicator) }}</span>
                                            @endforeach
                                        </div>
                                    </div>
                                    @endif

                                    @if($expense->validation_results && (count($expense->validation_results['flags'] ?? []) > 0 || count($expense->validation_results['warnings'] ?? []) > 0))
                                    <div class="mt-3">
                                        <h6>Validation Issues:</h6>
                                        @foreach($expense->validation_results['flags'] ?? [] as $flag)
                                            <div class="alert alert-danger alert-sm py-2">
                                                <strong>{{ $flag['type'] }}:</strong> {{ $flag['message'] }}
                                            </div>
                                        @endforeach
                                        @foreach($expense->validation_results['warnings'] ?? [] as $warning)
                                            <div class="alert alert-warning alert-sm py-2">
                                                <strong>{{ $warning['type'] }}:</strong> {{ $warning['message'] }}
                                            </div>
                                        @endforeach
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Approval History -->
                            @if($expense->approval_history && count($expense->approval_history) > 0)
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Lịch sử Duyệt</h6>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        @foreach($expense->approval_history as $history)
                                        <div class="timeline-item">
                                            <div class="timeline-marker bg-{{ $history['decision'] == 'approved' ? 'success' : 'danger' }}"></div>
                                            <div class="timeline-content">
                                                <h6 class="mb-1">{{ ucfirst($history['decision']) }}</h6>
                                                <p class="text-muted mb-1">
                                                    Bởi: {{ App\Models\User::find($history['approver_id'])->name ?? 'Unknown' }}
                                                    - {{ \Carbon\Carbon::parse($history['approved_at'] ?? $history['rejected_at'])->format('d/m/Y H:i') }}
                                                </p>
                                                @if(isset($history['comments']) && $history['comments'])
                                                    <p class="mb-0">{{ $history['comments'] }}</p>
                                                @endif
                                                @if(isset($history['processing_time']))
                                                    <small class="text-muted">Thời gian xử lý: {{ $history['processing_time'] }} phút</small>
                                                @endif
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endif

                            <!-- Attachments -->
                            @if($expense->attachments && count($expense->attachments) > 0)
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Đính kèm</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        @foreach($expense->attachments as $attachment)
                                        <div class="col-md-3 mb-3">
                                            <div class="card">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-file fa-2x text-muted mb-2"></i>
                                                    <p class="card-text small">{{ basename($attachment) }}</p>
                                                    <a href="{{ Storage::url($attachment) }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                                        <i class="fas fa-download me-1"></i>
                                                        Tải về
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>

                        <!-- Right Column - Status & Actions -->
                        <div class="col-md-4">
                            <!-- Status Card -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Trạng thái</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Trạng thái chung:</label>
                                        @php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'paid' => 'primary',
                                                'cancelled' => 'danger'
                                            ];
                                        @endphp
                                        <div>
                                            <span class="badge bg-{{ $statusColors[$expense->status] ?? 'secondary' }} fs-6">
                                                {{ $expense->status_label }}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Trạng thái duyệt:</label>
                                        @php
                                            $approvalColors = [
                                                'pending' => 'warning',
                                                'approved' => 'success',
                                                'auto_approved' => 'info',
                                                'rejected' => 'danger'
                                            ];
                                        @endphp
                                        <div>
                                            <span class="badge bg-{{ $approvalColors[$expense->approval_status] ?? 'secondary' }} fs-6">
                                                {{ ucfirst($expense->approval_status) }}
                                            </span>
                                        </div>
                                    </div>

                                    @if($approvalStatus && isset($approvalStatus['progress_percentage']))
                                    <div class="mb-3">
                                        <label class="form-label">Tiến độ duyệt:</label>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar" style="width: {{ $approvalStatus['progress_percentage'] }}%">
                                                {{ $approvalStatus['progress_percentage'] }}%
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            {{ $approvalStatus['completed_steps'] }}/{{ $approvalStatus['total_steps'] }} bước hoàn thành
                                        </small>
                                    </div>
                                    @endif

                                    <div class="mb-3">
                                        <label class="form-label">Người tạo:</label>
                                        <div>{{ $expense->createdBy->name }}</div>
                                        <small class="text-muted">{{ $expense->created_at->format('d/m/Y H:i') }}</small>
                                    </div>

                                    @if($expense->approved_by)
                                    <div class="mb-3">
                                        <label class="form-label">Người duyệt:</label>
                                        <div>{{ $expense->approvedBy->name ?? 'System' }}</div>
                                        @if($expense->approved_at)
                                            <small class="text-muted">{{ $expense->approved_at->format('d/m/Y H:i') }}</small>
                                        @endif
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Actions Card -->
                            @if($expense->can_approve && auth()->user()->hasRole(['admin', 'finance_manager', 'team_leader']))
                            <div class="card mb-4">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">
                                        <i class="fas fa-tasks me-2"></i>
                                        Thao tác Duyệt
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @if($approvalStatus && isset($approvalStatus['current_step']))
                                    <div class="mb-3">
                                        <label class="form-label">Bước hiện tại:</label>
                                        <div class="alert alert-info py-2">
                                            <strong>{{ $approvalStatus['current_step']['level'] ?? 'Unknown' }}</strong>
                                            <br><small>{{ $approvalStatus['current_step']['approver_role'] ?? '' }}</small>
                                        </div>
                                    </div>
                                    @endif

                                    <form action="{{ route('finance.expenses.process-approval', $expense) }}" method="POST">
                                        @csrf
                                        <div class="mb-3">
                                            <label for="decision" class="form-label">Quyết định <span class="text-danger">*</span></label>
                                            <select class="form-select" id="decision" name="decision" required>
                                                <option value="">Chọn quyết định</option>
                                                <option value="approved">Duyệt</option>
                                                <option value="rejected">Từ chối</option>
                                                <option value="request_changes">Yêu cầu chỉnh sửa</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="comments" class="form-label">Ghi chú</label>
                                            <textarea class="form-control" id="comments" name="comments" rows="3" placeholder="Nhập ghi chú (tùy chọn)"></textarea>
                                        </div>
                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-check me-1"></i>
                                                Xác nhận
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            @endif

                            <!-- Quick Info -->
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Thông tin nhanh</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless table-sm">
                                        <tr>
                                            <td>Tháng chi phí:</td>
                                            <td><strong>{{ $expense->formatted_month }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td>Ngày tạo:</td>
                                            <td>{{ $expense->created_at->format('d/m/Y H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Cập nhật cuối:</td>
                                            <td>{{ $expense->updated_at->format('d/m/Y H:i') }}</td>
                                        </tr>
                                        @if($expense->processed_at)
                                        <tr>
                                            <td>Xử lý xong:</td>
                                            <td>{{ $expense->processed_at->format('d/m/Y H:i') }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    margin-bottom: 0.5rem;
}
</style>
@endpush
@endsection
