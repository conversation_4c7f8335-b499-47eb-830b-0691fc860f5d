@echo off
echo ========================================
echo    DROPSHIP MANAGER - AUTO INSTALLER
echo ========================================
echo.

echo [1/6] Checking PHP and Composer...
php --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: PHP not found! Please install PHP first.
    pause
    exit /b 1
)

composer --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Composer not found! Please install Composer first.
    pause
    exit /b 1
)

echo [2/6] Installing dependencies...
composer install --no-dev --optimize-autoloader

echo [3/6] Setting up environment...
if not exist .env (
    copy .env.example .env
    echo .env file created from .env.example
)

echo [4/6] Generating application key...
php artisan key:generate

echo [5/6] Creating storage link...
php artisan storage:link

echo [6/6] Setup completed!
echo.
echo ========================================
echo           NEXT STEPS
echo ========================================
echo 1. Start XAMPP and enable Apache + MySQL
echo 2. Create database 'dropshipping_db' in phpMyAdmin
echo 3. Run: php artisan migrate
echo 4. Run: php artisan db:seed
echo 5. Run: php artisan serve
echo 6. Visit: http://localhost:8000
echo ========================================
echo.
pause
