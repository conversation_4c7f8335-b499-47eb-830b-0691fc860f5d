<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monthly_expenses', function (Blueprint $table) {
            // Smart categorization fields
            $table->string('vendor')->nullable()->after('description');
            $table->string('suggested_category')->nullable()->after('category');
            $table->decimal('categorization_confidence', 5, 2)->nullable()->after('suggested_category');
            $table->boolean('category_manually_corrected')->default(false)->after('categorization_confidence');

            // Validation and fraud detection fields
            $table->json('validation_results')->nullable()->after('category_manually_corrected');
            $table->decimal('fraud_score', 5, 2)->default(0)->after('validation_results');
            $table->string('risk_level')->default('low')->after('fraud_score'); // low, medium, high
            $table->json('fraud_indicators')->nullable()->after('risk_level');

            // Approval workflow fields
            $table->string('approval_status')->default('pending')->after('status'); // pending, approved, rejected, auto_approved
            $table->json('approval_path')->nullable()->after('approval_status');
            $table->json('approval_history')->nullable()->after('approval_path');
            $table->timestamp('approved_at')->nullable()->after('approval_history');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null')->after('approved_at');
            $table->text('approval_comments')->nullable()->after('approved_by');

            // Additional metadata
            $table->json('metadata')->nullable()->after('approval_comments');
            $table->timestamp('processed_at')->nullable()->after('metadata');

            // Indexes for performance
            $table->index(['approval_status', 'created_at']);
            $table->index(['risk_level', 'fraud_score']);
            $table->index(['category', 'team_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monthly_expenses', function (Blueprint $table) {
            $table->dropColumn([
                'vendor',
                'suggested_category',
                'categorization_confidence',
                'category_manually_corrected',
                'validation_results',
                'fraud_score',
                'risk_level',
                'fraud_indicators',
                'approval_status',
                'approval_path',
                'approval_history',
                'approved_at',
                'approved_by',
                'approval_comments',
                'metadata',
                'processed_at'
            ]);
        });
    }
};
