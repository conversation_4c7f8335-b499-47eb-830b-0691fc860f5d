<?php

namespace App\Services;

use App\Models\MonthlyExpense;
use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ExpenseAnalyticsDashboard
{
    public function generateRealTimeInsights($teamId, $period = 'current_month')
    {
        $cacheKey = "expense_insights_{$teamId}_{$period}";

        return Cache::remember($cacheKey, 300, function () use ($teamId, $period) { // 5 minutes cache
            return [
                'spending_overview' => $this->getSpendingOverview($teamId, $period),
                'category_breakdown' => $this->getCategoryBreakdown($teamId, $period),
                'trend_analysis' => $this->getTrendAnalysis($teamId, $period),
                'budget_tracking' => $this->getBudgetTracking($teamId, $period),
                'efficiency_metrics' => $this->getEfficiencyMetrics($teamId, $period),
                'alerts_and_recommendations' => $this->getAlertsAndRecommendations($teamId),
                'approval_metrics' => $this->getApprovalMetrics($teamId, $period),
                'risk_analysis' => $this->getRiskAnalysis($teamId, $period)
            ];
        });
    }

    private function getSpendingOverview($teamId, $period)
    {
        $expenses = $this->getExpensesForPeriod($teamId, $period);
        $previousPeriodExpenses = $this->getExpensesForPreviousPeriod($teamId, $period);

        $totalSpent = $expenses->sum('amount');
        $previousTotal = $previousPeriodExpenses->sum('amount');
        $changePercentage = $previousTotal > 0 ?
            round((($totalSpent - $previousTotal) / $previousTotal) * 100, 2) : 0;

        return [
            'total_spent' => $totalSpent,
            'total_transactions' => $expenses->count(),
            'average_transaction' => $expenses->count() > 0 ? round($totalSpent / $expenses->count(), 2) : 0,
            'largest_transaction' => $expenses->max('amount') ?: 0,
            'smallest_transaction' => $expenses->min('amount') ?: 0,
            'pending_approvals' => $expenses->where('approval_status', 'pending')->sum('amount'),
            'approved_today' => $expenses->where('approved_at', '>=', today())->sum('amount'),
            'vs_previous_period' => [
                'amount_change' => $totalSpent - $previousTotal,
                'percentage_change' => $changePercentage,
                'trend' => $changePercentage > 0 ? 'increasing' : ($changePercentage < 0 ? 'decreasing' : 'stable')
            ],
            'daily_average' => $this->getDailyAverage($teamId, $period),
            'projected_monthly_total' => $this->getProjectedMonthlyTotal($teamId, $period)
        ];
    }

    private function getCategoryBreakdown($teamId, $period)
    {
        $expenses = $this->getExpensesForPeriod($teamId, $period);
        $totalAmount = $expenses->sum('amount');
        $categoryData = [];

        foreach ($expenses->groupBy('category') as $category => $categoryExpenses) {
            $categoryTotal = $categoryExpenses->sum('amount');
            $previousPeriodTotal = $this->getCategoryPreviousPeriodTotal($teamId, $category, $period);

            $categoryData[$category] = [
                'total_amount' => $categoryTotal,
                'transaction_count' => $categoryExpenses->count(),
                'percentage_of_total' => $totalAmount > 0 ? round(($categoryTotal / $totalAmount) * 100, 2) : 0,
                'average_amount' => round($categoryTotal / $categoryExpenses->count(), 2),
                'trend' => $this->getCategoryTrend($teamId, $category, $period),
                'budget_utilization' => $this->getBudgetUtilization($teamId, $category, $period),
                'vs_previous_period' => [
                    'amount_change' => $categoryTotal - $previousPeriodTotal,
                    'percentage_change' => $previousPeriodTotal > 0 ?
                        round((($categoryTotal - $previousPeriodTotal) / $previousPeriodTotal) * 100, 2) : 0
                ],
                'top_expenses' => $categoryExpenses->sortByDesc('amount')->take(3)->values(),
                'approval_rate' => $this->getCategoryApprovalRate($categoryExpenses),
                'average_processing_time' => $this->getCategoryAverageProcessingTime($categoryExpenses)
            ];
        }

        // Sort by total amount descending
        uasort($categoryData, function($a, $b) {
            return $b['total_amount'] <=> $a['total_amount'];
        });

        return $categoryData;
    }

    private function getTrendAnalysis($teamId, $period)
    {
        $trends = [];

        // Daily spending trend for current month
        $dailySpending = $this->getDailySpendingTrend($teamId, $period);

        // Weekly comparison
        $weeklyComparison = $this->getWeeklyComparison($teamId);

        // Monthly growth rate
        $monthlyGrowthRate = $this->getMonthlyGrowthRate($teamId);

        // Seasonal patterns
        $seasonalPatterns = $this->getSeasonalPatterns($teamId);

        return [
            'daily_spending' => $dailySpending,
            'weekly_comparison' => $weeklyComparison,
            'monthly_growth_rate' => $monthlyGrowthRate,
            'seasonal_patterns' => $seasonalPatterns,
            'spending_velocity' => $this->getSpendingVelocity($teamId, $period),
            'peak_spending_days' => $this->getPeakSpendingDays($teamId),
            'category_trends' => $this->getCategoryTrends($teamId, $period)
        ];
    }

    private function getBudgetTracking($teamId, $period)
    {
        // This would integrate with a budget management system
        // For now, we'll use estimated budgets based on historical data

        $expenses = $this->getExpensesForPeriod($teamId, $period);
        $totalSpent = $expenses->sum('amount');

        // Estimate monthly budget as 120% of average monthly spending over last 6 months
        $estimatedBudget = $this->getEstimatedMonthlyBudget($teamId);

        $budgetUtilization = $estimatedBudget > 0 ? ($totalSpent / $estimatedBudget) * 100 : 0;

        return [
            'estimated_budget' => $estimatedBudget,
            'total_spent' => $totalSpent,
            'remaining_budget' => max(0, $estimatedBudget - $totalSpent),
            'utilization_percentage' => round($budgetUtilization, 2),
            'projected_overspend' => $this->getProjectedOverspend($teamId, $period),
            'budget_status' => $this->getBudgetStatus($budgetUtilization),
            'category_budgets' => $this->getCategoryBudgetTracking($teamId, $period),
            'burn_rate' => $this->getBurnRate($teamId, $period),
            'days_until_budget_exhausted' => $this->getDaysUntilBudgetExhausted($teamId, $period)
        ];
    }

    private function getEfficiencyMetrics($teamId, $period)
    {
        $expenses = $this->getExpensesForPeriod($teamId, $period);

        return [
            'average_processing_time' => $this->getAverageProcessingTime($expenses),
            'auto_approval_rate' => $this->getAutoApprovalRate($expenses),
            'first_time_approval_rate' => $this->getFirstTimeApprovalRate($expenses),
            'categorization_accuracy' => $this->getCategorizationAccuracy($teamId, $period),
            'data_quality_score' => $this->getDataQualityScore($expenses),
            'compliance_rate' => $this->getComplianceRate($expenses),
            'cost_per_transaction' => $this->getCostPerTransaction($expenses),
            'processing_efficiency_trend' => $this->getProcessingEfficiencyTrend($teamId)
        ];
    }

    private function getAlertsAndRecommendations($teamId)
    {
        $alerts = [];

        // Budget alerts
        $budgetAlerts = $this->checkBudgetAlerts($teamId);
        $alerts = array_merge($alerts, $budgetAlerts);

        // Spending pattern alerts
        $patternAlerts = $this->checkSpendingPatterns($teamId);
        $alerts = array_merge($alerts, $patternAlerts);

        // Efficiency recommendations
        $efficiencyRecs = $this->generateEfficiencyRecommendations($teamId);
        $alerts = array_merge($alerts, $efficiencyRecs);

        // Fraud alerts
        $fraudAlerts = $this->checkFraudAlerts($teamId);
        $alerts = array_merge($alerts, $fraudAlerts);

        // Process optimization suggestions
        $processOptimizations = $this->getProcessOptimizations($teamId);
        $alerts = array_merge($alerts, $processOptimizations);

        // Sort by priority
        usort($alerts, function($a, $b) {
            $priorityOrder = ['critical' => 1, 'high' => 2, 'medium' => 3, 'low' => 4];
            return $priorityOrder[$a['priority']] <=> $priorityOrder[$b['priority']];
        });

        return $alerts;
    }

    private function getApprovalMetrics($teamId, $period)
    {
        $expenses = $this->getExpensesForPeriod($teamId, $period);

        return [
            'total_pending' => $expenses->where('approval_status', 'pending')->count(),
            'total_approved' => $expenses->where('approval_status', 'approved')->count(),
            'total_rejected' => $expenses->where('approval_status', 'rejected')->count(),
            'auto_approved' => $expenses->where('approval_status', 'auto_approved')->count(),
            'approval_rate' => $this->calculateApprovalRate($expenses),
            'average_approval_time' => $this->getAverageApprovalTime($expenses),
            'bottlenecks' => $this->identifyApprovalBottlenecks($teamId, $period),
            'approver_performance' => $this->getApproverPerformance($teamId, $period),
            'escalated_approvals' => $this->getEscalatedApprovals($expenses),
            'approval_trends' => $this->getApprovalTrends($teamId)
        ];
    }

    private function getRiskAnalysis($teamId, $period)
    {
        $expenses = $this->getExpensesForPeriod($teamId, $period);

        return [
            'high_risk_expenses' => $expenses->where('risk_level', 'high')->count(),
            'medium_risk_expenses' => $expenses->where('risk_level', 'medium')->count(),
            'low_risk_expenses' => $expenses->where('risk_level', 'low')->count(),
            'average_fraud_score' => round($expenses->avg('fraud_score'), 2),
            'fraud_indicators_summary' => $this->getFraudIndicatorsSummary($expenses),
            'risk_trends' => $this->getRiskTrends($teamId),
            'validation_failure_rate' => $this->getValidationFailureRate($expenses),
            'manual_review_rate' => $this->getManualReviewRate($expenses),
            'risk_by_category' => $this->getRiskByCategory($expenses),
            'risk_mitigation_effectiveness' => $this->getRiskMitigationEffectiveness($teamId)
        ];
    }

    // Helper methods for data retrieval
    private function getExpensesForPeriod($teamId, $period)
    {
        $query = MonthlyExpense::where('team_id', $teamId);

        switch ($period) {
            case 'current_month':
                return $query->whereMonth('created_at', now()->month)
                           ->whereYear('created_at', now()->year)
                           ->get();
            case 'last_month':
                return $query->whereMonth('created_at', now()->subMonth()->month)
                           ->whereYear('created_at', now()->subMonth()->year)
                           ->get();
            case 'current_quarter':
                return $query->whereBetween('created_at', [
                    now()->startOfQuarter(),
                    now()->endOfQuarter()
                ])->get();
            case 'current_year':
                return $query->whereYear('created_at', now()->year)->get();
            case 'last_30_days':
                return $query->where('created_at', '>=', now()->subDays(30))->get();
            case 'last_7_days':
                return $query->where('created_at', '>=', now()->subDays(7))->get();
            default:
                return $query->whereMonth('created_at', now()->month)
                           ->whereYear('created_at', now()->year)
                           ->get();
        }
    }

    private function getExpensesForPreviousPeriod($teamId, $period)
    {
        $query = MonthlyExpense::where('team_id', $teamId);

        switch ($period) {
            case 'current_month':
                return $query->whereMonth('created_at', now()->subMonth()->month)
                           ->whereYear('created_at', now()->subMonth()->year)
                           ->get();
            case 'current_quarter':
                return $query->whereBetween('created_at', [
                    now()->subQuarter()->startOfQuarter(),
                    now()->subQuarter()->endOfQuarter()
                ])->get();
            case 'last_30_days':
                return $query->whereBetween('created_at', [
                    now()->subDays(60),
                    now()->subDays(30)
                ])->get();
            default:
                return collect();
        }
    }

    private function getDailyAverage($teamId, $period)
    {
        $expenses = $this->getExpensesForPeriod($teamId, $period);
        $days = $this->getPeriodDays($period);

        return $days > 0 ? round($expenses->sum('amount') / $days, 2) : 0;
    }

    private function getProjectedMonthlyTotal($teamId, $period)
    {
        if ($period !== 'current_month') return null;

        $currentMonthExpenses = $this->getExpensesForPeriod($teamId, 'current_month');
        $daysElapsed = now()->day;
        $daysInMonth = now()->daysInMonth;

        if ($daysElapsed === 0) return 0;

        $dailyAverage = $currentMonthExpenses->sum('amount') / $daysElapsed;
        return round($dailyAverage * $daysInMonth, 2);
    }

    private function getPeriodDays($period)
    {
        switch ($period) {
            case 'current_month':
                return now()->day;
            case 'last_month':
                return now()->subMonth()->daysInMonth;
            case 'current_quarter':
                return now()->diffInDays(now()->startOfQuarter()) + 1;
            case 'last_30_days':
                return 30;
            case 'last_7_days':
                return 7;
            default:
                return 30;
        }
    }

    private function getEstimatedMonthlyBudget($teamId)
    {
        // Calculate average monthly spending over last 6 months and add 20% buffer
        try {
            $monthlyTotals = DB::table('monthly_expenses')
                ->select(DB::raw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(amount) as monthly_total'))
                ->where('team_id', $teamId)
                ->where('created_at', '>=', now()->subMonths(6))
                ->groupBy(DB::raw('YEAR(created_at), MONTH(created_at)'))
                ->get();

            if ($monthlyTotals->isEmpty()) {
                return 0;
            }

            $avgMonthlySpending = $monthlyTotals->avg('monthly_total');
            return round($avgMonthlySpending * 1.2, 2); // Add 20% buffer
        } catch (\Exception $e) {
            // Fallback to simple calculation
            $totalSpending = MonthlyExpense::where('team_id', $teamId)
                ->where('created_at', '>=', now()->subMonths(6))
                ->sum('amount');

            return round(($totalSpending / 6) * 1.2, 2);
        }
    }

    // Placeholder methods for complex calculations
    private function getCategoryPreviousPeriodTotal($teamId, $category, $period) { return 0; }
    private function getCategoryTrend($teamId, $category, $period) { return 'stable'; }
    private function getBudgetUtilization($teamId, $category, $period) { return 0; }
    private function getCategoryApprovalRate($expenses) { return 95; }
    private function getCategoryAverageProcessingTime($expenses) { return 24; }
    private function getDailySpendingTrend($teamId, $period) { return []; }
    private function getWeeklyComparison($teamId) { return []; }
    private function getMonthlyGrowthRate($teamId) { return 0; }
    private function getSeasonalPatterns($teamId) { return []; }
    private function getSpendingVelocity($teamId, $period) { return 0; }
    private function getPeakSpendingDays($teamId) { return []; }
    private function getCategoryTrends($teamId, $period) { return []; }
    private function getProjectedOverspend($teamId, $period) { return 0; }
    private function getBudgetStatus($utilization) { return $utilization > 90 ? 'warning' : 'good'; }
    private function getCategoryBudgetTracking($teamId, $period) { return []; }
    private function getBurnRate($teamId, $period) { return 0; }
    private function getDaysUntilBudgetExhausted($teamId, $period) { return null; }
    private function getAverageProcessingTime($expenses) { return 24; }
    private function getAutoApprovalRate($expenses) { return 30; }
    private function getFirstTimeApprovalRate($expenses) { return 85; }
    private function getCategorizationAccuracy($teamId, $period) { return 92; }
    private function getDataQualityScore($expenses) { return 88; }
    private function getComplianceRate($expenses) { return 96; }
    private function getCostPerTransaction($expenses) { return 0; }
    private function getProcessingEfficiencyTrend($teamId) { return []; }
    private function checkBudgetAlerts($teamId) { return []; }
    private function checkSpendingPatterns($teamId) { return []; }
    private function generateEfficiencyRecommendations($teamId) { return []; }
    private function checkFraudAlerts($teamId) { return []; }
    private function getProcessOptimizations($teamId) { return []; }
    private function calculateApprovalRate($expenses) { return 90; }
    private function getAverageApprovalTime($expenses) { return 18; }
    private function identifyApprovalBottlenecks($teamId, $period) { return []; }
    private function getApproverPerformance($teamId, $period) { return []; }
    private function getEscalatedApprovals($expenses) { return 0; }
    private function getApprovalTrends($teamId) { return []; }
    private function getFraudIndicatorsSummary($expenses) { return []; }
    private function getRiskTrends($teamId) { return []; }
    private function getValidationFailureRate($expenses) { return 5; }
    private function getManualReviewRate($expenses) { return 15; }
    private function getRiskByCategory($expenses) { return []; }
    private function getRiskMitigationEffectiveness($teamId) { return 85; }
}
