<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'label',
        'description',
        'validation_rules',
        'is_encrypted',
        'is_public',
        'sort_order'
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
        'validation_rules' => 'array',
        'sort_order' => 'integer'
    ];

    // Cache duration for settings (1 hour)
    const CACHE_DURATION = 3600;

    // Boot method để handle caching
    protected static function boot()
    {
        parent::boot();

        // Clear cache when settings change
        static::saved(function ($setting) {
            Cache::forget('settings_all');
            Cache::forget('settings_category_' . $setting->category);
            Cache::forget('setting_' . $setting->key);
        });

        static::deleted(function ($setting) {
            Cache::forget('settings_all');
            Cache::forget('settings_category_' . $setting->category);
            Cache::forget('setting_' . $setting->key);
        });
    }

    // Scope để lấy settings theo category
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category)->orderBy('sort_order');
    }

    // Scope để lấy public settings
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    // Get processed value (decrypt if needed, cast to correct type)
    public function getProcessedValueAttribute()
    {
        $value = $this->value;

        // Decrypt if encrypted
        if ($this->is_encrypted && $value) {
            try {
                $value = Crypt::decryptString($value);
            } catch (\Exception $e) {
                return $this->default_value;
            }
        }

        // Cast to correct type
        return $this->castValue($value);
    }

    // Set value (encrypt if needed)
    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $value = Crypt::encryptString($value);
        }

        $this->attributes['value'] = $value;
    }

    // Cast value to correct type
    private function castValue($value)
    {
        if ($value === null) {
            return null;
        }

        switch ($this->type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'array':
            case 'json':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }

    // Static method để get setting value
    public static function get($key, $default = null)
    {
        $cacheKey = 'setting_' . $key;

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            return $setting->processed_value ?? $default;
        });
    }

    // Static method để set setting value
    public static function set($key, $value)
    {
        $setting = static::where('key', $key)->first();

        if ($setting) {
            $setting->update(['value' => $value]);
        } else {
            // Create new setting if not exists
            static::create([
                'key' => $key,
                'value' => $value
            ]);
        }

        return true;
    }

    // Get all settings by category
    public static function getByCategory($category)
    {
        $cacheKey = 'settings_category_' . $category;

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($category) {
            return static::byCategory($category)->get()->mapWithKeys(function ($setting) {
                return [$setting->key => $setting->processed_value];
            });
        });
    }

    // Get all settings
    public static function getAll()
    {
        return Cache::remember('settings_all', self::CACHE_DURATION, function () {
            return static::all()->mapWithKeys(function ($setting) {
                return [$setting->key => $setting->processed_value];
            });
        });
    }

    // Clear all settings cache
    public static function clearCache()
    {
        Cache::forget('settings_all');

        // Clear category caches
        $categories = static::distinct('category')->pluck('category');
        foreach ($categories as $category) {
            Cache::forget('settings_category_' . $category);
        }

        // Clear individual setting caches
        $keys = static::pluck('key');
        foreach ($keys as $key) {
            Cache::forget('setting_' . $key);
        }
    }

    // Validate setting value
    public function validateValue($value)
    {
        if (!$this->validation_rules) {
            return true;
        }

        $rules = is_array($this->validation_rules) ? $this->validation_rules : [$this->validation_rules];

        $validator = \Validator::make(
            ['value' => $value],
            ['value' => $rules]
        );

        return $validator->passes();
    }

    // Get validation errors
    public function getValidationErrors($value)
    {
        if (!$this->validation_rules) {
            return [];
        }

        $rules = is_array($this->validation_rules) ? $this->validation_rules : [$this->validation_rules];

        $validator = \Validator::make(
            ['value' => $value],
            ['value' => $rules]
        );

        return $validator->errors()->get('value');
    }



    // Check if setting has options (for select/radio inputs)
    public function hasOptions()
    {
        return !empty($this->options);
    }

    // Get formatted options for form inputs
    public function getFormattedOptions()
    {
        if (!$this->hasOptions()) {
            return [];
        }

        // If options is associative array, return as is
        if (array_keys($this->options) !== range(0, count($this->options) - 1)) {
            return $this->options;
        }

        // If options is indexed array, create value => label pairs
        return array_combine($this->options, $this->options);
    }
}
