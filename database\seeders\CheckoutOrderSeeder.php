<?php

namespace Database\Seeders;

use App\Models\CheckoutOrder;
use App\Models\CheckoutPayment;
use App\Models\CheckoutUnit;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class CheckoutOrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $checkoutUnits = CheckoutUnit::all();

        if ($checkoutUnits->isEmpty()) {
            return;
        }

        $statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled', 'returned', 'refunded'];
        $paymentStatuses = ['pending', 'partial', 'paid', 'refunded'];

        // Tạo 50 đơn hàng checkout mẫu
        for ($i = 1; $i <= 50; $i++) {
            $checkoutUnit = $checkoutUnits->random();
            $orderValue = rand(100000, 2000000); // 100k - 2M
            $checkoutFee = $orderValue * ($checkoutUnit->checkout_rate / 100);
            $shippingFee = rand(20000, 50000); // 20k - 50k
            $totalAmount = $orderValue + $checkoutFee + $shippingFee;

            $status = $statuses[array_rand($statuses)];
            $paymentStatus = $paymentStatuses[array_rand($paymentStatuses)];

            // Điều chỉnh payment status theo status
            if (in_array($status, ['completed', 'delivered'])) {
                $paymentStatus = 'paid';
            } elseif (in_array($status, ['cancelled', 'refunded'])) {
                $paymentStatus = 'refunded';
            }

            $createdAt = Carbon::now()->subDays(rand(0, 90)); // Trong 90 ngày qua

            $checkoutOrder = CheckoutOrder::create([
                'checkout_unit_id' => $checkoutUnit->id,
                'external_order_id' => 'EXT-' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'customer_name' => 'Khách hàng ' . $i,
                'customer_phone' => '09' . rand(10000000, 99999999),
                'customer_email' => 'customer' . $i . '@example.com',
                'shipping_address' => 'Địa chỉ giao hàng số ' . $i . ', Quận ' . rand(1, 12) . ', TP.HCM',
                'order_value' => $orderValue,
                'checkout_fee' => $checkoutFee,
                'shipping_fee' => $shippingFee,
                'total_amount' => $totalAmount,
                'status' => $status,
                'payment_status' => $paymentStatus,
                'notes' => rand(0, 1) ? 'Ghi chú cho đơn hàng ' . $i : null,
                'confirmed_at' => in_array($status, ['confirmed', 'processing', 'shipped', 'delivered', 'completed']) ? $createdAt->copy()->addHours(rand(1, 24)) : null,
                'shipped_at' => in_array($status, ['shipped', 'delivered', 'completed']) ? $createdAt->copy()->addDays(rand(1, 3)) : null,
                'delivered_at' => in_array($status, ['delivered', 'completed']) ? $createdAt->copy()->addDays(rand(3, 7)) : null,
                'completed_at' => $status === 'completed' ? $createdAt->copy()->addDays(rand(7, 14)) : null,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);

            // Tạo payment cho một số đơn hàng
            if (in_array($paymentStatus, ['paid', 'partial']) && rand(0, 1)) {
                $paymentAmount = $paymentStatus === 'paid' ? $totalAmount : $totalAmount * 0.7; // 70% nếu partial

                CheckoutPayment::create([
                    'checkout_unit_id' => $checkoutUnit->id,
                    'checkout_order_id' => $checkoutOrder->id,
                    'payment_type' => 'order',
                    'amount' => $paymentAmount,
                    'payment_method' => ['bank_transfer', 'cash', 'e_wallet'][array_rand(['bank_transfer', 'cash', 'e_wallet'])],
                    'status' => 'completed',
                    'description' => 'Thanh toán cho đơn hàng ' . $checkoutOrder->checkout_order_number,
                    'reference_number' => 'REF-' . strtoupper(uniqid()),
                    'payment_date' => $createdAt->copy()->addDays(rand(1, 5)),
                    'processed_at' => $createdAt->copy()->addDays(rand(1, 5)),
                    'created_at' => $createdAt->copy()->addDays(rand(1, 5)),
                    'updated_at' => $createdAt->copy()->addDays(rand(1, 5)),
                ]);
            }
        }

        // Tạo một số bulk payments
        foreach ($checkoutUnits->take(3) as $unit) {
            CheckoutPayment::create([
                'checkout_unit_id' => $unit->id,
                'payment_type' => 'bulk',
                'amount' => rand(5000000, ********), // 5M - 20M
                'payment_method' => 'bank_transfer',
                'status' => 'completed',
                'description' => 'Thanh toán hàng loạt cho ' . $unit->name,
                'reference_number' => 'BULK-' . strtoupper(uniqid()),
                'payment_date' => Carbon::now()->subDays(rand(1, 30)),
                'processed_at' => Carbon::now()->subDays(rand(1, 30)),
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
                'updated_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
        }
    }
}
