<?php

if (!function_exists('setting')) {
    /**
     * Get setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function setting($key, $default = null)
    {
        return \App\Models\Setting::get($key, $default);
    }
}

if (!function_exists('settings')) {
    /**
     * Get multiple settings or all settings by group
     *
     * @param string|array $keys
     * @return mixed
     */
    function settings($keys = null)
    {
        if (is_null($keys)) {
            return \App\Models\Setting::getAll();
        }

        if (is_string($keys)) {
            // If string, treat as category name
            return \App\Models\Setting::getByCategory($keys);
        }

        if (is_array($keys)) {
            // If array, get multiple specific settings
            $result = [];
            foreach ($keys as $key) {
                $result[$key] = setting($key);
            }
            return $result;
        }

        return null;
    }
}

if (!function_exists('set_setting')) {
    /**
     * Set setting value
     *
     * @param string $key
     * @param mixed $value
     * @param int|null $updatedBy
     * @return bool
     */
    function set_setting($key, $value, $updatedBy = null)
    {
        return \App\Models\Setting::set($key, $value, $updatedBy);
    }
}

if (!function_exists('site_name')) {
    /**
     * Get site name
     *
     * @return string
     */
    function site_name()
    {
        return setting('app_name', 'Dropship Manager');
    }
}

if (!function_exists('site_logo')) {
    /**
     * Get site logo URL
     *
     * @return string|null
     */
    function site_logo()
    {
        return setting('site_logo');
    }
}

if (!function_exists('default_currency')) {
    /**
     * Get default currency
     *
     * @return string
     */
    function default_currency()
    {
        return setting('default_currency', 'VND');
    }
}

if (!function_exists('default_exchange_rate')) {
    /**
     * Get default USD/VND exchange rate
     *
     * @return float
     */
    function default_exchange_rate()
    {
        return (float) setting('exchange_rate_usd_vnd', 24000);
    }
}

if (!function_exists('default_checkout_fee')) {
    /**
     * Get default checkout fee percentage
     *
     * @return float
     */
    function default_checkout_fee()
    {
        return (float) setting('checkout_default_rate', 15);
    }
}

if (!function_exists('max_members_per_team')) {
    /**
     * Get maximum members per team
     *
     * @return int
     */
    function max_members_per_team()
    {
        return (int) setting('team_max_members', 50);
    }
}

if (!function_exists('checkout_order_statuses')) {
    /**
     * Get checkout order statuses
     *
     * @return array
     */
    function checkout_order_statuses()
    {
        $statuses = setting('checkout_order_statuses');

        if (is_string($statuses)) {
            $statuses = json_decode($statuses, true);
        }

        return $statuses ?: [
            'pending' => 'Chờ xử lý',
            'confirmed' => 'Đã xác nhận',
            'processing' => 'Đang xử lý',
            'shipped' => 'Đã gửi hàng',
            'delivered' => 'Đã giao hàng',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
            'returned' => 'Đã trả hàng',
            'refunded' => 'Đã hoàn tiền'
        ];
    }
}

if (!function_exists('format_currency')) {
    /**
     * Format currency amount
     *
     * @param float $amount
     * @param string|null $currency
     * @return string
     */
    function format_currency($amount, $currency = null)
    {
        $currency = $currency ?: default_currency();

        switch (strtoupper($currency)) {
            case 'VND':
                return number_format($amount, 0) . ' ₫';
            case 'USD':
                return '$' . number_format($amount, 2);
            case 'EUR':
                return '€' . number_format($amount, 2);
            default:
                return number_format($amount, 2) . ' ' . $currency;
        }
    }
}

if (!function_exists('is_api_configured')) {
    /**
     * Check if API is configured
     *
     * @param string $apiType
     * @return bool
     */
    function is_api_configured($apiType)
    {
        switch ($apiType) {
            case 'google_sheets':
                return !empty(setting('google_sheets_api_key'));
            case 'ebay':
                return !empty(setting('ebay_api_key'));
            case 'exchange_rate':
                return !empty(setting('exchange_rate_api_key'));
            case 'smtp':
                return !empty(setting('smtp_host')) &&
                       !empty(setting('smtp_port')) &&
                       !empty(setting('smtp_username')) &&
                       !empty(setting('smtp_password'));
            default:
                return false;
        }
    }
}

if (!function_exists('get_timezone')) {
    /**
     * Get application timezone
     *
     * @return string
     */
    function get_timezone()
    {
        return setting('timezone', 'Asia/Ho_Chi_Minh');
    }
}

if (!function_exists('get_language')) {
    /**
     * Get default language
     *
     * @return string
     */
    function get_language()
    {
        return setting('default_language', 'vi');
    }
}

if (!function_exists('transaction_fee_percentage')) {
    /**
     * Get transaction fee percentage
     *
     * @return float
     */
    function transaction_fee_percentage()
    {
        return (float) setting('transaction_fee_percentage', 2.5);
    }
}

if (!function_exists('calculate_transaction_fee')) {
    /**
     * Calculate transaction fee
     *
     * @param float $amount
     * @return float
     */
    function calculate_transaction_fee($amount)
    {
        $percentage = transaction_fee_percentage();
        return ($amount * $percentage) / 100;
    }
}

if (!function_exists('notification_frequency')) {
    /**
     * Get notification frequency
     *
     * @return string
     */
    function notification_frequency()
    {
        return setting('notification_frequency', 'immediate');
    }
}

if (!function_exists('should_send_notification_now')) {
    /**
     * Check if notification should be sent now based on frequency
     *
     * @return bool
     */
    function should_send_notification_now()
    {
        $frequency = notification_frequency();

        switch ($frequency) {
            case 'immediate':
                return true;
            case 'hourly':
                return now()->minute === 0;
            case 'daily':
                return now()->hour === 9 && now()->minute === 0; // 9 AM
            case 'weekly':
                return now()->dayOfWeek === 1 && now()->hour === 9 && now()->minute === 0; // Monday 9 AM
            default:
                return true;
        }
    }
}

if (!function_exists('default_user_role')) {
    /**
     * Get default user role
     *
     * @return string
     */
    function default_user_role()
    {
        return setting('default_user_role', 'user');
    }
}

if (!function_exists('can_add_team_member')) {
    /**
     * Check if team can add more members
     *
     * @param \App\Models\Team $team
     * @return bool
     */
    function can_add_team_member($team)
    {
        $maxMembers = max_members_per_team();
        $currentMembers = $team->members()->count();

        return $currentMembers < $maxMembers;
    }
}

if (!function_exists('get_remaining_team_slots')) {
    /**
     * Get remaining team member slots
     *
     * @param \App\Models\Team $team
     * @return int
     */
    function get_remaining_team_slots($team)
    {
        $maxMembers = max_members_per_team();
        $currentMembers = $team->members()->count();

        return max(0, $maxMembers - $currentMembers);
    }
}

if (!function_exists('convert_to_default_currency')) {
    /**
     * Convert amount to default currency
     *
     * @param float $amount
     * @param string $fromCurrency
     * @return float
     */
    function convert_to_default_currency($amount, $fromCurrency)
    {
        $defaultCurrency = default_currency();

        if (strtoupper($fromCurrency) === strtoupper($defaultCurrency)) {
            return $amount;
        }

        // Simple conversion using default exchange rate
        if (strtoupper($fromCurrency) === 'USD' && strtoupper($defaultCurrency) === 'VND') {
            return $amount * default_exchange_rate();
        }

        if (strtoupper($fromCurrency) === 'VND' && strtoupper($defaultCurrency) === 'USD') {
            return $amount / default_exchange_rate();
        }

        // For other currencies, return as is (would need currency service)
        return $amount;
    }
}

if (!function_exists('get_smtp_config')) {
    /**
     * Get SMTP configuration array
     *
     * @return array
     */
    function get_smtp_config()
    {
        return [
            'host' => setting('smtp_host'),
            'port' => setting('smtp_port', 587),
            'username' => setting('smtp_username'),
            'password' => setting('smtp_password'),
            'encryption' => 'tls'
        ];
    }
}

if (!function_exists('is_settings_configured')) {
    /**
     * Check if basic settings are configured
     *
     * @return bool
     */
    function is_settings_configured()
    {
        $requiredSettings = [
            'site_name',
            'default_currency',
            'default_exchange_rate_usd_vnd',
            'timezone'
        ];

        foreach ($requiredSettings as $setting) {
            if (empty(setting($setting))) {
                return false;
            }
        }

        return true;
    }
}
