<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    protected $fillable = [
        'type',
        'user_id',
        'team_id',
        'title',
        'message',
        'data',
        'action_url',
        'priority',
        'read_at'
    ];

    protected $casts = [
        'data' => 'array',
        'read_at' => 'datetime'
    ];

    // Relationship với User
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Relationship với Team
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    // Scope để lấy notifications chưa đọc
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    // Scope để lấy notifications theo priority
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    // Scope để lấy notifications theo type
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Mark notification as read
    public function markAsRead()
    {
        $this->update(['read_at' => now()]);
    }

    // Check if notification is read
    public function isRead()
    {
        return !is_null($this->read_at);
    }

    // Get priority label
    public function getPriorityLabelAttribute()
    {
        $labels = [
            'low' => 'Thấp',
            'medium' => 'Trung bình',
            'high' => 'Cao',
            'urgent' => 'Khẩn cấp'
        ];

        return $labels[$this->priority] ?? $this->priority;
    }

    // Get priority color
    public function getPriorityColorAttribute()
    {
        $colors = [
            'low' => 'secondary',
            'medium' => 'info',
            'high' => 'warning',
            'urgent' => 'danger'
        ];

        return $colors[$this->priority] ?? 'secondary';
    }

    // Get time ago
    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }
}
