<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-table me-1"></i>
        Google Sheets API
    </h6>
    
    <div class="row">
        <div class="col-md-8">
            <div class="setting-item">
                <label for="google_sheets_api_key" class="form-label">
                    Google Sheets API Key
                    <span class="badge bg-warning text-dark ms-1">Encrypted</span>
                </label>
                <div class="input-group">
                    <input type="password" 
                           class="form-control <?php $__errorArgs = ['google_sheets_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="google_sheets_api_key" 
                           name="google_sheets_api_key" 
                           value="<?php echo e(old('google_sheets_api_key', $values['google_sheets_api_key'] ? '••••••••••••••••' : '')); ?>"
                           placeholder="Nhập Google Sheets API Key">
                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('google_sheets_api_key')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-text">
                    API Key để kết nối với Google Sheets. 
                    <a href="https://console.developers.google.com/" target="_blank">Lấy API Key</a>
                </div>
                <?php $__errorArgs = ['google_sheets_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="setting-item">
                <label class="form-label">Test kết nối</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary api-test-btn" onclick="testApi('google_sheets', this)">
                        <i class="fas fa-plug me-1"></i>
                        Test Google Sheets
                    </button>
                </div>
                <div class="test-result" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fab fa-ebay me-1"></i>
        eBay API
    </h6>
    
    <div class="row">
        <div class="col-md-8">
            <div class="setting-item">
                <label for="ebay_api_key" class="form-label">
                    eBay API Key
                    <span class="badge bg-warning text-dark ms-1">Encrypted</span>
                </label>
                <div class="input-group">
                    <input type="password" 
                           class="form-control <?php $__errorArgs = ['ebay_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="ebay_api_key" 
                           name="ebay_api_key" 
                           value="<?php echo e(old('ebay_api_key', $values['ebay_api_key'] ? '••••••••••••••••' : '')); ?>"
                           placeholder="Nhập eBay API Key">
                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('ebay_api_key')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-text">
                    API Key để tự động import payouts từ eBay. 
                    <a href="https://developer.ebay.com/" target="_blank">Lấy API Key</a>
                </div>
                <?php $__errorArgs = ['ebay_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="setting-item">
                <label class="form-label">Test kết nối</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary api-test-btn" onclick="testApi('ebay', this)">
                        <i class="fas fa-plug me-1"></i>
                        Test eBay API
                    </button>
                </div>
                <div class="test-result" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-exchange-alt me-1"></i>
        Exchange Rate API
    </h6>
    
    <div class="row">
        <div class="col-md-8">
            <div class="setting-item">
                <label for="exchange_rate_api_key" class="form-label">
                    Exchange Rate API Key
                    <span class="badge bg-warning text-dark ms-1">Encrypted</span>
                </label>
                <div class="input-group">
                    <input type="password" 
                           class="form-control <?php $__errorArgs = ['exchange_rate_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="exchange_rate_api_key" 
                           name="exchange_rate_api_key" 
                           value="<?php echo e(old('exchange_rate_api_key', $values['exchange_rate_api_key'] ? '••••••••••••••••' : '')); ?>"
                           placeholder="Nhập Exchange Rate API Key">
                    <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('exchange_rate_api_key')">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
                <div class="form-text">
                    API Key để lấy tỷ giá thời gian thực. 
                    <a href="https://exchangerate-api.com/" target="_blank">Lấy API Key</a>
                </div>
                <?php $__errorArgs = ['exchange_rate_api_key'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="setting-item">
                <label class="form-label">Test kết nối</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary api-test-btn" onclick="testApi('exchange_rate', this)">
                        <i class="fas fa-plug me-1"></i>
                        Test Exchange Rate
                    </button>
                </div>
                <div class="test-result" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-chart-bar me-1"></i>
        Trạng thái API
    </h6>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card border-left-primary">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">Google Sheets</h6>
                            <p class="card-text mb-0">
                                <?php if(!empty($values['google_sheets_api_key'])): ?>
                                    <span class="badge bg-success">Đã cấu hình</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Chưa cấu hình</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-table fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-left-success">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">eBay API</h6>
                            <p class="card-text mb-0">
                                <?php if(!empty($values['ebay_api_key'])): ?>
                                    <span class="badge bg-success">Đã cấu hình</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Chưa cấu hình</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="text-success">
                            <i class="fab fa-ebay fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-left-info">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">Exchange Rate</h6>
                            <p class="card-text mb-0">
                                <?php if(!empty($values['exchange_rate_api_key'])): ?>
                                    <span class="badge bg-success">Đã cấu hình</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Chưa cấu hình</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-tools me-1"></i>
        Công cụ API
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Test tất cả API</h6>
                </div>
                <div class="card-body">
                    <p class="card-text">Kiểm tra kết nối đến tất cả API đã cấu hình</p>
                    <button type="button" class="btn btn-primary" onclick="testAllApis()">
                        <i class="fas fa-play me-1"></i>
                        Test tất cả
                    </button>
                    <div id="allApiResults" class="mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Hướng dẫn cấu hình</h6>
                </div>
                <div class="card-body">
                    <div class="accordion" id="apiGuideAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#googleSheetsGuide">
                                    Google Sheets API
                                </button>
                            </h2>
                            <div id="googleSheetsGuide" class="accordion-collapse collapse" data-bs-parent="#apiGuideAccordion">
                                <div class="accordion-body">
                                    <ol class="small">
                                        <li>Truy cập <a href="https://console.developers.google.com/" target="_blank">Google Cloud Console</a></li>
                                        <li>Tạo project mới hoặc chọn project hiện có</li>
                                        <li>Enable Google Sheets API</li>
                                        <li>Tạo credentials (API Key)</li>
                                        <li>Copy API Key và paste vào field trên</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#ebayGuide">
                                    eBay API
                                </button>
                            </h2>
                            <div id="ebayGuide" class="accordion-collapse collapse" data-bs-parent="#apiGuideAccordion">
                                <div class="accordion-body">
                                    <ol class="small">
                                        <li>Truy cập <a href="https://developer.ebay.com/" target="_blank">eBay Developers</a></li>
                                        <li>Đăng ký tài khoản developer</li>
                                        <li>Tạo application mới</li>
                                        <li>Lấy App ID (Client ID)</li>
                                        <li>Copy App ID và paste vào field trên</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#exchangeRateGuide">
                                    Exchange Rate API
                                </button>
                            </h2>
                            <div id="exchangeRateGuide" class="accordion-collapse collapse" data-bs-parent="#apiGuideAccordion">
                                <div class="accordion-body">
                                    <ol class="small">
                                        <li>Truy cập <a href="https://exchangerate-api.com/" target="_blank">ExchangeRate-API</a></li>
                                        <li>Đăng ký tài khoản miễn phí</li>
                                        <li>Lấy API Key từ dashboard</li>
                                        <li>Copy API Key và paste vào field trên</li>
                                        <li>Free plan: 1500 requests/month</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function testAllApis() {
    const button = event.target;
    const originalText = button.innerHTML;
    const resultsDiv = document.getElementById('allApiResults');
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Testing...';
    button.disabled = true;
    resultsDiv.style.display = 'block';
    resultsDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Đang test các API...</div>';
    
    const apis = ['google_sheets', 'ebay', 'exchange_rate'];
    const results = [];
    let completed = 0;
    
    apis.forEach(function(apiType) {
        $.post('<?php echo e(route("admin.settings.test-api")); ?>', {
            _token: '<?php echo e(csrf_token()); ?>',
            api_type: apiType
        })
        .done(function(response) {
            results.push({
                api: apiType,
                success: response.success,
                message: response.message
            });
        })
        .fail(function() {
            results.push({
                api: apiType,
                success: false,
                message: 'Lỗi kết nối'
            });
        })
        .always(function() {
            completed++;
            if (completed === apis.length) {
                displayAllApiResults(results);
                button.innerHTML = originalText;
                button.disabled = false;
            }
        });
    });
}

function displayAllApiResults(results) {
    const resultsDiv = document.getElementById('allApiResults');
    let html = '<div class="list-group list-group-flush">';
    
    const apiNames = {
        'google_sheets': 'Google Sheets',
        'ebay': 'eBay API',
        'exchange_rate': 'Exchange Rate API'
    };
    
    results.forEach(function(result) {
        const statusClass = result.success ? 'success' : 'danger';
        const icon = result.success ? 'fas fa-check' : 'fas fa-times';
        
        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <strong>${apiNames[result.api]}</strong><br>
                    <small class="text-muted">${result.message}</small>
                </div>
                <span class="badge bg-${statusClass}">
                    <i class="${icon}"></i>
                </span>
            </div>
        `;
    });
    
    html += '</div>';
    resultsDiv.innerHTML = html;
}

// Clear password fields when they get focus (to allow new input)
document.querySelectorAll('input[type="password"]').forEach(function(input) {
    input.addEventListener('focus', function() {
        if (this.value === '••••••••••••••••') {
            this.value = '';
        }
    });
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #007bff !important;
}

.border-left-success {
    border-left: 0.25rem solid #28a745 !important;
}

.border-left-info {
    border-left: 0.25rem solid #17a2b8 !important;
}
</style>
<?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/admin/settings/partials/api.blade.php ENDPATH**/ ?>