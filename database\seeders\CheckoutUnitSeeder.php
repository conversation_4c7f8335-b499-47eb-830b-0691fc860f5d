<?php

namespace Database\Seeders;

use App\Models\CheckoutUnit;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CheckoutUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $checkoutUnits = [
            [
                'name' => 'Team Checkout A',
                'code' => 'TEAM_A',
                'description' => 'Đội checkout chuyên về thời trang và phụ kiện',
                'checkout_rate' => 15.00,
                'google_sheet_url' => 'https://docs.google.com/spreadsheets/d/1234567890/edit',
                'contact_person' => 'Nguyễn Văn A',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '0123456789',
                'status' => 'active'
            ],
            [
                'name' => 'Team Checkout B',
                'code' => 'TEAM_B',
                'description' => 'Đội checkout chuyên về điện tử và công nghệ',
                'checkout_rate' => 12.00,
                'google_sheet_url' => 'https://docs.google.com/spreadsheets/d/0987654321/edit',
                'contact_person' => 'Trần Thị B',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '0987654321',
                'status' => 'active'
            ],
            [
                'name' => 'Team Checkout C',
                'code' => 'TEAM_C',
                'description' => 'Đội checkout chuyên về gia dụng và nhà bếp',
                'checkout_rate' => 10.00,
                'google_sheet_url' => 'https://docs.google.com/spreadsheets/d/1122334455/edit',
                'contact_person' => 'Lê Văn C',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '0369852147',
                'status' => 'active'
            ],
            [
                'name' => 'Team Checkout D',
                'code' => 'TEAM_D',
                'description' => 'Đội checkout chuyên về sức khỏe và làm đẹp',
                'checkout_rate' => 18.00,
                'google_sheet_url' => 'https://docs.google.com/spreadsheets/d/5544332211/edit',
                'contact_person' => 'Phạm Thị D',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '0741852963',
                'status' => 'active'
            ],
            [
                'name' => 'Team Checkout E',
                'code' => 'TEAM_E',
                'description' => 'Đội checkout backup và hỗ trợ',
                'checkout_rate' => 8.00,
                'google_sheet_url' => null,
                'contact_person' => 'Hoàng Văn E',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '0159753486',
                'status' => 'inactive'
            ]
        ];

        foreach ($checkoutUnits as $unitData) {
            CheckoutUnit::create($unitData);
        }
    }
}
