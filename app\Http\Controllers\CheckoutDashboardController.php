<?php

namespace App\Http\Controllers;

use App\Models\CheckoutOrder;
use App\Models\CheckoutPayment;
use App\Models\CheckoutUnit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CheckoutDashboardController extends Controller
{
    public function index(Request $request)
    {
        // Lấy period từ request (week, month, year)
        $period = $request->get('period', 'month');
        $checkoutUnitId = $request->get('checkout_unit_id');

        // Thống kê tổng quan
        $stats = $this->getOverallStats($checkoutUnitId);

        // Thống kê theo đơn vị checkout
        $unitStats = $this->getUnitStats($checkoutUnitId);

        // Thống kê theo thời gian
        $timeStats = $this->getTimeStats($period, $checkoutUnitId);

        // Đơn hàng gần đây
        $recentOrders = $this->getRecentOrders($checkoutUnitId);

        // Top đơn vị checkout
        $topUnits = $this->getTopUnits();

        // Danh sách đơn vị checkout để filter
        $checkoutUnits = CheckoutUnit::active()->get();

        return view('checkout.dashboard', compact(
            'stats',
            'unitStats',
            'timeStats',
            'recentOrders',
            'topUnits',
            'checkoutUnits',
            'period',
            'checkoutUnitId'
        ));
    }

    private function getOverallStats($checkoutUnitId = null)
    {
        $query = CheckoutOrder::query();
        if ($checkoutUnitId) {
            $query->where('checkout_unit_id', $checkoutUnitId);
        }

        $totalOrders = $query->count();
        $completedOrders = $query->where('status', 'completed')->count();
        $problematicOrders = $query->whereIn('status', ['cancelled', 'returned', 'refunded'])->count();
        $totalCheckoutAmount = $query->sum('total_amount');

        $paymentQuery = CheckoutPayment::query();
        if ($checkoutUnitId) {
            $paymentQuery->where('checkout_unit_id', $checkoutUnitId);
        }

        $totalPaidAmount = $paymentQuery->where('status', 'completed')->sum('amount');
        $pendingPaymentAmount = max(0, $totalCheckoutAmount - $totalPaidAmount);

        return [
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrders,
            'problematic_orders' => $problematicOrders,
            'total_checkout_amount' => $totalCheckoutAmount,
            'total_paid_amount' => $totalPaidAmount,
            'pending_payment_amount' => $pendingPaymentAmount,
            'completion_rate' => $totalOrders > 0 ? round(($completedOrders / $totalOrders) * 100, 2) : 0,
            'problem_rate' => $totalOrders > 0 ? round(($problematicOrders / $totalOrders) * 100, 2) : 0,
        ];
    }

    private function getUnitStats($checkoutUnitId = null)
    {
        $query = CheckoutUnit::with(['checkoutOrders', 'checkoutPayments']);

        if ($checkoutUnitId) {
            $query->where('id', $checkoutUnitId);
        }

        return $query->active()->get()->map(function ($unit) {
            return [
                'id' => $unit->id,
                'name' => $unit->name,
                'code' => $unit->code,
                'checkout_rate' => $unit->checkout_rate,
                'total_orders' => $unit->total_orders,
                'completed_orders' => $unit->completed_orders,
                'problematic_orders' => $unit->problematic_orders,
                'total_checkout_amount' => $unit->total_checkout_amount,
                'total_paid_amount' => $unit->total_paid_amount,
                'pending_payment_amount' => $unit->pending_payment_amount,
                'completion_rate' => $unit->completion_rate,
                'problem_rate' => $unit->problem_rate,
            ];
        });
    }

    private function getTimeStats($period, $checkoutUnitId = null)
    {
        $now = Carbon::now();
        $data = [];

        switch ($period) {
            case 'week':
                // 7 ngày gần nhất
                for ($i = 6; $i >= 0; $i--) {
                    $date = $now->copy()->subDays($i);
                    $data[] = $this->getStatsForDate($date, $checkoutUnitId);
                }
                break;

            case 'month':
                // 30 ngày gần nhất
                for ($i = 29; $i >= 0; $i--) {
                    $date = $now->copy()->subDays($i);
                    $data[] = $this->getStatsForDate($date, $checkoutUnitId);
                }
                break;

            case 'year':
                // 12 tháng gần nhất
                for ($i = 11; $i >= 0; $i--) {
                    $date = $now->copy()->subMonths($i);
                    $data[] = $this->getStatsForMonth($date, $checkoutUnitId);
                }
                break;
        }

        return $data;
    }

    private function getStatsForDate($date, $checkoutUnitId = null)
    {
        $query = CheckoutOrder::whereDate('created_at', $date);
        if ($checkoutUnitId) {
            $query->where('checkout_unit_id', $checkoutUnitId);
        }

        $orders = $query->count();
        $completed = $query->where('status', 'completed')->count();
        $problematic = $query->whereIn('status', ['cancelled', 'returned', 'refunded'])->count();
        $amount = $query->sum('total_amount');

        return [
            'date' => $date->format('d/m'),
            'orders' => $orders,
            'completed' => $completed,
            'problematic' => $problematic,
            'amount' => $amount,
        ];
    }

    private function getStatsForMonth($date, $checkoutUnitId = null)
    {
        $query = CheckoutOrder::whereYear('created_at', $date->year)
                              ->whereMonth('created_at', $date->month);
        if ($checkoutUnitId) {
            $query->where('checkout_unit_id', $checkoutUnitId);
        }

        $orders = $query->count();
        $completed = $query->where('status', 'completed')->count();
        $problematic = $query->whereIn('status', ['cancelled', 'returned', 'refunded'])->count();
        $amount = $query->sum('total_amount');

        return [
            'date' => $date->format('m/Y'),
            'orders' => $orders,
            'completed' => $completed,
            'problematic' => $problematic,
            'amount' => $amount,
        ];
    }

    private function getRecentOrders($checkoutUnitId = null)
    {
        $query = CheckoutOrder::with(['checkoutUnit']);

        if ($checkoutUnitId) {
            $query->where('checkout_unit_id', $checkoutUnitId);
        }

        return $query->orderBy('created_at', 'desc')->limit(10)->get();
    }

    private function getTopUnits()
    {
        return CheckoutUnit::active()
            ->withCount(['checkoutOrders'])
            ->withSum(['checkoutOrders'], 'total_amount')
            ->orderBy('checkout_orders_sum_total_amount', 'desc')
            ->limit(5)
            ->get();
    }

    public function analytics(Request $request)
    {
        $user = auth()->user();
        $period = $request->get('period', 'month');
        $checkoutUnitId = $request->get('checkout_unit_id');
        $teamId = $request->get('team_id', $user->team_id);

        // Check permission
        if (!$user->isAdmin() && $user->team_id != $teamId) {
            abort(403, 'Unauthorized access to team analytics');
        }

        // Phân tích chi tiết theo status
        $statusAnalytics = $this->getStatusAnalytics($checkoutUnitId, $teamId);

        // Phân tích theo payment method
        $paymentMethodAnalytics = $this->getPaymentMethodAnalytics($checkoutUnitId, $teamId);

        // Trend analysis
        $trendAnalytics = $this->getTrendAnalytics($period, $checkoutUnitId, $teamId);

        $checkoutUnits = CheckoutUnit::active()->get();
        $teams = $user->isAdmin() ? \App\Models\Team::all() : collect();

        // Mock analytics data for now
        $analytics = [
            'total_orders' => 150,
            'success_rate' => 85.5,
            'total_revenue' => 45000000,
            'avg_order_value' => 300000,
            'status_breakdown' => [
                'completed' => 128,
                'pending' => 15,
                'failed' => 7
            ],
            'daily_orders' => [
                '01/12' => 12,
                '02/12' => 15,
                '03/12' => 18,
                '04/12' => 10,
                '05/12' => 22
            ]
        ];

        $teamStats = [
            [
                'team_name' => 'Team A',
                'total_orders' => 85,
                'successful_orders' => 72,
                'success_rate' => 84.7,
                'total_revenue' => 25500000,
                'avg_order_value' => 300000
            ],
            [
                'team_name' => 'Team B',
                'total_orders' => 65,
                'successful_orders' => 56,
                'success_rate' => 86.2,
                'total_revenue' => 19500000,
                'avg_order_value' => 300000
            ]
        ];

        return view('checkout.analytics', compact(
            'analytics',
            'teamStats',
            'teams',
            'checkoutUnits',
            'period',
            'checkoutUnitId',
            'teamId'
        ));
    }

    private function getStatusAnalytics($checkoutUnitId = null)
    {
        $query = CheckoutOrder::select('status', DB::raw('count(*) as count'), DB::raw('sum(total_amount) as amount'));

        if ($checkoutUnitId) {
            $query->where('checkout_unit_id', $checkoutUnitId);
        }

        return $query->groupBy('status')->get();
    }

    private function getPaymentMethodAnalytics($checkoutUnitId = null)
    {
        $query = CheckoutPayment::select('payment_method', DB::raw('count(*) as count'), DB::raw('sum(amount) as amount'));

        if ($checkoutUnitId) {
            $query->where('checkout_unit_id', $checkoutUnitId);
        }

        return $query->where('status', 'completed')->groupBy('payment_method')->get();
    }

    private function getTrendAnalytics($period, $checkoutUnitId = null)
    {
        // Tính toán xu hướng tăng trưởng
        $currentPeriodStats = $this->getTimeStats($period, $checkoutUnitId);

        // So sánh với period trước đó để tính growth rate
        // Implementation tùy thuộc vào yêu cầu cụ thể

        return [
            'current_period' => $currentPeriodStats,
            'growth_rate' => 0, // Placeholder
        ];
    }
}
