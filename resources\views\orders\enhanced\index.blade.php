@extends('layouts.app')

@section('title', 'Quản lý Đơn hàng')
@section('page-title', 'Quản lý Đơn hàng')

@section('page-actions')
<div class="btn-group" role="group">
    <a href="{{ route('orders.enhanced.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Tạo đơn hàng mới
    </a>
    <a href="{{ route('orders.enhanced.dashboard') }}" class="btn btn-outline-primary">
        <i class="fas fa-chart-pie me-1"></i>
        Dashboard
    </a>
    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#bulkActionModal">
        <i class="fas fa-tasks me-1"></i>
        T<PERSON> tác hàng loạt
    </button>
</div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Statistics Summary -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ number_format($stats['total_orders']) }}</h4>
                            <p class="mb-0">Tổng đơn hàng</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ number_format($stats['total_revenue'], 0, ',', '.') }}đ</h4>
                            <p class="mb-0">Tổng doanh thu</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ number_format($stats['total_profit'], 0, ',', '.') }}đ</h4>
                            <p class="mb-0">Tổng lợi nhuận</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ number_format($stats['avg_profit_margin'], 1) }}%</h4>
                            <p class="mb-0">Margin trung bình</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0">
                <i class="fas fa-filter me-1"></i>
                Bộ lọc nâng cao
                <button class="btn btn-sm btn-outline-secondary float-end" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </h6>
        </div>
        <div class="collapse" id="filterCollapse">
            <div class="card-body">
                <form method="GET" action="{{ route('orders.enhanced.index') }}" id="filterForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Tìm kiếm</label>
                            <input type="text" class="form-control" name="search" value="{{ request('search') }}" 
                                   placeholder="Mã đơn, Buyer ID, Tên khách...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Trạng thái</label>
                            <select class="form-select" name="status">
                                <option value="">Tất cả</option>
                                @foreach($filterOptions['statuses'] as $value => $label)
                                    <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Platform</label>
                            <select class="form-select" name="platform">
                                <option value="">Tất cả</option>
                                @foreach($filterOptions['platforms'] as $platform)
                                    <option value="{{ $platform }}" {{ request('platform') == $platform ? 'selected' : '' }}>
                                        {{ ucfirst($platform) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Tài khoản</label>
                            <select class="form-select" name="platform_account">
                                <option value="">Tất cả</option>
                                @foreach($filterOptions['accounts'] as $account)
                                    <option value="{{ $account }}" {{ request('platform_account') == $account ? 'selected' : '' }}>
                                        {{ $account }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Người xử lý</label>
                            <select class="form-select" name="assigned_to">
                                <option value="">Tất cả</option>
                                @foreach($filterOptions['users'] as $user)
                                    <option value="{{ $user->id }}" {{ request('assigned_to') == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-2">
                            <label class="form-label">Từ ngày</label>
                            <input type="date" class="form-control" name="date_from" value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Đến ngày</label>
                            <input type="date" class="form-control" name="date_to" value="{{ request('date_to') }}">
                        </div>
                        @if(auth()->user()->isAdmin())
                        <div class="col-md-2">
                            <label class="form-label">Team</label>
                            <select class="form-select" name="team_id">
                                <option value="">Tất cả</option>
                                @foreach($filterOptions['teams'] as $team)
                                    <option value="{{ $team->id }}" {{ request('team_id') == $team->id ? 'selected' : '' }}>
                                        {{ $team->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        @endif
                        <div class="col-md-2">
                            <label class="form-label">Lợi nhuận</label>
                            <select class="form-select" name="is_profitable">
                                <option value="">Tất cả</option>
                                <option value="1" {{ request('is_profitable') === '1' ? 'selected' : '' }}>Có lợi nhuận</option>
                                <option value="0" {{ request('is_profitable') === '0' ? 'selected' : '' }}>Không lợi nhuận</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>
                                Lọc
                            </button>
                            <a href="{{ route('orders.enhanced.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>
                                Xóa bộ lọc
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Danh sách đơn hàng ({{ $orders->total() }} đơn)</h6>
            <div class="btn-group btn-group-sm">
                <button type="button" class="btn btn-outline-secondary" onclick="selectAll()">
                    <i class="fas fa-check-square me-1"></i>
                    Chọn tất cả
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearSelection()">
                    <i class="fas fa-square me-1"></i>
                    Bỏ chọn
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th>Mã đơn</th>
                            <th>Ngày</th>
                            <th>Platform</th>
                            <th>Buyer ID</th>
                            <th>Khách hàng</th>
                            <th>SKU</th>
                            <th>Giá gốc</th>
                            <th>Giá bán</th>
                            <th>Lợi nhuận</th>
                            <th>Trạng thái</th>
                            <th>Người xử lý</th>
                            <th width="120">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($orders as $order)
                        <tr>
                            <td>
                                <input type="checkbox" class="order-checkbox" value="{{ $order->id }}">
                            </td>
                            <td>
                                <a href="{{ route('orders.enhanced.show', $order) }}" class="text-decoration-none fw-bold">
                                    {{ $order->order_number }}
                                </a>
                                @if($order->external_order_id)
                                    <br><small class="text-muted">{{ $order->external_order_id }}</small>
                                @endif
                            </td>
                            <td>{{ $order->created_at->format('d/m/Y H:i') }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="{{ $order->platform_icon ?? 'fas fa-shopping-cart' }} me-1"></i>
                                    <div>
                                        <div>{{ ucfirst($order->platform) }}</div>
                                        <small class="text-muted">{{ $order->platform_account }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ $order->buyer_id }}</td>
                            <td>
                                <div>{{ $order->customer_name }}</div>
                                <small class="text-muted">{{ $order->customer_email }}</small>
                            </td>
                            <td>
                                @foreach($order->orderItems as $item)
                                    <div>{{ $item->product_sku }}</div>
                                    <small class="text-muted">SL: {{ $item->quantity }}</small>
                                @endforeach
                            </td>
                            <td>{{ number_format($order->cost_amount, 0, ',', '.') }}đ</td>
                            <td>{{ number_format($order->total_amount, 0, ',', '.') }}đ</td>
                            <td>
                                <span class="text-{{ $order->profit_amount > 0 ? 'success' : 'danger' }} fw-bold">
                                    {{ number_format($order->profit_amount, 0, ',', '.') }}đ
                                </span>
                                @if($order->profit_margin)
                                    <br><small class="text-muted">{{ number_format($order->profit_margin, 1) }}%</small>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $order->status_badge }}">
                                    {{ $filterOptions['statuses'][$order->status] ?? ucfirst($order->status) }}
                                </span>
                            </td>
                            <td>
                                @if($order->assigned_to)
                                    {{ $order->assignedUser->name ?? 'N/A' }}
                                @else
                                    <span class="text-muted">Chưa giao</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('orders.enhanced.show', $order) }}" class="btn btn-outline-primary" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('orders.enhanced.edit', $order) }}" class="btn btn-outline-secondary" title="Chỉnh sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-danger" onclick="deleteOrder({{ $order->id }})" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="13" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-3x mb-3"></i>
                                <br>Không tìm thấy đơn hàng nào
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($orders->hasPages())
        <div class="card-footer">
            {{ $orders->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thao tác hàng loạt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="bulkActionForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Chọn thao tác</label>
                        <select class="form-select" name="action" id="bulkAction" required>
                            <option value="">-- Chọn thao tác --</option>
                            <option value="update_status">Cập nhật trạng thái</option>
                            <option value="assign_to">Giao việc</option>
                            <option value="update_platform_account">Cập nhật tài khoản platform</option>
                        </select>
                    </div>
                    
                    <div class="mb-3 d-none" id="statusField">
                        <label class="form-label">Trạng thái mới</label>
                        <select class="form-select" name="status">
                            @foreach($filterOptions['statuses'] as $value => $label)
                                <option value="{{ $value }}">{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="mb-3 d-none" id="assignField">
                        <label class="form-label">Giao cho</label>
                        <select class="form-select" name="assigned_to">
                            @foreach($filterOptions['users'] as $user)
                                <option value="{{ $user->id }}">{{ $user->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="mb-3 d-none" id="accountField">
                        <label class="form-label">Tài khoản platform</label>
                        <input type="text" class="form-control" name="platform_account" placeholder="Nhập tài khoản platform">
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        Đã chọn <span id="selectedCount">0</span> đơn hàng
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Thực hiện</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
let selectedOrders = [];

// Toggle select all
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.order-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedOrders();
}

// Select all
function selectAll() {
    document.getElementById('selectAllCheckbox').checked = true;
    toggleSelectAll();
}

// Clear selection
function clearSelection() {
    document.getElementById('selectAllCheckbox').checked = false;
    toggleSelectAll();
}

// Update selected orders
function updateSelectedOrders() {
    selectedOrders = Array.from(document.querySelectorAll('.order-checkbox:checked')).map(cb => cb.value);
    document.getElementById('selectedCount').textContent = selectedOrders.length;
}

// Listen to checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('order-checkbox')) {
        updateSelectedOrders();
    }
});

// Bulk action form handling
document.getElementById('bulkAction').addEventListener('change', function() {
    const action = this.value;
    
    // Hide all fields
    document.querySelectorAll('#statusField, #assignField, #accountField').forEach(field => {
        field.classList.add('d-none');
    });
    
    // Show relevant field
    if (action === 'update_status') {
        document.getElementById('statusField').classList.remove('d-none');
    } else if (action === 'assign_to') {
        document.getElementById('assignField').classList.remove('d-none');
    } else if (action === 'update_platform_account') {
        document.getElementById('accountField').classList.remove('d-none');
    }
});

// Submit bulk action
document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (selectedOrders.length === 0) {
        alert('Vui lòng chọn ít nhất một đơn hàng');
        return;
    }
    
    const formData = new FormData(this);
    formData.append('order_ids', JSON.stringify(selectedOrders));
    
    fetch('{{ route("orders.enhanced.bulk-update") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Có lỗi xảy ra: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi thực hiện thao tác');
    });
});

// Delete order
function deleteOrder(orderId) {
    if (confirm('Bạn có chắc chắn muốn xóa đơn hàng này?')) {
        fetch(`/orders/enhanced/${orderId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra khi xóa đơn hàng');
            }
        });
    }
}
</script>
@endpush
@endsection
