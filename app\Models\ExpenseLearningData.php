<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExpenseLearningData extends Model
{
    protected $fillable = [
        'description',
        'amount',
        'vendor',
        'team_id',
        'suggested_category',
        'actual_category',
        'confidence',
        'correction_reason',
        'metadata'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'confidence' => 'decimal:2',
        'metadata' => 'array'
    ];

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }
}
