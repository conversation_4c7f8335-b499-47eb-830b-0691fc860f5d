<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Supplier extends Model
{
    protected $fillable = [
        'name',
        'company_name',
        'email',
        'phone',
        'address',
        'website',
        'description',
        'status',
        'commission_rate'
    ];

    protected $casts = [
        'commission_rate' => 'decimal:2'
    ];

    // Relationship với Products
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    // Scope để lấy các supplier đang active
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Tính tổng số sản phẩm của supplier
    public function getTotalProductsAttribute()
    {
        return $this->products()->count();
    }

    // Tính tổng giá trị đơn hàng từ supplier này
    public function getTotalOrderValueAttribute()
    {
        return $this->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->sum('order_items.total_price');
    }
}
