<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-percentage me-1"></i>
        Phí checkout
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="default_checkout_fee_percentage" class="form-label">
                    Tỷ lệ phí checkout mặc định (%) <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                    <input type="number" 
                           class="form-control @error('default_checkout_fee_percentage') is-invalid @enderror" 
                           id="default_checkout_fee_percentage" 
                           name="default_checkout_fee_percentage" 
                           value="{{ old('default_checkout_fee_percentage', $values['default_checkout_fee_percentage'] ?? '15') }}"
                           min="0"
                           max="100"
                           step="0.1"
                           required>
                    <span class="input-group-text">%</span>
                </div>
                <div class="form-text">Phí checkout mặc định cho các đơn hàng mới</div>
                @error('default_checkout_fee_percentage')
                    <div class="invalid-feedback">{{ $message }}</div>
                @enderror
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label class="form-label">Ví dụ tính phí checkout</label>
                <div class="card bg-light">
                    <div class="card-body">
                        <div id="checkoutFeeExample">
                            @php
                                $exampleOrderValue = 500000; // 500k VND
                                $checkoutFeePercentage = $values['default_checkout_fee_percentage'] ?? 15;
                                $checkoutFee = ($exampleOrderValue * $checkoutFeePercentage) / 100;
                                $totalAmount = $exampleOrderValue + $checkoutFee;
                            @endphp
                            <small>
                                <strong>Giá trị đơn hàng:</strong> {{ number_format($exampleOrderValue) }} VND<br>
                                <strong>Phí checkout ({{ $checkoutFeePercentage }}%):</strong> {{ number_format($checkoutFee) }} VND<br>
                                <strong>Tổng khách phải trả:</strong> {{ number_format($totalAmount) }} VND
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-list me-1"></i>
        Trạng thái đơn hàng
    </h6>
    
    <div class="row">
        <div class="col-md-12">
            <div class="setting-item">
                <label for="checkout_order_statuses" class="form-label">
                    Danh sách trạng thái đơn hàng checkout <span class="text-danger">*</span>
                </label>
                <div id="statusList">
                    @php
                        $statuses = $values['checkout_order_statuses'] ?? [];
                        if (is_string($statuses)) {
                            $statuses = json_decode($statuses, true);
                        }
                        if (empty($statuses)) {
                            $statuses = [
                                'pending' => 'Chờ xử lý',
                                'confirmed' => 'Đã xác nhận',
                                'processing' => 'Đang xử lý',
                                'shipped' => 'Đã gửi hàng',
                                'delivered' => 'Đã giao hàng',
                                'completed' => 'Hoàn thành',
                                'cancelled' => 'Đã hủy',
                                'returned' => 'Đã trả hàng',
                                'refunded' => 'Đã hoàn tiền'
                            ];
                        }
                    @endphp
                    
                    @foreach($statuses as $key => $label)
                    <div class="status-item mb-2" data-status="{{ $key }}">
                        <div class="card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <label class="form-label">Mã trạng thái</label>
                                        <input type="text" class="form-control status-key" value="{{ $key }}" readonly>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Tên hiển thị</label>
                                        <input type="text" class="form-control status-label" value="{{ $label }}" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="d-grid">
                                            @if(!in_array($key, ['pending', 'completed', 'cancelled']))
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeStatus('{{ $key }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @else
                                            <button type="button" class="btn btn-outline-secondary btn-sm" disabled title="Trạng thái bắt buộc">
                                                <i class="fas fa-lock"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-primary" onclick="addNewStatus()">
                        <i class="fas fa-plus me-1"></i>
                        Thêm trạng thái mới
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetStatusesToDefault()">
                        <i class="fas fa-undo me-1"></i>
                        Reset về mặc định
                    </button>
                </div>
                
                <input type="hidden" name="checkout_order_statuses" id="checkout_order_statuses_input">
                
                <div class="form-text mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    Các trạng thái "Chờ xử lý", "Hoàn thành" và "Đã hủy" là bắt buộc và không thể xóa.
                </div>
                @error('checkout_order_statuses')
                    <div class="invalid-feedback d-block">{{ $message }}</div>
                @enderror
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-chart-line me-1"></i>
        Thống kê checkout
    </h6>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h6 class="card-title text-primary">Phí checkout</h6>
                    <p class="card-text">
                        <strong class="h5">{{ $values['default_checkout_fee_percentage'] ?? 15 }}%</strong><br>
                        <small class="text-muted">Mặc định</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h6 class="card-title text-success">Trạng thái</h6>
                    <p class="card-text">
                        <strong class="h5">{{ count($statuses) }}</strong><br>
                        <small class="text-muted">Tổng số</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h6 class="card-title text-info">Checkout units</h6>
                    <p class="card-text">
                        <strong class="h5">{{ \App\Models\CheckoutUnit::count() }}</strong><br>
                        <small class="text-muted">Đang hoạt động</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h6 class="card-title text-warning">Đơn hàng</h6>
                    <p class="card-text">
                        <strong class="h5">{{ \App\Models\CheckoutOrder::count() }}</strong><br>
                        <small class="text-muted">Tổng số</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-tools me-1"></i>
        Công cụ quản lý
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Tính toán phí checkout</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Giá trị đơn hàng (VND)</label>
                        <input type="number" class="form-control" id="orderValueCalculator" placeholder="Nhập giá trị đơn hàng" min="0" step="1000">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Phí checkout</label>
                        <input type="text" class="form-control" id="calculatedCheckoutFee" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tổng khách phải trả</label>
                        <input type="text" class="form-control" id="totalCustomerPayment" readonly>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" onclick="calculateCheckoutFee()">
                        <i class="fas fa-calculator me-1"></i>
                        Tính toán
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Workflow trạng thái</h6>
                </div>
                <div class="card-body">
                    <div class="status-workflow">
                        <div class="d-flex flex-wrap gap-2">
                            @foreach($statuses as $key => $label)
                            <span class="badge bg-secondary status-badge" data-status="{{ $key }}">
                                {{ $label }}
                            </span>
                            @if(!$loop->last)
                                <i class="fas fa-arrow-right text-muted align-self-center"></i>
                            @endif
                            @endforeach
                        </div>
                        <small class="text-muted mt-2 d-block">
                            Workflow mặc định cho đơn hàng checkout. Có thể tùy chỉnh theo nhu cầu.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update checkout fee example when percentage changes
document.getElementById('default_checkout_fee_percentage').addEventListener('input', function() {
    updateCheckoutFeeExample();
});

function updateCheckoutFeeExample() {
    const feePercentage = parseFloat(document.getElementById('default_checkout_fee_percentage').value) || 0;
    const exampleOrderValue = 500000; // 500k VND
    const checkoutFee = (exampleOrderValue * feePercentage) / 100;
    const totalAmount = exampleOrderValue + checkoutFee;
    
    document.getElementById('checkoutFeeExample').innerHTML = `
        <small>
            <strong>Giá trị đơn hàng:</strong> ${exampleOrderValue.toLocaleString()} VND<br>
            <strong>Phí checkout (${feePercentage}%):</strong> ${checkoutFee.toLocaleString()} VND<br>
            <strong>Tổng khách phải trả:</strong> ${totalAmount.toLocaleString()} VND
        </small>
    `;
}

function calculateCheckoutFee() {
    const orderValue = parseFloat(document.getElementById('orderValueCalculator').value) || 0;
    const feePercentage = parseFloat(document.getElementById('default_checkout_fee_percentage').value) || 0;
    const checkoutFee = (orderValue * feePercentage) / 100;
    const total = orderValue + checkoutFee;
    
    document.getElementById('calculatedCheckoutFee').value = checkoutFee.toLocaleString() + ' VND';
    document.getElementById('totalCustomerPayment').value = total.toLocaleString() + ' VND';
}

// Auto-calculate when order value changes
document.getElementById('orderValueCalculator').addEventListener('input', calculateCheckoutFee);

function addNewStatus() {
    const statusKey = prompt('Nhập mã trạng thái (chỉ chữ thường, số và dấu gạch dưới):');
    if (!statusKey) return;
    
    // Validate status key
    if (!/^[a-z0-9_]+$/.test(statusKey)) {
        alert('Mã trạng thái chỉ được chứa chữ thường, số và dấu gạch dưới');
        return;
    }
    
    // Check if status already exists
    if (document.querySelector(`[data-status="${statusKey}"]`)) {
        alert('Mã trạng thái đã tồn tại');
        return;
    }
    
    const statusLabel = prompt('Nhập tên hiển thị cho trạng thái:');
    if (!statusLabel) return;
    
    const statusList = document.getElementById('statusList');
    const statusItem = document.createElement('div');
    statusItem.className = 'status-item mb-2';
    statusItem.setAttribute('data-status', statusKey);
    
    statusItem.innerHTML = `
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <label class="form-label">Mã trạng thái</label>
                        <input type="text" class="form-control status-key" value="${statusKey}" readonly>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Tên hiển thị</label>
                        <input type="text" class="form-control status-label" value="${statusLabel}" required>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeStatus('${statusKey}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    statusList.appendChild(statusItem);
    updateStatusesInput();
}

function removeStatus(statusKey) {
    if (['pending', 'completed', 'cancelled'].includes(statusKey)) {
        alert('Không thể xóa trạng thái bắt buộc');
        return;
    }
    
    if (confirm('Bạn có chắc chắn muốn xóa trạng thái này?')) {
        document.querySelector(`[data-status="${statusKey}"]`).remove();
        updateStatusesInput();
    }
}

function resetStatusesToDefault() {
    if (!confirm('Bạn có chắc chắn muốn reset tất cả trạng thái về mặc định?')) {
        return;
    }
    
    const defaultStatuses = {
        'pending': 'Chờ xử lý',
        'confirmed': 'Đã xác nhận',
        'processing': 'Đang xử lý',
        'shipped': 'Đã gửi hàng',
        'delivered': 'Đã giao hàng',
        'completed': 'Hoàn thành',
        'cancelled': 'Đã hủy',
        'returned': 'Đã trả hàng',
        'refunded': 'Đã hoàn tiền'
    };
    
    const statusList = document.getElementById('statusList');
    statusList.innerHTML = '';
    
    Object.entries(defaultStatuses).forEach(([key, label]) => {
        const isRequired = ['pending', 'completed', 'cancelled'].includes(key);
        const statusItem = document.createElement('div');
        statusItem.className = 'status-item mb-2';
        statusItem.setAttribute('data-status', key);
        
        statusItem.innerHTML = `
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label class="form-label">Mã trạng thái</label>
                            <input type="text" class="form-control status-key" value="${key}" readonly>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Tên hiển thị</label>
                            <input type="text" class="form-control status-label" value="${label}" required>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                ${isRequired ? 
                                    `<button type="button" class="btn btn-outline-secondary btn-sm" disabled title="Trạng thái bắt buộc">
                                        <i class="fas fa-lock"></i>
                                    </button>` :
                                    `<button type="button" class="btn btn-outline-danger btn-sm" onclick="removeStatus('${key}')">
                                        <i class="fas fa-trash"></i>
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        statusList.appendChild(statusItem);
    });
    
    updateStatusesInput();
}

function updateStatusesInput() {
    const statuses = {};
    document.querySelectorAll('.status-item').forEach(item => {
        const key = item.querySelector('.status-key').value;
        const label = item.querySelector('.status-label').value;
        if (key && label) {
            statuses[key] = label;
        }
    });
    
    document.getElementById('checkout_order_statuses_input').value = JSON.stringify(statuses);
}

// Update statuses input when labels change
document.addEventListener('input', function(e) {
    if (e.target.classList.contains('status-label')) {
        updateStatusesInput();
    }
});

// Initialize statuses input on page load
document.addEventListener('DOMContentLoaded', function() {
    updateStatusesInput();
});
</script>
