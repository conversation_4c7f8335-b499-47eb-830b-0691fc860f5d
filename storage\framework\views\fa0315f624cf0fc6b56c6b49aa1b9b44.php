<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-globe me-1"></i>
        Thông tin website
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="site_name" class="form-label">
                    Tên website <span class="text-danger">*</span>
                </label>
                <input type="text" 
                       class="form-control <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="site_name" 
                       name="site_name" 
                       value="<?php echo e(old('site_name', $values['site_name'] ?? '')); ?>"
                       required>
                <div class="form-text">Tên hiển thị của website trong header và title</div>
                <?php $__errorArgs = ['site_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label for="site_logo" class="form-label">Logo website</label>
                <input type="url" 
                       class="form-control <?php $__errorArgs = ['site_logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       id="site_logo" 
                       name="site_logo" 
                       value="<?php echo e(old('site_logo', $values['site_logo'] ?? '')); ?>"
                       placeholder="https://example.com/logo.png">
                <div class="form-text">URL của logo website (để trống nếu không có)</div>
                <?php $__errorArgs = ['site_logo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-clock me-1"></i>
        Múi giờ và ngôn ngữ
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="timezone" class="form-label">
                    Múi giờ mặc định <span class="text-danger">*</span>
                </label>
                <select class="form-select <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        id="timezone" 
                        name="timezone" 
                        required>
                    <?php
                        $timezones = [
                            'Asia/Ho_Chi_Minh' => 'Việt Nam (UTC+7)',
                            'UTC' => 'UTC (UTC+0)',
                            'America/New_York' => 'New York (UTC-5)',
                            'Europe/London' => 'London (UTC+0)',
                            'Asia/Tokyo' => 'Tokyo (UTC+9)',
                            'Asia/Shanghai' => 'Shanghai (UTC+8)',
                            'Australia/Sydney' => 'Sydney (UTC+10)'
                        ];
                        $currentTimezone = old('timezone', $values['timezone'] ?? 'Asia/Ho_Chi_Minh');
                    ?>
                    <?php $__currentLoopData = $timezones; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($value); ?>" <?php echo e($currentTimezone === $value ? 'selected' : ''); ?>>
                            <?php echo e($label); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <div class="form-text">Múi giờ sẽ được áp dụng cho toàn bộ hệ thống</div>
                <?php $__errorArgs = ['timezone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label for="default_language" class="form-label">
                    Ngôn ngữ mặc định <span class="text-danger">*</span>
                </label>
                <select class="form-select <?php $__errorArgs = ['default_language'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        id="default_language" 
                        name="default_language" 
                        required>
                    <?php
                        $languages = [
                            'vi' => 'Tiếng Việt',
                            'en' => 'English'
                        ];
                        $currentLanguage = old('default_language', $values['default_language'] ?? 'vi');
                    ?>
                    <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($value); ?>" <?php echo e($currentLanguage === $value ? 'selected' : ''); ?>>
                            <?php echo e($label); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <div class="form-text">Ngôn ngữ hiển thị mặc định cho người dùng mới</div>
                <?php $__errorArgs = ['default_language'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-info-circle me-1"></i>
        Thông tin hiện tại
    </h6>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h6 class="card-title">Múi giờ hiện tại</h6>
                    <p class="card-text">
                        <strong><?php echo e(now()->setTimezone($values['timezone'] ?? 'Asia/Ho_Chi_Minh')->format('H:i:s')); ?></strong><br>
                        <small class="text-muted"><?php echo e(now()->setTimezone($values['timezone'] ?? 'Asia/Ho_Chi_Minh')->format('d/m/Y')); ?></small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h6 class="card-title">Phiên bản hệ thống</h6>
                    <p class="card-text">
                        <strong>v1.0.0</strong><br>
                        <small class="text-muted">Dropship Manager</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h6 class="card-title">Cập nhật cuối</h6>
                    <p class="card-text">
                        <?php if(isset($settings) && $settings->isNotEmpty()): ?>
                            <?php
                                $lastUpdated = $settings->where('updated_at', '!=', null)->sortByDesc('updated_at')->first();
                            ?>
                            <?php if($lastUpdated): ?>
                                <strong><?php echo e($lastUpdated->updated_at->format('H:i')); ?></strong><br>
                                <small class="text-muted"><?php echo e($lastUpdated->updated_at->format('d/m/Y')); ?></small>
                            <?php else: ?>
                                <strong>Chưa có</strong><br>
                                <small class="text-muted">Cập nhật đầu tiên</small>
                            <?php endif; ?>
                        <?php else: ?>
                            <strong>Chưa có</strong><br>
                            <small class="text-muted">Cập nhật đầu tiên</small>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-tools me-1"></i>
        Công cụ quản lý
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="d-grid">
                <button type="button" class="btn btn-outline-primary" onclick="initializeDefaults()">
                    <i class="fas fa-magic me-1"></i>
                    Khởi tạo settings mặc định
                </button>
                <small class="form-text text-muted mt-1">
                    Tạo các settings mặc định nếu chưa có (không ghi đè settings hiện tại)
                </small>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="d-grid">
                <button type="button" class="btn btn-outline-info" onclick="previewSettings()">
                    <i class="fas fa-eye me-1"></i>
                    Preview cấu hình
                </button>
                <small class="form-text text-muted mt-1">
                    Xem trước cách cấu hình sẽ hiển thị trên website
                </small>
            </div>
        </div>
    </div>
</div>

<script>
function initializeDefaults() {
    if (!confirm('Bạn có chắc chắn muốn khởi tạo settings mặc định? Điều này sẽ tạo các settings chưa có nhưng không ghi đè settings hiện tại.')) {
        return;
    }
    
    $.post('<?php echo e(route("admin.settings.initialize-defaults")); ?>', {
        _token: '<?php echo e(csrf_token()); ?>'
    })
    .done(function(response) {
        location.reload();
    })
    .fail(function() {
        showAlert('error', 'Lỗi khởi tạo settings mặc định');
    });
}

function previewSettings() {
    const siteName = document.getElementById('site_name').value || 'Dropship Manager';
    const siteLogo = document.getElementById('site_logo').value;
    const timezone = document.getElementById('timezone').value;
    const language = document.getElementById('default_language').value;
    
    let previewHtml = `
        <div class="modal fade" id="previewModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Preview cấu hình</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex align-items-center">
                                    ${siteLogo ? `<img src="${siteLogo}" alt="Logo" style="height: 30px; margin-right: 10px;">` : ''}
                                    <h6 class="mb-0">${siteName}</h6>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Thông tin cấu hình:</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>Tên website:</strong> ${siteName}</li>
                                            <li><strong>Logo:</strong> ${siteLogo || 'Không có'}</li>
                                            <li><strong>Múi giờ:</strong> ${timezone}</li>
                                            <li><strong>Ngôn ngữ:</strong> ${language === 'vi' ? 'Tiếng Việt' : 'English'}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Thời gian hiện tại:</h6>
                                        <p class="h4 text-primary" id="currentTime"></p>
                                        <small class="text-muted">Theo múi giờ đã chọn</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    $('#previewModal').remove();
    
    // Add modal to body
    $('body').append(previewHtml);
    
    // Show modal
    $('#previewModal').modal('show');
    
    // Update time every second
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('vi-VN', {
            timeZone: timezone,
            hour12: false
        });
        const dateString = now.toLocaleDateString('vi-VN', {
            timeZone: timezone
        });
        
        $('#currentTime').html(`${timeString}<br><small>${dateString}</small>`);
    }
    
    updateTime();
    const timeInterval = setInterval(updateTime, 1000);
    
    // Clear interval when modal is closed
    $('#previewModal').on('hidden.bs.modal', function() {
        clearInterval(timeInterval);
        $(this).remove();
    });
}
</script>
<?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/admin/settings/partials/general.blade.php ENDPATH**/ ?>