<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> c<PERSON><PERSON>huậ<PERSON> - Dropship Manager'); ?>
<?php $__env->startSection('page-title', 'Báo cáo Lợi nhuận'); ?>

<?php $__env->startSection('content'); ?>
<!-- Filter Controls -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('finance.profit-report')); ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Từ tháng</label>
                <input type="month" name="month_from" class="form-control" value="<?php echo e(request('month_from', now()->subMonths(11)->format('Y-m'))); ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label"><PERSON><PERSON><PERSON> tháng</label>
                <input type="month" name="month_to" class="form-control" value="<?php echo e(request('month_to', now()->format('Y-m'))); ?>">
            </div>
            <?php if(auth()->user()->isAdmin()): ?>
            <div class="col-md-3">
                <label class="form-label">Team</label>
                <select name="team_id" class="form-select">
                    <option value="">Tất cả teams</option>
                    <?php $__currentLoopData = $teams ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($team->id); ?>" <?php echo e(request('team_id') == $team->id ? 'selected' : ''); ?>>
                            <?php echo e($team->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <?php endif; ?>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> Tạo báo cáo
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Tổng Payouts
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo e(number_format($summary['total_payouts'] ?? 0)); ?>đ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Tổng Chi phí
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo e(number_format($summary['total_expenses'] ?? 0)); ?>đ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-<?php echo e(($summary['profit'] ?? 0) >= 0 ? 'success' : 'danger'); ?> shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-<?php echo e(($summary['profit'] ?? 0) >= 0 ? 'success' : 'danger'); ?> text-uppercase mb-1">
                            Lợi nhuận
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo e(number_format($summary['profit'] ?? 0)); ?>đ
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Tỷ suất LN
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo e(number_format($summary['profit_margin'] ?? 0, 1)); ?>%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Monthly Profit Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Biểu đồ Lợi nhuận theo tháng</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="exportChart('profit-chart')">Xuất PNG</a>
                        <a class="dropdown-item" href="#" onclick="printChart('profit-chart')">In biểu đồ</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="profitChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Profit Distribution -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Phân bổ theo Team</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="teamChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Details Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">Chi tiết theo tháng</h6>
        <div class="btn-group">
            <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> Xuất Excel
            </button>
            <button type="button" class="btn btn-info btn-sm" onclick="exportToPDF()">
                <i class="fas fa-file-pdf"></i> Xuất PDF
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered" width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>Tháng</th>
                        <th>Payouts (đ)</th>
                        <th>Chi phí (đ)</th>
                        <th>Lợi nhuận (đ)</th>
                        <th>Tỷ suất LN (%)</th>
                        <th>So với tháng trước</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $monthlyData ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><strong><?php echo e($data['month']); ?></strong></td>
                        <td class="text-success"><?php echo e(number_format($data['payouts'])); ?></td>
                        <td class="text-danger"><?php echo e(number_format($data['expenses'])); ?></td>
                        <td class="text-<?php echo e($data['profit'] >= 0 ? 'success' : 'danger'); ?>">
                            <strong><?php echo e(number_format($data['profit'])); ?></strong>
                        </td>
                        <td><?php echo e(number_format($data['profit_margin'], 1)); ?>%</td>
                        <td>
                            <?php if($data['growth'] > 0): ?>
                                <span class="text-success">
                                    <i class="fas fa-arrow-up"></i> <?php echo e(number_format($data['growth'], 1)); ?>%
                                </span>
                            <?php elseif($data['growth'] < 0): ?>
                                <span class="text-danger">
                                    <i class="fas fa-arrow-down"></i> <?php echo e(number_format(abs($data['growth']), 1)); ?>%
                                </span>
                            <?php else: ?>
                                <span class="text-muted">
                                    <i class="fas fa-minus"></i> 0%
                                </span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="6" class="text-center">Không có dữ liệu trong khoảng thời gian này</td>
                    </tr>
                    <?php endif; ?>
                </tbody>
                <?php if(count($monthlyData ?? []) > 0): ?>
                <tfoot>
                    <tr class="table-active">
                        <th>Tổng cộng</th>
                        <th class="text-success"><?php echo e(number_format($summary['total_payouts'] ?? 0)); ?></th>
                        <th class="text-danger"><?php echo e(number_format($summary['total_expenses'] ?? 0)); ?></th>
                        <th class="text-<?php echo e(($summary['profit'] ?? 0) >= 0 ? 'success' : 'danger'); ?>">
                            <strong><?php echo e(number_format($summary['profit'] ?? 0)); ?></strong>
                        </th>
                        <th><?php echo e(number_format($summary['profit_margin'] ?? 0, 1)); ?>%</th>
                        <th>-</th>
                    </tr>
                </tfoot>
                <?php endif; ?>
            </table>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Profit Chart
const profitCtx = document.getElementById('profitChart').getContext('2d');
new Chart(profitCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode(array_column($monthlyData ?? [], 'month')); ?>,
        datasets: [{
            label: 'Payouts',
            data: <?php echo json_encode(array_column($monthlyData ?? [], 'payouts')); ?>,
            borderColor: '#1cc88a',
            backgroundColor: 'rgba(28, 200, 138, 0.1)',
            fill: false
        }, {
            label: 'Chi phí',
            data: <?php echo json_encode(array_column($monthlyData ?? [], 'expenses')); ?>,
            borderColor: '#e74a3b',
            backgroundColor: 'rgba(231, 74, 59, 0.1)',
            fill: false
        }, {
            label: 'Lợi nhuận',
            data: <?php echo json_encode(array_column($monthlyData ?? [], 'profit')); ?>,
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat('vi-VN').format(value) + 'đ';
                    }
                }
            }
        },
        plugins: {
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.dataset.label + ': ' + new Intl.NumberFormat('vi-VN').format(context.parsed.y) + 'đ';
                    }
                }
            }
        }
    }
});

// Team Chart
const teamCtx = document.getElementById('teamChart').getContext('2d');
new Chart(teamCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_keys($teamBreakdown ?? [])); ?>,
        datasets: [{
            data: <?php echo json_encode(array_values($teamBreakdown ?? [])); ?>,
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b'],
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        return context.label + ': ' + new Intl.NumberFormat('vi-VN').format(context.parsed) + 'đ';
                    }
                }
            }
        }
    }
});

// Export functions
function exportToExcel() {
    window.location.href = '<?php echo e(route("finance.profit-report")); ?>?' + new URLSearchParams(window.location.search) + '&export=excel';
}

function exportToPDF() {
    window.location.href = '<?php echo e(route("finance.profit-report")); ?>?' + new URLSearchParams(window.location.search) + '&export=pdf';
}

function exportChart(chartId) {
    // Implementation for chart export
    console.log('Exporting chart:', chartId);
}

function printChart(chartId) {
    window.print();
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/finance/profit-report.blade.php ENDPATH**/ ?>