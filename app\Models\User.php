<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'team_id',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // Boot method để validation
    protected static function boot()
    {
        parent::boot();

        // Validation khi tạo user mới
        static::creating(function ($user) {
            if (!$user->team_id) {
                throw new \Exception('User phải thuộc về một team.');
            }
        });

        // Validation khi cập nhật user
        static::updating(function ($user) {
            if (!$user->team_id) {
                throw new \Exception('User phải thuộc về một team.');
            }
        });
    }

    // Relationship với Team
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    // Relationship với EbayAccounts (seller)
    public function ebayAccounts()
    {
        return $this->hasMany(EbayAccount::class, 'seller_id');
    }

    // Relationship với EbayPayouts (người nhập)
    public function enteredPayouts()
    {
        return $this->hasMany(EbayPayout::class, 'entered_by');
    }

    // Relationship với MonthlyExpenses (người tạo)
    public function createdExpenses()
    {
        return $this->hasMany(MonthlyExpense::class, 'created_by');
    }

    // Relationship với MonthlyExpenses (người duyệt)
    public function approvedExpenses()
    {
        return $this->hasMany(MonthlyExpense::class, 'approved_by');
    }

    // Relationship với Teams (leader)
    public function leadingTeams()
    {
        return $this->hasMany(Team::class, 'leader_id');
    }

    // Kiểm tra role
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isTeamLeader()
    {
        return $this->role === 'team_leader';
    }

    public function isSeller()
    {
        return $this->role === 'seller';
    }

    // Kiểm tra quyền xem team
    public function canViewTeam($teamId)
    {
        if ($this->isAdmin()) {
            return true;
        }

        if ($this->isTeamLeader() && $this->team_id == $teamId) {
            return true;
        }

        return $this->team_id == $teamId;
    }

    // Kiểm tra quyền quản lý team
    public function canManageTeam($teamId)
    {
        if ($this->isAdmin()) {
            return true;
        }

        return $this->isTeamLeader() && $this->team_id == $teamId;
    }

    // Kiểm tra quyền duyệt chi phí
    public function canApproveExpenses($teamId = null)
    {
        if ($this->isAdmin()) {
            return true;
        }

        if ($this->isTeamLeader()) {
            return $teamId ? $this->team_id == $teamId : true;
        }

        return false;
    }

    // Kiểm tra quyền xem tài chính
    public function canViewFinance($teamId = null)
    {
        if ($this->isAdmin()) {
            return true;
        }

        if ($this->isTeamLeader()) {
            return $teamId ? $this->team_id == $teamId : true;
        }

        return $teamId ? $this->team_id == $teamId : false;
    }

    // Kiểm tra quyền quản lý eBay accounts
    public function canManageEbayAccounts($teamId = null)
    {
        if ($this->isAdmin()) {
            return true;
        }

        if ($this->isTeamLeader()) {
            return $teamId ? $this->team_id == $teamId : true;
        }

        return false;
    }

    // Kiểm tra quyền nhập payout cho account
    public function canEnterPayoutForAccount($accountId)
    {
        if ($this->isAdmin()) {
            return true;
        }

        $account = \App\Models\EbayAccount::find($accountId);
        if (!$account) {
            return false;
        }

        // Seller chỉ có thể nhập payout cho account của mình
        if ($this->isSeller()) {
            return $account->seller_id == $this->id;
        }

        // Team leader có thể nhập payout cho tất cả accounts trong team
        if ($this->isTeamLeader()) {
            return $account->team_id == $this->team_id;
        }

        return false;
    }

    // Kiểm tra có phải leader của team hiện tại không
    public function isLeaderOfCurrentTeam()
    {
        return $this->team && $this->team->leader_id == $this->id;
    }

    // Lấy danh sách teams user có thể xem
    public function getViewableTeams()
    {
        if ($this->isAdmin()) {
            return \App\Models\Team::all();
        }

        return collect([$this->team]);
    }

    // Lấy danh sách teams user có thể quản lý
    public function getManageableTeams()
    {
        if ($this->isAdmin()) {
            return \App\Models\Team::all();
        }

        if ($this->isTeamLeader()) {
            return collect([$this->team]);
        }

        return collect();
    }

    // Lấy role hiển thị
    public function getRoleLabelAttribute()
    {
        $labels = [
            'admin' => 'Quản trị viên',
            'team_leader' => 'Trưởng nhóm',
            'seller' => 'Seller',
            'user' => 'Người dùng'
        ];

        return $labels[$this->role] ?? $this->role;
    }
}
