<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'team_id',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // Relationship với Team
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    // Relationship với EbayAccounts (seller)
    public function ebayAccounts()
    {
        return $this->hasMany(EbayAccount::class, 'seller_id');
    }

    // Relationship với EbayPayouts (người nhập)
    public function enteredPayouts()
    {
        return $this->hasMany(EbayPayout::class, 'entered_by');
    }

    // Relationship với MonthlyExpenses (người tạo)
    public function createdExpenses()
    {
        return $this->hasMany(MonthlyExpense::class, 'created_by');
    }

    // Relationship với MonthlyExpenses (người duyệt)
    public function approvedExpenses()
    {
        return $this->hasMany(MonthlyExpense::class, 'approved_by');
    }

    // Relationship với Teams (leader)
    public function leadingTeams()
    {
        return $this->hasMany(Team::class, 'leader_id');
    }

    // Kiểm tra role
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    public function isTeamLeader()
    {
        return $this->role === 'team_leader';
    }

    public function isSeller()
    {
        return $this->role === 'seller';
    }

    // Kiểm tra quyền xem team
    public function canViewTeam($teamId)
    {
        if ($this->isAdmin()) {
            return true;
        }

        if ($this->isTeamLeader() && $this->team_id == $teamId) {
            return true;
        }

        return $this->team_id == $teamId;
    }

    // Lấy role hiển thị
    public function getRoleLabelAttribute()
    {
        $labels = [
            'admin' => 'Quản trị viên',
            'team_leader' => 'Trưởng nhóm',
            'seller' => 'Seller',
            'user' => 'Người dùng'
        ];

        return $labels[$this->role] ?? $this->role;
    }
}
