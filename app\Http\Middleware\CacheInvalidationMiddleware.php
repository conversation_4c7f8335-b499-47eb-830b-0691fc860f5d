<?php

namespace App\Http\Middleware;

use App\Services\CacheService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CacheInvalidationMiddleware
{
    protected $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only invalidate cache for successful POST, PUT, PATCH, DELETE requests
        if ($response->isSuccessful() && in_array($request->method(), ['POST', 'PUT', 'PATCH', 'DELETE'])) {
            $this->invalidateRelevantCache($request);
        }

        return $response;
    }

    /**
     * Invalidate relevant cache based on the request
     */
    private function invalidateRelevantCache(Request $request)
    {
        $route = $request->route();
        if (!$route) {
            return;
        }

        $routeName = $route->getName();
        $parameters = $route->parameters();

        // Invalidate cache based on route patterns
        if (str_contains($routeName, 'finance.payouts')) {
            $this->invalidatePayoutCache($parameters);
        } elseif (str_contains($routeName, 'finance.expenses')) {
            $this->invalidateExpenseCache($parameters);
        } elseif (str_contains($routeName, 'finance.teams')) {
            $this->invalidateTeamCache($parameters);
        } elseif (str_contains($routeName, 'checkout.orders') || str_contains($routeName, 'checkout.units')) {
            $this->invalidateCheckoutCache($parameters);
        } elseif (str_contains($routeName, 'notifications')) {
            $this->invalidateNotificationCache($request);
        }
    }

    /**
     * Invalidate payout-related cache
     */
    private function invalidatePayoutCache($parameters)
    {
        if (isset($parameters['payout'])) {
            $payout = $parameters['payout'];
            if (is_object($payout) && isset($payout->team_id)) {
                $this->cacheService->invalidateTeamCache($payout->team_id);
            }
        }

        // Always invalidate global dashboard cache for financial changes
        $this->cacheService->invalidateAllDashboardCache();
    }

    /**
     * Invalidate expense-related cache
     */
    private function invalidateExpenseCache($parameters)
    {
        if (isset($parameters['expense'])) {
            $expense = $parameters['expense'];
            if (is_object($expense) && isset($expense->team_id)) {
                $this->cacheService->invalidateTeamCache($expense->team_id);
            }
        }

        // Always invalidate global dashboard cache for financial changes
        $this->cacheService->invalidateAllDashboardCache();
    }

    /**
     * Invalidate team-related cache
     */
    private function invalidateTeamCache($parameters)
    {
        if (isset($parameters['team'])) {
            $team = $parameters['team'];
            $teamId = is_object($team) ? $team->id : $team;
            $this->cacheService->invalidateTeamCache($teamId);
        }

        // Team changes affect global stats
        $this->cacheService->invalidateAllDashboardCache();
    }

    /**
     * Invalidate checkout-related cache
     */
    private function invalidateCheckoutCache($parameters)
    {
        if (isset($parameters['checkoutUnit'])) {
            $unit = $parameters['checkoutUnit'];
            $unitId = is_object($unit) ? $unit->id : $unit;
            $this->cacheService->invalidateCheckoutUnitCache($unitId);
        }

        if (isset($parameters['checkoutOrder'])) {
            $order = $parameters['checkoutOrder'];
            if (is_object($order) && isset($order->checkout_unit_id)) {
                $this->cacheService->invalidateCheckoutUnitCache($order->checkout_unit_id);
            }
        }
    }

    /**
     * Invalidate notification-related cache
     */
    private function invalidateNotificationCache(Request $request)
    {
        $user = $request->user();
        if ($user) {
            $this->cacheService->invalidateUserNotificationCache($user->id);
        }
    }
}
