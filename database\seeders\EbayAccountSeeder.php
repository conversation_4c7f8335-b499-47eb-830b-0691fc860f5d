<?php

namespace Database\Seeders;

use App\Models\EbayAccount;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EbayAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teams = Team::with('members')->get();

        foreach ($teams as $team) {
            $sellers = $team->members()->where('role', 'seller')->get();

            foreach ($sellers as $index => $seller) {
                // Mỗi seller có 1-2 accounts
                $accountCount = rand(1, 2);

                for ($i = 1; $i <= $accountCount; $i++) {
                    EbayAccount::create([
                        'account_name' => $seller->name . ' Account ' . $i,
                        'email' => 'ebay' . $i . '.' . strtolower(str_replace(' ', '', $seller->name)) . '@example.com',
                        'ebay_user_id' => 'ebay_' . $team->code . '_' . ($index + 1) . '_' . $i,
                        'team_id' => $team->id,
                        'seller_id' => $seller->id,
                        'status' => ['active', 'active', 'active', 'suspended'][array_rand(['active', 'active', 'active', 'suspended'])],
                        'payout_schedule' => ['weekly', 'bi_weekly', 'monthly'][array_rand(['weekly', 'bi_weekly', 'monthly'])],
                        'last_payout_date' => now()->subDays(rand(1, 30)),
                        'total_payouts' => rand(10000, 100000),
                        'notes' => 'Account eBay của ' . $seller->name . ' - Account ' . $i
                    ]);
                }
            }
        }
    }
}
