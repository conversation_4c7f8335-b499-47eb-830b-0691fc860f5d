<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('monthly_expenses', function (Blueprint $table) {
            // Thêm category column tr<PERSON><PERSON><PERSON> khi thêm các smart fields
            $table->string('category')->default('other')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('monthly_expenses', function (Blueprint $table) {
            $table->dropColumn('category');
        });
    }
};
