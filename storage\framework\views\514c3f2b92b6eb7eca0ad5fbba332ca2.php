<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-coins me-1"></i>
        <PERSON><PERSON><PERSON> hình tiền tệ
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="default_currency" class="form-label">
                    Loạ<PERSON> tiền tệ chính <span class="text-danger">*</span>
                </label>
                <select class="form-select <?php $__errorArgs = ['default_currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                        id="default_currency" 
                        name="default_currency" 
                        required>
                    <?php
                        $currencies = [
                            'VND' => 'Việt Nam Đồng (VND)',
                            'USD' => 'US Dollar (USD)',
                            'EUR' => 'Euro (EUR)',
                            'GBP' => 'British Pound (GBP)',
                            'JPY' => 'Japanese Yen (JPY)'
                        ];
                        $currentCurrency = old('default_currency', $values['default_currency'] ?? 'VND');
                    ?>
                    <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($value); ?>" <?php echo e($currentCurrency === $value ? 'selected' : ''); ?>>
                            <?php echo e($label); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <div class="form-text">Loại tiền tệ chính được sử dụng trong hệ thống</div>
                <?php $__errorArgs = ['default_currency'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label for="default_exchange_rate_usd_vnd" class="form-label">
                    Tỷ giá mặc định USD/VND <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                    <span class="input-group-text">1 USD =</span>
                    <input type="number" 
                           class="form-control <?php $__errorArgs = ['default_exchange_rate_usd_vnd'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="default_exchange_rate_usd_vnd" 
                           name="default_exchange_rate_usd_vnd" 
                           value="<?php echo e(old('default_exchange_rate_usd_vnd', $values['default_exchange_rate_usd_vnd'] ?? '24000')); ?>"
                           min="1"
                           step="0.01"
                           required>
                    <span class="input-group-text">VND</span>
                </div>
                <div class="form-text">Tỷ giá dự phòng khi API tỷ giá không khả dụng</div>
                <?php $__errorArgs = ['default_exchange_rate_usd_vnd'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-percentage me-1"></i>
        Phí và hoa hồng
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="setting-item">
                <label for="transaction_fee_percentage" class="form-label">
                    Phí giao dịch (%) <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                    <input type="number" 
                           class="form-control <?php $__errorArgs = ['transaction_fee_percentage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           id="transaction_fee_percentage" 
                           name="transaction_fee_percentage" 
                           value="<?php echo e(old('transaction_fee_percentage', $values['transaction_fee_percentage'] ?? '2.5')); ?>"
                           min="0"
                           max="100"
                           step="0.1"
                           required>
                    <span class="input-group-text">%</span>
                </div>
                <div class="form-text">Phí giao dịch áp dụng cho các thanh toán</div>
                <?php $__errorArgs = ['transaction_fee_percentage'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="setting-item">
                <label class="form-label">Ví dụ tính phí</label>
                <div class="card bg-light">
                    <div class="card-body">
                        <div id="feeExample">
                            <?php
                                $exampleAmount = 1000000; // 1 triệu VND
                                $feePercentage = $values['transaction_fee_percentage'] ?? 2.5;
                                $fee = ($exampleAmount * $feePercentage) / 100;
                                $total = $exampleAmount + $fee;
                            ?>
                            <small>
                                <strong>Số tiền giao dịch:</strong> <?php echo e(number_format($exampleAmount)); ?> VND<br>
                                <strong>Phí giao dịch (<?php echo e($feePercentage); ?>%):</strong> <?php echo e(number_format($fee)); ?> VND<br>
                                <strong>Tổng cộng:</strong> <?php echo e(number_format($total)); ?> VND
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-chart-line me-1"></i>
        Thống kê tài chính
    </h6>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h6 class="card-title text-primary">Tỷ giá hiện tại</h6>
                    <p class="card-text">
                        <strong class="h5"><?php echo e(number_format($values['default_exchange_rate_usd_vnd'] ?? 24000)); ?></strong><br>
                        <small class="text-muted">VND/USD</small>
                    </p>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="getCurrentExchangeRate()">
                        <i class="fas fa-sync me-1"></i>
                        Cập nhật từ API
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h6 class="card-title text-success">Phí giao dịch</h6>
                    <p class="card-text">
                        <strong class="h5"><?php echo e($values['transaction_fee_percentage'] ?? 2.5); ?>%</strong><br>
                        <small class="text-muted">Áp dụng cho mọi giao dịch</small>
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-info">
                <div class="card-body text-center">
                    <h6 class="card-title text-info">Tiền tệ chính</h6>
                    <p class="card-text">
                        <strong class="h5"><?php echo e($values['default_currency'] ?? 'VND'); ?></strong><br>
                        <small class="text-muted">Hiển thị mặc định</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="setting-group">
    <h6 class="text-primary mb-3">
        <i class="fas fa-calculator me-1"></i>
        Công cụ tính toán
    </h6>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Chuyển đổi tiền tệ</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Số tiền USD</label>
                        <input type="number" class="form-control" id="usdAmount" placeholder="Nhập số tiền USD" min="0" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tương đương VND</label>
                        <input type="text" class="form-control" id="vndAmount" readonly>
                    </div>
                    <button type="button" class="btn btn-primary btn-sm" onclick="convertCurrency()">
                        <i class="fas fa-exchange-alt me-1"></i>
                        Chuyển đổi
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Tính phí giao dịch</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Số tiền giao dịch</label>
                        <input type="number" class="form-control" id="transactionAmount" placeholder="Nhập số tiền" min="0" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Phí giao dịch</label>
                        <input type="text" class="form-control" id="calculatedFee" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tổng cộng</label>
                        <input type="text" class="form-control" id="totalAmount" readonly>
                    </div>
                    <button type="button" class="btn btn-success btn-sm" onclick="calculateFee()">
                        <i class="fas fa-calculator me-1"></i>
                        Tính phí
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update fee example when percentage changes
document.getElementById('transaction_fee_percentage').addEventListener('input', function() {
    updateFeeExample();
});

function updateFeeExample() {
    const feePercentage = parseFloat(document.getElementById('transaction_fee_percentage').value) || 0;
    const exampleAmount = 1000000; // 1 triệu VND
    const fee = (exampleAmount * feePercentage) / 100;
    const total = exampleAmount + fee;
    
    document.getElementById('feeExample').innerHTML = `
        <small>
            <strong>Số tiền giao dịch:</strong> ${exampleAmount.toLocaleString()} VND<br>
            <strong>Phí giao dịch (${feePercentage}%):</strong> ${fee.toLocaleString()} VND<br>
            <strong>Tổng cộng:</strong> ${total.toLocaleString()} VND
        </small>
    `;
}

function getCurrentExchangeRate() {
    // Mock function - in real implementation would call exchange rate API
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang cập nhật...';
    button.disabled = true;
    
    setTimeout(function() {
        // Simulate API call
        const newRate = Math.floor(Math.random() * (25000 - 23000) + 23000);
        document.getElementById('default_exchange_rate_usd_vnd').value = newRate;
        
        button.innerHTML = originalText;
        button.disabled = false;
        
        showAlert('success', `Đã cập nhật tỷ giá mới: ${newRate.toLocaleString()} VND/USD`);
    }, 2000);
}

function convertCurrency() {
    const usdAmount = parseFloat(document.getElementById('usdAmount').value) || 0;
    const exchangeRate = parseFloat(document.getElementById('default_exchange_rate_usd_vnd').value) || 24000;
    const vndAmount = usdAmount * exchangeRate;
    
    document.getElementById('vndAmount').value = vndAmount.toLocaleString() + ' VND';
}

function calculateFee() {
    const amount = parseFloat(document.getElementById('transactionAmount').value) || 0;
    const feePercentage = parseFloat(document.getElementById('transaction_fee_percentage').value) || 0;
    const fee = (amount * feePercentage) / 100;
    const total = amount + fee;
    
    document.getElementById('calculatedFee').value = fee.toLocaleString() + ' VND';
    document.getElementById('totalAmount').value = total.toLocaleString() + ' VND';
}

// Auto-convert when USD amount changes
document.getElementById('usdAmount').addEventListener('input', convertCurrency);

// Auto-calculate fee when transaction amount changes
document.getElementById('transactionAmount').addEventListener('input', calculateFee);
</script>
<?php /**PATH E:\XAMPP\htdocs\Dropship Manager\resources\views/admin/settings/partials/financial.blade.php ENDPATH**/ ?>