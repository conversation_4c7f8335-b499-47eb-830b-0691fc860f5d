<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SettingsService
{
    /**
     * Get setting value with fallback
     */
    public function get($key, $default = null)
    {
        return Setting::get($key, $default);
    }

    /**
     * Set setting value
     */
    public function set($key, $value, $updatedBy = null)
    {
        return Setting::set($key, $value, $updatedBy);
    }

    /**
     * Get all settings by group
     */
    public function getByGroup($group)
    {
        return Setting::getByGroup($group);
    }

    /**
     * Update multiple settings at once
     */
    public function updateMultiple($settings, $updatedBy = null)
    {
        $errors = [];
        $updated = [];

        foreach ($settings as $key => $value) {
            try {
                $setting = Setting::where('key', $key)->first();
                
                if ($setting) {
                    // Validate value
                    if (!$setting->validateValue($value)) {
                        $errors[$key] = $setting->getValidationErrors($value);
                        continue;
                    }
                }

                Setting::set($key, $value, $updatedBy);
                $updated[] = $key;

            } catch (\Exception $e) {
                $errors[$key] = ['Lỗi cập nhật: ' . $e->getMessage()];
                Log::error("Failed to update setting {$key}: " . $e->getMessage());
            }
        }

        return [
            'success' => empty($errors),
            'updated' => $updated,
            'errors' => $errors
        ];
    }

    /**
     * Initialize default settings
     */
    public function initializeDefaults()
    {
        $defaultSettings = $this->getDefaultSettings();

        foreach ($defaultSettings as $setting) {
            $existing = Setting::where('key', $setting['key'])->first();
            
            if (!$existing) {
                Setting::create($setting);
            }
        }
    }

    /**
     * Get default settings configuration
     */
    private function getDefaultSettings()
    {
        return [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'Dropship Manager',
                'group' => 'general',
                'type' => 'string',
                'description' => 'Tên website',
                'default_value' => 'Dropship Manager',
                'is_public' => true,
                'validation_rules' => ['required', 'string', 'max:255'],
                'sort_order' => 1
            ],
            [
                'key' => 'site_logo',
                'value' => null,
                'group' => 'general',
                'type' => 'string',
                'description' => 'Logo website (URL)',
                'default_value' => null,
                'is_public' => true,
                'validation_rules' => ['nullable', 'url'],
                'sort_order' => 2
            ],
            [
                'key' => 'timezone',
                'value' => 'Asia/Ho_Chi_Minh',
                'group' => 'general',
                'type' => 'string',
                'description' => 'Múi giờ mặc định',
                'default_value' => 'Asia/Ho_Chi_Minh',
                'is_public' => true,
                'validation_rules' => ['required', 'string'],
                'options' => [
                    'Asia/Ho_Chi_Minh' => 'Việt Nam (UTC+7)',
                    'UTC' => 'UTC (UTC+0)',
                    'America/New_York' => 'New York (UTC-5)',
                    'Europe/London' => 'London (UTC+0)'
                ],
                'sort_order' => 3
            ],
            [
                'key' => 'default_language',
                'value' => 'vi',
                'group' => 'general',
                'type' => 'string',
                'description' => 'Ngôn ngữ mặc định',
                'default_value' => 'vi',
                'is_public' => true,
                'validation_rules' => ['required', 'string'],
                'options' => [
                    'vi' => 'Tiếng Việt',
                    'en' => 'English'
                ],
                'sort_order' => 4
            ],

            // Financial Settings
            [
                'key' => 'default_currency',
                'value' => 'VND',
                'group' => 'finance',
                'type' => 'string',
                'description' => 'Loại tiền tệ chính',
                'default_value' => 'VND',
                'is_public' => true,
                'validation_rules' => ['required', 'string', 'size:3'],
                'options' => [
                    'VND' => 'Việt Nam Đồng (VND)',
                    'USD' => 'US Dollar (USD)',
                    'EUR' => 'Euro (EUR)'
                ],
                'sort_order' => 1
            ],
            [
                'key' => 'default_exchange_rate_usd_vnd',
                'value' => '24000',
                'group' => 'finance',
                'type' => 'float',
                'description' => 'Tỷ giá mặc định USD/VND',
                'default_value' => '24000',
                'validation_rules' => ['required', 'numeric', 'min:1'],
                'sort_order' => 2
            ],
            [
                'key' => 'transaction_fee_percentage',
                'value' => '2.5',
                'group' => 'finance',
                'type' => 'float',
                'description' => 'Phí giao dịch (%)',
                'default_value' => '2.5',
                'validation_rules' => ['required', 'numeric', 'min:0', 'max:100'],
                'sort_order' => 3
            ],

            // Notification Settings
            [
                'key' => 'smtp_host',
                'value' => null,
                'group' => 'notification',
                'type' => 'string',
                'description' => 'SMTP Host',
                'default_value' => null,
                'is_encrypted' => false,
                'validation_rules' => ['nullable', 'string'],
                'sort_order' => 1
            ],
            [
                'key' => 'smtp_port',
                'value' => '587',
                'group' => 'notification',
                'type' => 'integer',
                'description' => 'SMTP Port',
                'default_value' => '587',
                'validation_rules' => ['nullable', 'integer', 'min:1', 'max:65535'],
                'sort_order' => 2
            ],
            [
                'key' => 'smtp_username',
                'value' => null,
                'group' => 'notification',
                'type' => 'string',
                'description' => 'SMTP Username',
                'default_value' => null,
                'is_encrypted' => true,
                'validation_rules' => ['nullable', 'string'],
                'sort_order' => 3
            ],
            [
                'key' => 'smtp_password',
                'value' => null,
                'group' => 'notification',
                'type' => 'string',
                'description' => 'SMTP Password',
                'default_value' => null,
                'is_encrypted' => true,
                'validation_rules' => ['nullable', 'string'],
                'sort_order' => 4
            ],
            [
                'key' => 'notification_frequency',
                'value' => 'immediate',
                'group' => 'notification',
                'type' => 'string',
                'description' => 'Tần suất gửi thông báo',
                'default_value' => 'immediate',
                'validation_rules' => ['required', 'string'],
                'options' => [
                    'immediate' => 'Ngay lập tức',
                    'hourly' => 'Mỗi giờ',
                    'daily' => 'Hàng ngày',
                    'weekly' => 'Hàng tuần'
                ],
                'sort_order' => 5
            ],

            // API Settings
            [
                'key' => 'google_sheets_api_key',
                'value' => null,
                'group' => 'api',
                'type' => 'string',
                'description' => 'Google Sheets API Key',
                'default_value' => null,
                'is_encrypted' => true,
                'validation_rules' => ['nullable', 'string'],
                'sort_order' => 1
            ],
            [
                'key' => 'ebay_api_key',
                'value' => null,
                'group' => 'api',
                'type' => 'string',
                'description' => 'eBay API Key',
                'default_value' => null,
                'is_encrypted' => true,
                'validation_rules' => ['nullable', 'string'],
                'sort_order' => 2
            ],
            [
                'key' => 'exchange_rate_api_key',
                'value' => null,
                'group' => 'api',
                'type' => 'string',
                'description' => 'Exchange Rate API Key',
                'default_value' => null,
                'is_encrypted' => true,
                'validation_rules' => ['nullable', 'string'],
                'sort_order' => 3
            ],

            // Checkout Settings
            [
                'key' => 'default_checkout_fee_percentage',
                'value' => '15',
                'group' => 'checkout',
                'type' => 'float',
                'description' => 'Tỷ lệ phí checkout mặc định (%)',
                'default_value' => '15',
                'validation_rules' => ['required', 'numeric', 'min:0', 'max:100'],
                'sort_order' => 1
            ],
            [
                'key' => 'checkout_order_statuses',
                'value' => json_encode([
                    'pending' => 'Chờ xử lý',
                    'confirmed' => 'Đã xác nhận',
                    'processing' => 'Đang xử lý',
                    'shipped' => 'Đã gửi hàng',
                    'delivered' => 'Đã giao hàng',
                    'completed' => 'Hoàn thành',
                    'cancelled' => 'Đã hủy',
                    'returned' => 'Đã trả hàng',
                    'refunded' => 'Đã hoàn tiền'
                ]),
                'group' => 'checkout',
                'type' => 'json',
                'description' => 'Trạng thái đơn hàng checkout',
                'default_value' => json_encode([
                    'pending' => 'Chờ xử lý',
                    'confirmed' => 'Đã xác nhận',
                    'processing' => 'Đang xử lý',
                    'shipped' => 'Đã gửi hàng',
                    'delivered' => 'Đã giao hàng',
                    'completed' => 'Hoàn thành',
                    'cancelled' => 'Đã hủy',
                    'returned' => 'Đã trả hàng',
                    'refunded' => 'Đã hoàn tiền'
                ]),
                'sort_order' => 2
            ],

            // Team Settings
            [
                'key' => 'max_members_per_team',
                'value' => '50',
                'group' => 'team',
                'type' => 'integer',
                'description' => 'Số lượng members tối đa per team',
                'default_value' => '50',
                'validation_rules' => ['required', 'integer', 'min:1', 'max:1000'],
                'sort_order' => 1
            ],
            [
                'key' => 'default_user_role',
                'value' => 'user',
                'group' => 'team',
                'type' => 'string',
                'description' => 'Role mặc định cho user mới',
                'default_value' => 'user',
                'validation_rules' => ['required', 'string'],
                'options' => [
                    'user' => 'User',
                    'seller' => 'Seller'
                ],
                'sort_order' => 2
            ]
        ];
    }

    /**
     * Export settings to array
     */
    public function exportSettings()
    {
        $settings = Setting::all();
        
        return [
            'exported_at' => now()->toISOString(),
            'version' => '1.0',
            'settings' => $settings->map(function ($setting) {
                return [
                    'key' => $setting->key,
                    'value' => $setting->is_encrypted ? '[ENCRYPTED]' : $setting->value,
                    'group' => $setting->group,
                    'type' => $setting->type,
                    'description' => $setting->description,
                    'default_value' => $setting->default_value,
                    'is_public' => $setting->is_public,
                    'validation_rules' => $setting->validation_rules,
                    'options' => $setting->options,
                    'sort_order' => $setting->sort_order
                ];
            })
        ];
    }

    /**
     * Import settings from array
     */
    public function importSettings($data, $updatedBy = null)
    {
        if (!isset($data['settings'])) {
            throw new \Exception('Invalid settings data format');
        }

        $imported = 0;
        $errors = [];

        foreach ($data['settings'] as $settingData) {
            try {
                // Skip encrypted values in import
                if ($settingData['value'] === '[ENCRYPTED]') {
                    continue;
                }

                $setting = Setting::where('key', $settingData['key'])->first();
                
                if ($setting) {
                    $setting->update([
                        'value' => $settingData['value'],
                        'updated_by' => $updatedBy
                    ]);
                } else {
                    Setting::create(array_merge($settingData, [
                        'updated_by' => $updatedBy
                    ]));
                }

                $imported++;

            } catch (\Exception $e) {
                $errors[] = "Failed to import {$settingData['key']}: " . $e->getMessage();
            }
        }

        return [
            'success' => empty($errors),
            'imported' => $imported,
            'errors' => $errors
        ];
    }

    /**
     * Reset settings to default values
     */
    public function resetToDefaults($group = null, $updatedBy = null)
    {
        $query = Setting::query();
        
        if ($group) {
            $query->where('group', $group);
        }

        $settings = $query->get();
        $reset = 0;

        foreach ($settings as $setting) {
            if ($setting->default_value !== null) {
                $setting->update([
                    'value' => $setting->default_value,
                    'updated_by' => $updatedBy
                ]);
                $reset++;
            }
        }

        return $reset;
    }

    /**
     * Test API connection
     */
    public function testApiConnection($apiType)
    {
        switch ($apiType) {
            case 'google_sheets':
                return $this->testGoogleSheetsConnection();
            case 'ebay':
                return $this->testEbayConnection();
            case 'exchange_rate':
                return $this->testExchangeRateConnection();
            case 'smtp':
                return $this->testSmtpConnection();
            default:
                return ['success' => false, 'message' => 'Unknown API type'];
        }
    }

    private function testGoogleSheetsConnection()
    {
        $apiKey = $this->get('google_sheets_api_key');
        
        if (!$apiKey) {
            return ['success' => false, 'message' => 'API Key chưa được cấu hình'];
        }

        // Mock test - in real implementation, would test actual API
        return ['success' => true, 'message' => 'Kết nối Google Sheets thành công (Demo)'];
    }

    private function testEbayConnection()
    {
        $apiKey = $this->get('ebay_api_key');
        
        if (!$apiKey) {
            return ['success' => false, 'message' => 'API Key chưa được cấu hình'];
        }

        // Mock test
        return ['success' => true, 'message' => 'Kết nối eBay API thành công (Demo)'];
    }

    private function testExchangeRateConnection()
    {
        $apiKey = $this->get('exchange_rate_api_key');
        
        if (!$apiKey) {
            return ['success' => false, 'message' => 'API Key chưa được cấu hình'];
        }

        // Mock test
        return ['success' => true, 'message' => 'Kết nối Exchange Rate API thành công (Demo)'];
    }

    private function testSmtpConnection()
    {
        $host = $this->get('smtp_host');
        $port = $this->get('smtp_port');
        $username = $this->get('smtp_username');
        $password = $this->get('smtp_password');
        
        if (!$host || !$port || !$username || !$password) {
            return ['success' => false, 'message' => 'SMTP settings chưa được cấu hình đầy đủ'];
        }

        // Mock test
        return ['success' => true, 'message' => 'Kết nối SMTP thành công (Demo)'];
    }
}
