@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> hình hệ thống - Admin')
@section('page-title', '<PERSON><PERSON><PERSON> hình hệ thống')

@section('page-actions')
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="exportSettings()">
        <i class="fas fa-download me-1"></i>
        Export
    </button>
    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#importModal">
        <i class="fas fa-upload me-1"></i>
        Import
    </button>
    <button type="button" class="btn btn-outline-warning" onclick="clearCache()">
        <i class="fas fa-sync me-1"></i>
        Clear Cache
    </button>
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-danger dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-undo me-1"></i>
            Reset
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="resetToDefaults()">Reset tất cả</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" onclick="resetToDefaults('general')">Reset cấu hình chung</a></li>
            <li><a class="dropdown-item" href="#" onclick="resetToDefaults('finance')">Reset cấu hình tài chính</a></li>
            <li><a class="dropdown-item" href="#" onclick="resetToDefaults('notification')">Reset cấu hình thông báo</a></li>
            <li><a class="dropdown-item" href="#" onclick="resetToDefaults('api')">Reset cấu hình API</a></li>
            <li><a class="dropdown-item" href="#" onclick="resetToDefaults('checkout')">Reset cấu hình checkout</a></li>
            <li><a class="dropdown-item" href="#" onclick="resetToDefaults('team')">Reset cấu hình team</a></li>
        </ul>
    </div>
</div>
@endsection

@section('content')
<!-- Settings Form -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link {{ $activeTab === 'general' ? 'active' : '' }}" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                    <i class="fas fa-cog me-1"></i>
                    Cấu hình chung
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link {{ $activeTab === 'financial' ? 'active' : '' }}" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button" role="tab">
                    <i class="fas fa-dollar-sign me-1"></i>
                    Tài chính
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link {{ $activeTab === 'notification' ? 'active' : '' }}" id="notification-tab" data-bs-toggle="tab" data-bs-target="#notification" type="button" role="tab">
                    <i class="fas fa-bell me-1"></i>
                    Thông báo
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link {{ $activeTab === 'api' ? 'active' : '' }}" id="api-tab" data-bs-toggle="tab" data-bs-target="#api" type="button" role="tab">
                    <i class="fas fa-plug me-1"></i>
                    API
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link {{ $activeTab === 'checkout' ? 'active' : '' }}" id="checkout-tab" data-bs-toggle="tab" data-bs-target="#checkout" type="button" role="tab">
                    <i class="fas fa-shopping-cart me-1"></i>
                    Checkout
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link {{ $activeTab === 'team' ? 'active' : '' }}" id="team-tab" data-bs-toggle="tab" data-bs-target="#team" type="button" role="tab">
                    <i class="fas fa-users me-1"></i>
                    Team
                </button>
            </li>
        </ul>
    </div>

    <form action="{{ route('admin.settings.update') }}" method="POST" id="settingsForm">
        @csrf
        @method('PUT')
        <input type="hidden" name="tab" id="activeTabInput" value="{{ $activeTab }}">

        <div class="card-body">
            <div class="tab-content" id="settingsTabContent">
                <!-- General Settings -->
                <div class="tab-pane fade {{ $activeTab === 'general' ? 'show active' : '' }}" id="general" role="tabpanel">
                    <h5 class="mb-3">Cấu hình chung</h5>
                    @include('admin.settings.partials.general', ['settings' => $settingsGroups['general'], 'values' => $currentValues])
                </div>

                <!-- Finance Settings -->
                <div class="tab-pane fade {{ $activeTab === 'financial' ? 'show active' : '' }}" id="financial" role="tabpanel">
                    <h5 class="mb-3">Cấu hình tài chính</h5>
                    @include('admin.settings.partials.financial', ['settings' => $settingsGroups['financial'], 'values' => $currentValues])
                </div>

                <!-- Notification Settings -->
                <div class="tab-pane fade {{ $activeTab === 'notification' ? 'show active' : '' }}" id="notification" role="tabpanel">
                    <h5 class="mb-3">Cấu hình thông báo</h5>
                    @include('admin.settings.partials.notification', ['settings' => $settingsGroups['notification'], 'values' => $currentValues])
                </div>

                <!-- API Settings -->
                <div class="tab-pane fade {{ $activeTab === 'api' ? 'show active' : '' }}" id="api" role="tabpanel">
                    <h5 class="mb-3">Cấu hình API</h5>
                    @include('admin.settings.partials.api', ['settings' => $settingsGroups['api'], 'values' => $currentValues])
                </div>

                <!-- Checkout Settings -->
                <div class="tab-pane fade {{ $activeTab === 'checkout' ? 'show active' : '' }}" id="checkout" role="tabpanel">
                    <h5 class="mb-3">Cấu hình checkout</h5>
                    @include('admin.settings.partials.checkout', ['settings' => $settingsGroups['checkout'], 'values' => $currentValues])
                </div>

                <!-- Team Settings -->
                <div class="tab-pane fade {{ $activeTab === 'team' ? 'show active' : '' }}" id="team" role="tabpanel">
                    <h5 class="mb-3">Cấu hình team</h5>
                    @include('admin.settings.partials.team', ['settings' => $settingsGroups['team'], 'values' => $currentValues])
                </div>
            </div>
        </div>

        <div class="card-footer">
            <div class="d-flex justify-content-between">
                <div>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Thay đổi sẽ được áp dụng ngay lập tức và gửi thông báo đến các admin khác.
                    </small>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary me-2" onclick="location.reload()">
                        <i class="fas fa-undo me-1"></i>
                        Hủy
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Lưu cấu hình
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import cấu hình</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('admin.settings.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="settings_file" class="form-label">Chọn file cấu hình (JSON)</label>
                        <input type="file" class="form-control" id="settings_file" name="settings_file" accept=".json" required>
                        <div class="form-text">
                            Chỉ chấp nhận file JSON được export từ hệ thống. Các settings đã mã hóa sẽ không được import.
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        <strong>Cảnh báo:</strong> Import sẽ ghi đè lên các cấu hình hiện tại. Hãy chắc chắn bạn đã backup trước khi thực hiện.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>
                        Import
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    border-bottom-color: #007bff;
    color: #007bff;
    background: none;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #007bff;
    color: #007bff;
}

.setting-group {
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.api-test-btn {
    margin-left: 0.5rem;
}

.test-result {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.test-result.success {
    background-color: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.test-result.error {
    background-color: #f8d7da;
    color: #842029;
    border: 1px solid #f5c2c7;
}
</style>
@endpush

@push('scripts')
<script>
// Track active tab
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('#settingsTabs button[data-bs-toggle="tab"]');
    const activeTabInput = document.getElementById('activeTabInput');

    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const tabId = event.target.getAttribute('data-bs-target').replace('#', '');
            activeTabInput.value = tabId;
        });
    });
});

// Export settings
function exportSettings() {
    window.location.href = '{{ route("admin.settings.export") }}';
}

// Clear cache
function clearCache() {
    if (!confirm('Bạn có chắc chắn muốn xóa cache settings?')) {
        return;
    }

    $.post('{{ route("admin.settings.clear-cache") }}', {
        _token: '{{ csrf_token() }}'
    })
    .done(function(response) {
        if (response.success) {
            showAlert('success', response.message);
        } else {
            showAlert('error', response.message);
        }
    })
    .fail(function() {
        showAlert('error', 'Lỗi khi xóa cache');
    });
}

// Reset to defaults
function resetToDefaults(group = null) {
    const message = group
        ? `Bạn có chắc chắn muốn reset cấu hình nhóm "${group}" về giá trị mặc định?`
        : 'Bạn có chắc chắn muốn reset TẤT CẢ cấu hình về giá trị mặc định?';

    if (!confirm(message)) {
        return;
    }

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ route("admin.settings.reset-defaults") }}';

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = '{{ csrf_token() }}';
    form.appendChild(csrfToken);

    if (group) {
        const groupInput = document.createElement('input');
        groupInput.type = 'hidden';
        groupInput.name = 'group';
        groupInput.value = group;
        form.appendChild(groupInput);
    }

    document.body.appendChild(form);
    form.submit();
}

// Test API connection
function testApi(apiType, buttonElement) {
    const button = $(buttonElement);
    const originalText = button.html();
    const resultDiv = button.siblings('.test-result');

    // Show loading
    button.html('<i class="fas fa-spinner fa-spin"></i> Testing...').prop('disabled', true);
    resultDiv.hide();

    $.post('{{ route("admin.settings.test-api") }}', {
        _token: '{{ csrf_token() }}',
        api_type: apiType
    })
    .done(function(response) {
        const resultClass = response.success ? 'success' : 'error';
        const icon = response.success ? 'fas fa-check' : 'fas fa-times';

        resultDiv.removeClass('success error').addClass(resultClass);
        resultDiv.html(`<i class="${icon}"></i> ${response.message}`).show();
    })
    .fail(function() {
        resultDiv.removeClass('success error').addClass('error');
        resultDiv.html('<i class="fas fa-times"></i> Lỗi kết nối').show();
    })
    .always(function() {
        button.html(originalText).prop('disabled', false);
    });
}

// Show alert
function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon} me-1"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Insert alert at the top of the content
    $('.container-fluid').prepend(alertHtml);

    // Auto hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// Handle form submission
$('#settingsForm').on('submit', function(e) {
    const submitButton = $(this).find('button[type="submit"]');
    const originalText = submitButton.html();

    submitButton.html('<i class="fas fa-spinner fa-spin me-1"></i> Đang lưu...').prop('disabled', true);

    // Re-enable button after 3 seconds (in case of redirect)
    setTimeout(function() {
        submitButton.html(originalText).prop('disabled', false);
    }, 3000);
});

// Auto-save on tab change (optional)
$('#settingsTabs button[data-bs-toggle="tab"]').on('shown.bs.tab', function(event) {
    // Could implement auto-save here if needed
});
</script>
@endpush
