<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            // First, rename 'group' to 'category' if exists
            if (Schema::hasColumn('settings', 'group') && !Schema::hasColumn('settings', 'category')) {
                $table->renameColumn('group', 'category');
            }
        });

        // Second pass to add new columns
        Schema::table('settings', function (Blueprint $table) {
            // Add new columns if they don't exist
            if (!Schema::hasColumn('settings', 'category')) {
                $table->string('category')->default('general')->after('type');
            }
            if (!Schema::hasColumn('settings', 'label')) {
                $table->string('label')->nullable()->after('category');
            }
            if (!Schema::hasColumn('settings', 'validation_rules')) {
                $table->json('validation_rules')->nullable()->after('description');
            }
        });

        // Third pass to add indexes
        Schema::table('settings', function (Blueprint $table) {
            try {
                $table->index(['category', 'sort_order']);
            } catch (\Exception $e) {
                // Index might already exist
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->dropColumn(['category', 'label', 'validation_rules']);
            $table->dropIndex(['category', 'sort_order']);
        });
    }
};
