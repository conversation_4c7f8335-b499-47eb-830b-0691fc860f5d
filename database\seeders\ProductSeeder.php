<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use App\Models\Supplier;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all();
        $suppliers = Supplier::all();

        $products = [
            [
                'name' => 'Áo thun nam basic',
                'description' => 'Áo thun nam chất liệu cotton 100%, form regular fit, phù hợp mặc hàng ngày',
                'short_description' => 'Áo thun nam cotton 100% basic',
                'cost_price' => 45000,
                'selling_price' => 89000,
                'compare_price' => 120000,
                'stock_quantity' => 150,
                'min_stock_level' => 20,
                'weight' => '200g',
                'dimensions' => '30x40x2cm',
                'category_name' => 'Thời trang nam',
                'supplier_name' => 'Công ty TNHH Thời Trang ABC',
                'is_featured' => true,
                'seo_title' => 'Áo thun nam basic cotton 100% chất lượng cao',
                'seo_description' => 'Áo thun nam basic cotton 100%, form regular fit, nhiều màu sắc, giá tốt nhất thị trường'
            ],
            [
                'name' => 'Quần jeans nam slim fit',
                'description' => 'Quần jeans nam form slim fit, chất liệu denim cao cấp, thiết kế hiện đại',
                'short_description' => 'Quần jeans nam slim fit denim cao cấp',
                'cost_price' => 180000,
                'selling_price' => 350000,
                'compare_price' => 450000,
                'stock_quantity' => 80,
                'min_stock_level' => 15,
                'weight' => '600g',
                'dimensions' => '35x45x3cm',
                'category_name' => 'Thời trang nam',
                'supplier_name' => 'Công ty TNHH Thời Trang ABC',
                'is_featured' => false
            ],
            [
                'name' => 'Váy midi nữ',
                'description' => 'Váy midi nữ thiết kế thanh lịch, chất liệu voan mềm mại, phù hợp đi làm và dự tiệc',
                'short_description' => 'Váy midi nữ thanh lịch chất liệu voan',
                'cost_price' => 120000,
                'selling_price' => 250000,
                'compare_price' => 320000,
                'stock_quantity' => 60,
                'min_stock_level' => 10,
                'weight' => '300g',
                'dimensions' => '32x42x2cm',
                'category_name' => 'Thời trang nữ',
                'supplier_name' => 'Công ty TNHH Thời Trang ABC',
                'is_featured' => true
            ],
            [
                'name' => 'Tai nghe Bluetooth',
                'description' => 'Tai nghe Bluetooth 5.0, chất lượng âm thanh Hi-Fi, pin 8 giờ, chống nước IPX5',
                'short_description' => 'Tai nghe Bluetooth 5.0 Hi-Fi',
                'cost_price' => 250000,
                'selling_price' => 450000,
                'compare_price' => 600000,
                'stock_quantity' => 40,
                'min_stock_level' => 8,
                'weight' => '150g',
                'dimensions' => '15x10x5cm',
                'category_name' => 'Điện tử',
                'supplier_name' => 'Nhà cung cấp Điện tử XYZ',
                'is_featured' => true
            ],
            [
                'name' => 'Nồi cơm điện 1.8L',
                'description' => 'Nồi cơm điện 1.8L, lòng nồi chống dính, nấu cơm ngon, tiết kiệm điện',
                'short_description' => 'Nồi cơm điện 1.8L chống dính',
                'cost_price' => 320000,
                'selling_price' => 580000,
                'compare_price' => 750000,
                'stock_quantity' => 25,
                'min_stock_level' => 5,
                'weight' => '2.5kg',
                'dimensions' => '30x30x25cm',
                'category_name' => 'Gia dụng',
                'supplier_name' => 'Gia Dụng Việt',
                'is_featured' => false
            ],
            [
                'name' => 'Kem dưỡng da mặt',
                'description' => 'Kem dưỡng da mặt chống lão hóa, chiết xuất từ thiên nhiên, phù hợp mọi loại da',
                'short_description' => 'Kem dưỡng da mặt chống lão hóa',
                'cost_price' => 80000,
                'selling_price' => 160000,
                'compare_price' => 220000,
                'stock_quantity' => 100,
                'min_stock_level' => 20,
                'weight' => '100g',
                'dimensions' => '8x8x6cm',
                'category_name' => 'Sức khỏe & Làm đẹp',
                'supplier_name' => 'Beauty & Health Store',
                'is_featured' => true
            ],
            [
                'name' => 'Giày thể thao nam',
                'description' => 'Giày thể thao nam thiết kế năng động, đế êm ái, phù hợp chạy bộ và tập gym',
                'short_description' => 'Giày thể thao nam năng động',
                'cost_price' => 280000,
                'selling_price' => 520000,
                'compare_price' => 680000,
                'stock_quantity' => 35,
                'min_stock_level' => 8,
                'weight' => '800g',
                'dimensions' => '35x25x15cm',
                'category_name' => 'Thể thao',
                'supplier_name' => 'Sports World',
                'is_featured' => false
            ],
            [
                'name' => 'Bình sữa cho bé',
                'description' => 'Bình sữa cho bé chất liệu PP an toàn, núm ti silicon mềm, dung tích 240ml',
                'short_description' => 'Bình sữa cho bé PP an toàn 240ml',
                'cost_price' => 35000,
                'selling_price' => 75000,
                'compare_price' => 95000,
                'stock_quantity' => 200,
                'min_stock_level' => 30,
                'weight' => '120g',
                'dimensions' => '8x8x18cm',
                'category_name' => 'Mẹ và bé',
                'supplier_name' => 'Beauty & Health Store',
                'is_featured' => false
            ]
        ];

        foreach ($products as $productData) {
            // Tìm category và supplier
            $category = $categories->where('name', $productData['category_name'])->first();
            $supplier = $suppliers->where('name', $productData['supplier_name'])->first();

            if ($category && $supplier) {
                Product::create([
                    'name' => $productData['name'],
                    'description' => $productData['description'],
                    'short_description' => $productData['short_description'],
                    'cost_price' => $productData['cost_price'],
                    'selling_price' => $productData['selling_price'],
                    'compare_price' => $productData['compare_price'] ?? null,
                    'stock_quantity' => $productData['stock_quantity'],
                    'min_stock_level' => $productData['min_stock_level'],
                    'weight' => $productData['weight'] ?? null,
                    'dimensions' => $productData['dimensions'] ?? null,
                    'category_id' => $category->id,
                    'supplier_id' => $supplier->id,
                    'status' => 'active',
                    'is_featured' => $productData['is_featured'] ?? false,
                    'seo_title' => $productData['seo_title'] ?? null,
                    'seo_description' => $productData['seo_description'] ?? null,
                ]);
            }
        }
    }
}
