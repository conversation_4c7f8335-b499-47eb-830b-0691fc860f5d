<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class CheckoutUnit extends Model
{
    protected $fillable = [
        'name',
        'code',
        'description',
        'checkout_rate',
        'google_sheet_url',
        'contact_person',
        'contact_email',
        'contact_phone',
        'status',
        'settings'
    ];

    protected $casts = [
        'checkout_rate' => 'decimal:2',
        'settings' => 'array'
    ];

    // Tự động tạo code khi tạo checkout unit mới
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($checkoutUnit) {
            if (empty($checkoutUnit->code)) {
                $checkoutUnit->code = strtoupper(Str::slug($checkoutUnit->name, '_'));
            }
        });
    }

    // Relationship với CheckoutOrders
    public function checkoutOrders(): HasMany
    {
        return $this->hasMany(CheckoutOrder::class);
    }

    // Relationship với CheckoutPayments
    public function checkoutPayments(): HasMany
    {
        return $this->hasMany(CheckoutPayment::class);
    }

    // Scope để lấy các checkout unit đang active
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Tính tổng số đơn hàng
    public function getTotalOrdersAttribute()
    {
        return $this->checkoutOrders()->count();
    }

    // Tính tổng số đơn hoàn thành
    public function getCompletedOrdersAttribute()
    {
        return $this->checkoutOrders()->where('status', 'completed')->count();
    }

    // Tính tổng số đơn có sự cố
    public function getProblematicOrdersAttribute()
    {
        return $this->checkoutOrders()->whereIn('status', ['cancelled', 'returned', 'refunded'])->count();
    }

    // Tính tổng tiền checkout
    public function getTotalCheckoutAmountAttribute()
    {
        return $this->checkoutOrders()->sum('total_amount');
    }

    // Tính tổng tiền đã thanh toán
    public function getTotalPaidAmountAttribute()
    {
        return $this->checkoutPayments()->where('status', 'completed')->sum('amount');
    }

    // Tính tổng tiền cần thanh toán
    public function getPendingPaymentAmountAttribute()
    {
        $totalCheckout = $this->total_checkout_amount;
        $totalPaid = $this->total_paid_amount;
        return max(0, $totalCheckout - $totalPaid);
    }

    // Tính tỷ lệ hoàn thành
    public function getCompletionRateAttribute()
    {
        $total = $this->total_orders;
        if ($total == 0) return 0;
        return round(($this->completed_orders / $total) * 100, 2);
    }

    // Tính tỷ lệ sự cố
    public function getProblemRateAttribute()
    {
        $total = $this->total_orders;
        if ($total == 0) return 0;
        return round(($this->problematic_orders / $total) * 100, 2);
    }
}
