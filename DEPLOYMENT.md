# Hướng dẫn triển khai Dropship Manager

## 1. Triển khai trên local (XAMPP)

### Bước 1: Cài đặt XAMPP
- Tải và cài đặt XAMPP từ https://www.apachefriends.org/
- Khởi động Apache và MySQL trong XAMPP Control Panel

### Bước 2: Tạo database
1. Mở phpMyAdmin (http://localhost/phpmyadmin)
2. Tạo database mới tên `dropshipping_db`
3. Chọn Collation: `utf8mb4_unicode_ci`

### Bước 3: Cấu hình Laravel
1. Copy file `.env.example` thành `.env`
2. Cập nhật thông tin database trong `.env`:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=dropshipping_db
DB_USERNAME=root
DB_PASSWORD=
```

### Bước 4: Cài đặt dependencies và chạy migration
```bash
composer install
php artisan key:generate
php artisan migrate
php artisan db:seed
php artisan storage:link
```

### Bước 5: Khởi động server
```bash
php artisan serve
```
Truy cập: http://localhost:8000

## 2. Triển khai trên shared hosting

### Bước 1: Chuẩn bị trên local
1. Chạy migration và seeder trên local để tạo database structure
2. Export database từ phpMyAdmin thành file `.sql`
3. Chạy `composer install --no-dev` để tối ưu cho production

### Bước 2: Upload mã nguồn
1. Nén toàn bộ mã nguồn (trừ thư mục `node_modules` nếu có)
2. Upload vào thư mục `/home/<USER>/laravel_project` trên hosting
3. Copy nội dung thư mục `public` vào `/home/<USER>/public_html`

### Bước 3: Cấu hình public_html/index.php
Chỉnh sửa file `public_html/index.php`:
```php
<?php

use Illuminate\Contracts\Http\Kernel;
use Illuminate\Http\Request;

define('LARAVEL_START', microtime(true));

// Đường dẫn đến Laravel project
require __DIR__.'/../laravel_project/vendor/autoload.php';

$app = require_once __DIR__.'/../laravel_project/bootstrap/app.php';

$kernel = $app->make(Kernel::class);

$response = $kernel->handle(
    $request = Request::capture()
)->send();

$kernel->terminate($request, $response);
```

### Bước 4: Tạo database trên hosting
1. Vào cPanel > MySQL Databases
2. Tạo database mới
3. Tạo user và gán quyền cho database
4. Import file `.sql` vào database qua phpMyAdmin

### Bước 5: Cấu hình .env cho production
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=http://yourdomain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password
```

### Bước 6: Cấu hình quyền thư mục
```bash
chmod -R 755 storage
chmod -R 755 bootstrap/cache
```

## 3. Đồng bộ dữ liệu giữa nhiều máy

### Phương pháp 1: Sử dụng MySQL server tập trung
1. Cài đặt MySQL server trên một máy chủ trung tâm
2. Cấu hình firewall để cho phép kết nối từ xa
3. Tất cả các máy client kết nối đến MySQL server này
4. Cập nhật `.env` trên mỗi máy:
```env
DB_HOST=ip_address_of_central_server
DB_DATABASE=dropshipping_db
DB_USERNAME=remote_user
DB_PASSWORD=remote_password
```

### Phương pháp 2: Sử dụng cloud database
1. Sử dụng dịch vụ như AWS RDS, Google Cloud SQL, hoặc DigitalOcean Database
2. Tạo database instance
3. Cấu hình connection string cho tất cả các máy

## 4. Import dữ liệu từ Google Sheets (CSV)

### Bước 1: Xuất dữ liệu từ Google Sheets
1. Mở Google Sheets
2. File > Download > Comma-separated values (.csv)

### Bước 2: Sử dụng chức năng Import trong hệ thống
1. Truy cập `/import` trong hệ thống
2. Tải template CSV mẫu
3. Điền dữ liệu theo format template
4. Upload file CSV và import

### Cấu trúc file CSV cần thiết:
- `name`: Tên sản phẩm (bắt buộc)
- `cost_price`: Giá gốc (bắt buộc)
- `selling_price`: Giá bán (bắt buộc)
- `category_name`: Tên danh mục (bắt buộc)
- `supplier_name`: Tên nhà cung cấp (bắt buộc)
- Các trường khác: `sku`, `description`, `stock_quantity`, v.v.

## 5. Backup và bảo mật

### Backup database
```bash
# Backup
mysqldump -u username -p dropshipping_db > backup.sql

# Restore
mysql -u username -p dropshipping_db < backup.sql
```

### Bảo mật
1. Đổi mật khẩu database mặc định
2. Sử dụng HTTPS cho production
3. Cập nhật `APP_KEY` cho mỗi environment
4. Giới hạn quyền truy cập database

## 6. Troubleshooting

### Lỗi thường gặp:
1. **500 Internal Server Error**: Kiểm tra quyền thư mục storage và bootstrap/cache
2. **Database connection error**: Kiểm tra thông tin database trong .env
3. **Class not found**: Chạy `composer dump-autoload`
4. **Storage link error**: Chạy `php artisan storage:link`

### Log files:
- Laravel logs: `storage/logs/laravel.log`
- Web server logs: Kiểm tra trong cPanel hoặc server logs

## 7. Tính năng chính của hệ thống

### Dashboard
- Thống kê tổng quan (sản phẩm, đơn hàng, doanh thu)
- Biểu đồ doanh thu theo tháng
- Danh sách đơn hàng gần đây
- Sản phẩm sắp hết hàng

### Quản lý sản phẩm
- CRUD sản phẩm với đầy đủ thông tin
- Upload hình ảnh
- Quản lý tồn kho
- Tính toán profit margin
- SEO optimization

### Import/Export
- Import sản phẩm từ CSV
- Template CSV mẫu
- Xử lý lỗi và báo cáo chi tiết
- Tự động tạo category và supplier nếu chưa tồn tại

### Quản lý đơn hàng
- Theo dõi trạng thái đơn hàng
- Quản lý thanh toán
- Tính toán chi phí vận chuyển
- Báo cáo doanh thu

### Nhà cung cấp
- Quản lý thông tin nhà cung cấp
- Tỷ lệ hoa hồng
- Thống kê hiệu suất

Hệ thống được thiết kế để hỗ trợ dropshipping với khả năng mở rộng và đồng bộ dữ liệu giữa nhiều máy tính.
